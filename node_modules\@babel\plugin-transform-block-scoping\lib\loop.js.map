{"version": 3, "names": ["_core", "require", "collectLoopBodyBindingsVisitor", "Expression|Declaration|Loop", "path", "skip", "<PERSON><PERSON>", "state", "isFunctionParent", "bindings", "scope", "name", "Object", "keys", "binding", "kind", "blockScoped", "push", "getLoopBodyBindings", "loopPath", "traverse", "getUsageInBody", "seen", "WeakSet", "capturedInClosure", "constantViolations", "filterMap", "inBody", "inClosure", "relativeLoopLocation", "id", "isUpdateExpression", "get", "isAssignmentExpression", "add", "node", "references", "referencePaths", "has", "hasConstantViolations", "length", "usages", "concat", "bodyPath", "currPath", "parentPath", "isFunction", "isClass", "Error", "collectCompletionsAndVarsVisitor", "Function", "LabeledStatement", "enter", "labelsStack", "label", "exit", "popped", "pop", "Loop", "_", "labellessContinueTargets", "labellessBreakTargets", "SwitchStatement", "BreakStatement|ContinueStatement", "includes", "isBreakStatement", "breaks<PERSON><PERSON><PERSON><PERSON>", "ReturnStatement", "returns", "VariableDeclaration", "parent", "loopNode", "isVarInLoopHead", "vars", "wrapLoopBody", "captured", "updatedBindingsUsages", "callArgs", "closureParams", "updater", "updatedUsage", "t", "identifier", "innerName", "generateUid", "assignmentExpression", "replaceWith", "fn", "functionExpression", "toBlock", "body", "call", "callExpression", "fnParent", "findParent", "p", "async", "generator", "yieldExpression", "awaitExpression", "updaterNode", "expressionStatement", "sequenceExpression", "<PERSON><PERSON><PERSON>", "insertBefore", "variableDeclaration", "variableDeclarator", "bodyStmts", "varNames", "assign", "decl", "declarations", "getBindingIdentifiers", "init", "replacement", "isForStatement", "isForXStatement", "left", "remove", "map", "completionId", "injected", "Set", "type", "returnStatement", "stringLiteral", "cloneNode", "template", "statement", "ast", "arg", "argument", "buildUndefinedNode", "blockStatement", "key", "list", "result", "item", "mapped"], "sources": ["../src/loop.ts"], "sourcesContent": ["import { template, types as t } from \"@babel/core\";\nimport type { NodePath, Visitor, Binding } from \"@babel/traverse\";\n\ninterface LoopBodyBindingsState {\n  blockScoped: Binding[];\n}\n\nconst collectLoopBodyBindingsVisitor: Visitor<LoopBodyBindingsState> = {\n  \"Expression|Declaration|Loop\"(path) {\n    path.skip();\n  },\n  Scope(path, state) {\n    if (path.isFunctionParent()) path.skip();\n\n    const { bindings } = path.scope;\n    for (const name of Object.keys(bindings)) {\n      const binding = bindings[name];\n      if (\n        binding.kind === \"let\" ||\n        binding.kind === \"const\" ||\n        binding.kind === \"hoisted\"\n      ) {\n        state.blockScoped.push(binding);\n      }\n    }\n  },\n};\n\nexport function getLoopBodyBindings(loopPath: NodePath<t.Loop>) {\n  const state: LoopBodyBindingsState = { blockScoped: [] };\n  loopPath.traverse(collectLoopBodyBindingsVisitor, state);\n  return state.blockScoped;\n}\n\nexport function getUsageInBody(binding: Binding, loopPath: NodePath<t.Loop>) {\n  // UpdateExpressions are counted both as a reference and a mutation,\n  // so we need to de-duplicate them.\n  const seen = new WeakSet<t.Node>();\n\n  let capturedInClosure = false;\n\n  const constantViolations = filterMap(binding.constantViolations, path => {\n    const { inBody, inClosure } = relativeLoopLocation(path, loopPath);\n    if (!inBody) return null;\n    capturedInClosure ||= inClosure;\n\n    const id = path.isUpdateExpression()\n      ? path.get(\"argument\")\n      : path.isAssignmentExpression()\n      ? path.get(\"left\")\n      : null;\n    if (id) seen.add(id.node);\n    return id as NodePath<t.Identifier> | null;\n  });\n\n  const references = filterMap(binding.referencePaths, path => {\n    if (seen.has(path.node)) return null;\n\n    const { inBody, inClosure } = relativeLoopLocation(path, loopPath);\n    if (!inBody) return null;\n    capturedInClosure ||= inClosure;\n\n    return path as NodePath<t.Identifier>;\n  });\n\n  return {\n    capturedInClosure,\n    hasConstantViolations: constantViolations.length > 0,\n    usages: references.concat(constantViolations),\n  };\n}\n\nfunction relativeLoopLocation(path: NodePath, loopPath: NodePath<t.Loop>) {\n  const bodyPath = loopPath.get(\"body\");\n  let inClosure = false;\n\n  for (let currPath = path; currPath; currPath = currPath.parentPath) {\n    if (currPath.isFunction() || currPath.isClass()) inClosure = true;\n    if (currPath === bodyPath) {\n      return { inBody: true, inClosure };\n    } else if (currPath === loopPath) {\n      return { inBody: false, inClosure };\n    }\n  }\n\n  throw new Error(\n    \"Internal Babel error: path is not in loop. Please report this as a bug.\",\n  );\n}\n\ninterface CompletionsAndVarsState {\n  breaksContinues: NodePath<t.BreakStatement | t.ContinueStatement>[];\n  returns: NodePath<t.ReturnStatement>[];\n  labelsStack: string[];\n  labellessContinueTargets: number;\n  labellessBreakTargets: number;\n\n  vars: NodePath<t.VariableDeclaration>[];\n  loopNode: t.Loop;\n}\n\nconst collectCompletionsAndVarsVisitor: Visitor<CompletionsAndVarsState> = {\n  Function(path) {\n    path.skip();\n  },\n  LabeledStatement: {\n    enter({ node }, state) {\n      state.labelsStack.push(node.label.name);\n    },\n    exit({ node }, state) {\n      const popped = state.labelsStack.pop();\n      if (popped !== node.label.name) {\n        throw new Error(\"Assertion failure. Please report this bug to Babel.\");\n      }\n    },\n  },\n  Loop: {\n    enter(_, state) {\n      state.labellessContinueTargets++;\n      state.labellessBreakTargets++;\n    },\n    exit(_, state) {\n      state.labellessContinueTargets--;\n      state.labellessBreakTargets--;\n    },\n  },\n  SwitchStatement: {\n    enter(_, state) {\n      state.labellessBreakTargets++;\n    },\n    exit(_, state) {\n      state.labellessBreakTargets--;\n    },\n  },\n  \"BreakStatement|ContinueStatement\"(\n    path: NodePath<t.BreakStatement | t.ContinueStatement>,\n    state,\n  ) {\n    const { label } = path.node;\n    if (label) {\n      if (state.labelsStack.includes(label.name)) return;\n    } else if (\n      path.isBreakStatement()\n        ? state.labellessBreakTargets > 0\n        : state.labellessContinueTargets > 0\n    ) {\n      return;\n    }\n    state.breaksContinues.push(path);\n  },\n  ReturnStatement(path, state) {\n    state.returns.push(path);\n  },\n  VariableDeclaration(path, state) {\n    if (path.parent === state.loopNode && isVarInLoopHead(path)) return;\n    if (path.node.kind === \"var\") state.vars.push(path);\n  },\n};\n\nexport function wrapLoopBody(\n  loopPath: NodePath<t.Loop>,\n  captured: string[],\n  updatedBindingsUsages: Map<string, NodePath<t.Identifier>[]>,\n) {\n  const loopNode = loopPath.node;\n  const state: CompletionsAndVarsState = {\n    breaksContinues: [],\n    returns: [],\n    labelsStack: [],\n    labellessBreakTargets: 0,\n    labellessContinueTargets: 0,\n    vars: [],\n    loopNode,\n  };\n  loopPath.traverse(collectCompletionsAndVarsVisitor, state);\n\n  const callArgs = [];\n  const closureParams = [];\n  const updater = [];\n  for (const [name, updatedUsage] of updatedBindingsUsages) {\n    callArgs.push(t.identifier(name));\n\n    const innerName = loopPath.scope.generateUid(name);\n    closureParams.push(t.identifier(innerName));\n    updater.push(\n      t.assignmentExpression(\"=\", t.identifier(name), t.identifier(innerName)),\n    );\n    for (const path of updatedUsage) path.replaceWith(t.identifier(innerName));\n  }\n  for (const name of captured) {\n    if (updatedBindingsUsages.has(name)) continue; // already injected\n    callArgs.push(t.identifier(name));\n    closureParams.push(t.identifier(name));\n  }\n\n  const id = loopPath.scope.generateUid(\"loop\");\n  const fn = t.functionExpression(\n    null,\n    closureParams,\n    t.toBlock(loopNode.body),\n  );\n  let call: t.Expression = t.callExpression(t.identifier(id), callArgs);\n\n  const fnParent = loopPath.findParent(p => p.isFunction());\n  if (fnParent) {\n    const { async, generator } = fnParent.node as t.Function;\n    fn.async = async;\n    fn.generator = generator;\n    if (generator) call = t.yieldExpression(call, true);\n    else if (async) call = t.awaitExpression(call);\n  }\n\n  const updaterNode =\n    updater.length > 0\n      ? t.expressionStatement(t.sequenceExpression(updater))\n      : null;\n  if (updaterNode) fn.body.body.push(updaterNode);\n\n  // NOTE: Calling .insertBefore on the loop path might cause the\n  // loop to be moved in the AST. For example, in\n  //   if (true) for (let x of y) ...\n  // .insertBefore will replace the loop with a block:\n  //   if (true) { var _loop = ...; for (let x of y) ... }\n  // All subsequent operations in this function on the loop node\n  // must not assume that loopPath still represents the loop.\n  // TODO: Consider using a function declaration\n  const [varPath] = loopPath.insertBefore(\n    t.variableDeclaration(\"var\", [t.variableDeclarator(t.identifier(id), fn)]),\n  ) as [NodePath<t.VariableDeclaration>];\n\n  const bodyStmts: t.Statement[] = [];\n\n  const varNames: string[] = [];\n  for (const varPath of state.vars) {\n    const assign = [];\n    for (const decl of varPath.node.declarations) {\n      varNames.push(...Object.keys(t.getBindingIdentifiers(decl.id)));\n      if (decl.init) {\n        assign.push(t.assignmentExpression(\"=\", decl.id, decl.init));\n      }\n    }\n    if (assign.length > 0) {\n      let replacement: t.Node =\n        assign.length === 1 ? assign[0] : t.sequenceExpression(assign);\n      if (\n        !t.isForStatement(varPath.parent, { init: varPath.node }) &&\n        !t.isForXStatement(varPath.parent, { left: varPath.node })\n      ) {\n        replacement = t.expressionStatement(replacement);\n      }\n      varPath.replaceWith(replacement);\n    } else {\n      varPath.remove();\n    }\n  }\n  if (varNames.length) {\n    bodyStmts.push(\n      t.variableDeclaration(\n        \"var\",\n        varNames.map(name => t.variableDeclarator(t.identifier(name))),\n      ),\n    );\n  }\n\n  if (state.breaksContinues.length === 0 && state.returns.length === 0) {\n    bodyStmts.push(t.expressionStatement(call));\n  } else {\n    const completionId = loopPath.scope.generateUid(\"ret\");\n    bodyStmts.push(\n      t.variableDeclaration(\"var\", [\n        t.variableDeclarator(t.identifier(completionId), call),\n      ]),\n    );\n\n    const injected = new Set<string>();\n    for (const path of state.breaksContinues) {\n      const { node } = path;\n      const { type, label } = node;\n      let name = type === \"BreakStatement\" ? \"break\" : \"continue\";\n      if (label) name += \"|\" + label.name;\n      path.replaceWith(t.returnStatement(t.stringLiteral(name)));\n      if (updaterNode) path.insertBefore(t.cloneNode(updaterNode));\n\n      if (injected.has(name)) continue;\n      injected.add(name);\n\n      bodyStmts.push(\n        template.statement.ast`\n        if (\n          ${t.identifier(completionId)} === ${t.stringLiteral(name)}\n        ) ${node}\n      `,\n      );\n    }\n    if (state.returns.length) {\n      for (const path of state.returns) {\n        const arg = path.node.argument || path.scope.buildUndefinedNode();\n        path.replaceWith(\n          template.statement.ast`\n          return { v: ${arg} };\n        `,\n        );\n      }\n\n      bodyStmts.push(\n        template.statement.ast`\n        if (typeof ${t.identifier(completionId)} === \"object\")\n          return ${t.identifier(completionId)}.v;\n      `,\n      );\n    }\n  }\n\n  loopNode.body = t.blockStatement(bodyStmts);\n\n  return varPath;\n}\n\nexport function isVarInLoopHead(path: NodePath<t.VariableDeclaration>) {\n  if (t.isForStatement(path.parent)) return path.key === \"init\";\n  if (t.isForXStatement(path.parent)) return path.key === \"left\";\n  return false;\n}\n\nfunction filterMap<T, U extends object>(list: T[], fn: (item: T) => U | null) {\n  const result: U[] = [];\n  for (const item of list) {\n    const mapped = fn(item);\n    if (mapped) result.push(mapped);\n  }\n  return result;\n}\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAOA,MAAMC,8BAA8D,GAAG;EACrE,6BAA6BC,CAACC,IAAI,EAAE;IAClCA,IAAI,CAACC,IAAI,CAAC,CAAC;EACb,CAAC;EACDC,KAAKA,CAACF,IAAI,EAAEG,KAAK,EAAE;IACjB,IAAIH,IAAI,CAACI,gBAAgB,CAAC,CAAC,EAAEJ,IAAI,CAACC,IAAI,CAAC,CAAC;IAExC,MAAM;MAAEI;IAAS,CAAC,GAAGL,IAAI,CAACM,KAAK;IAC/B,KAAK,MAAMC,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAAC,EAAE;MACxC,MAAMK,OAAO,GAAGL,QAAQ,CAACE,IAAI,CAAC;MAC9B,IACEG,OAAO,CAACC,IAAI,KAAK,KAAK,IACtBD,OAAO,CAACC,IAAI,KAAK,OAAO,IACxBD,OAAO,CAACC,IAAI,KAAK,SAAS,EAC1B;QACAR,KAAK,CAACS,WAAW,CAACC,IAAI,CAACH,OAAO,CAAC;MACjC;IACF;EACF;AACF,CAAC;AAEM,SAASI,mBAAmBA,CAACC,QAA0B,EAAE;EAC9D,MAAMZ,KAA4B,GAAG;IAAES,WAAW,EAAE;EAAG,CAAC;EACxDG,QAAQ,CAACC,QAAQ,CAAClB,8BAA8B,EAAEK,KAAK,CAAC;EACxD,OAAOA,KAAK,CAACS,WAAW;AAC1B;AAEO,SAASK,cAAcA,CAACP,OAAgB,EAAEK,QAA0B,EAAE;EAG3E,MAAMG,IAAI,GAAG,IAAIC,OAAO,CAAS,CAAC;EAElC,IAAIC,iBAAiB,GAAG,KAAK;EAE7B,MAAMC,kBAAkB,GAAGC,SAAS,CAACZ,OAAO,CAACW,kBAAkB,EAAErB,IAAI,IAAI;IACvE,MAAM;MAAEuB,MAAM;MAAEC;IAAU,CAAC,GAAGC,oBAAoB,CAACzB,IAAI,EAAEe,QAAQ,CAAC;IAClE,IAAI,CAACQ,MAAM,EAAE,OAAO,IAAI;IACxBH,iBAAiB,KAAjBA,iBAAiB,GAAKI,SAAS;IAE/B,MAAME,EAAE,GAAG1B,IAAI,CAAC2B,kBAAkB,CAAC,CAAC,GAChC3B,IAAI,CAAC4B,GAAG,CAAC,UAAU,CAAC,GACpB5B,IAAI,CAAC6B,sBAAsB,CAAC,CAAC,GAC7B7B,IAAI,CAAC4B,GAAG,CAAC,MAAM,CAAC,GAChB,IAAI;IACR,IAAIF,EAAE,EAAER,IAAI,CAACY,GAAG,CAACJ,EAAE,CAACK,IAAI,CAAC;IACzB,OAAOL,EAAE;EACX,CAAC,CAAC;EAEF,MAAMM,UAAU,GAAGV,SAAS,CAACZ,OAAO,CAACuB,cAAc,EAAEjC,IAAI,IAAI;IAC3D,IAAIkB,IAAI,CAACgB,GAAG,CAAClC,IAAI,CAAC+B,IAAI,CAAC,EAAE,OAAO,IAAI;IAEpC,MAAM;MAAER,MAAM;MAAEC;IAAU,CAAC,GAAGC,oBAAoB,CAACzB,IAAI,EAAEe,QAAQ,CAAC;IAClE,IAAI,CAACQ,MAAM,EAAE,OAAO,IAAI;IACxBH,iBAAiB,KAAjBA,iBAAiB,GAAKI,SAAS;IAE/B,OAAOxB,IAAI;EACb,CAAC,CAAC;EAEF,OAAO;IACLoB,iBAAiB;IACjBe,qBAAqB,EAAEd,kBAAkB,CAACe,MAAM,GAAG,CAAC;IACpDC,MAAM,EAAEL,UAAU,CAACM,MAAM,CAACjB,kBAAkB;EAC9C,CAAC;AACH;AAEA,SAASI,oBAAoBA,CAACzB,IAAc,EAAEe,QAA0B,EAAE;EACxE,MAAMwB,QAAQ,GAAGxB,QAAQ,CAACa,GAAG,CAAC,MAAM,CAAC;EACrC,IAAIJ,SAAS,GAAG,KAAK;EAErB,KAAK,IAAIgB,QAAQ,GAAGxC,IAAI,EAAEwC,QAAQ,EAAEA,QAAQ,GAAGA,QAAQ,CAACC,UAAU,EAAE;IAClE,IAAID,QAAQ,CAACE,UAAU,CAAC,CAAC,IAAIF,QAAQ,CAACG,OAAO,CAAC,CAAC,EAAEnB,SAAS,GAAG,IAAI;IACjE,IAAIgB,QAAQ,KAAKD,QAAQ,EAAE;MACzB,OAAO;QAAEhB,MAAM,EAAE,IAAI;QAAEC;MAAU,CAAC;IACpC,CAAC,MAAM,IAAIgB,QAAQ,KAAKzB,QAAQ,EAAE;MAChC,OAAO;QAAEQ,MAAM,EAAE,KAAK;QAAEC;MAAU,CAAC;IACrC;EACF;EAEA,MAAM,IAAIoB,KAAK,CACb,yEACF,CAAC;AACH;AAaA,MAAMC,gCAAkE,GAAG;EACzEC,QAAQA,CAAC9C,IAAI,EAAE;IACbA,IAAI,CAACC,IAAI,CAAC,CAAC;EACb,CAAC;EACD8C,gBAAgB,EAAE;IAChBC,KAAKA,CAAC;MAAEjB;IAAK,CAAC,EAAE5B,KAAK,EAAE;MACrBA,KAAK,CAAC8C,WAAW,CAACpC,IAAI,CAACkB,IAAI,CAACmB,KAAK,CAAC3C,IAAI,CAAC;IACzC,CAAC;IACD4C,IAAIA,CAAC;MAAEpB;IAAK,CAAC,EAAE5B,KAAK,EAAE;MACpB,MAAMiD,MAAM,GAAGjD,KAAK,CAAC8C,WAAW,CAACI,GAAG,CAAC,CAAC;MACtC,IAAID,MAAM,KAAKrB,IAAI,CAACmB,KAAK,CAAC3C,IAAI,EAAE;QAC9B,MAAM,IAAIqC,KAAK,CAAC,qDAAqD,CAAC;MACxE;IACF;EACF,CAAC;EACDU,IAAI,EAAE;IACJN,KAAKA,CAACO,CAAC,EAAEpD,KAAK,EAAE;MACdA,KAAK,CAACqD,wBAAwB,EAAE;MAChCrD,KAAK,CAACsD,qBAAqB,EAAE;IAC/B,CAAC;IACDN,IAAIA,CAACI,CAAC,EAAEpD,KAAK,EAAE;MACbA,KAAK,CAACqD,wBAAwB,EAAE;MAChCrD,KAAK,CAACsD,qBAAqB,EAAE;IAC/B;EACF,CAAC;EACDC,eAAe,EAAE;IACfV,KAAKA,CAACO,CAAC,EAAEpD,KAAK,EAAE;MACdA,KAAK,CAACsD,qBAAqB,EAAE;IAC/B,CAAC;IACDN,IAAIA,CAACI,CAAC,EAAEpD,KAAK,EAAE;MACbA,KAAK,CAACsD,qBAAqB,EAAE;IAC/B;EACF,CAAC;EACD,kCAAkCE,CAChC3D,IAAsD,EACtDG,KAAK,EACL;IACA,MAAM;MAAE+C;IAAM,CAAC,GAAGlD,IAAI,CAAC+B,IAAI;IAC3B,IAAImB,KAAK,EAAE;MACT,IAAI/C,KAAK,CAAC8C,WAAW,CAACW,QAAQ,CAACV,KAAK,CAAC3C,IAAI,CAAC,EAAE;IAC9C,CAAC,MAAM,IACLP,IAAI,CAAC6D,gBAAgB,CAAC,CAAC,GACnB1D,KAAK,CAACsD,qBAAqB,GAAG,CAAC,GAC/BtD,KAAK,CAACqD,wBAAwB,GAAG,CAAC,EACtC;MACA;IACF;IACArD,KAAK,CAAC2D,eAAe,CAACjD,IAAI,CAACb,IAAI,CAAC;EAClC,CAAC;EACD+D,eAAeA,CAAC/D,IAAI,EAAEG,KAAK,EAAE;IAC3BA,KAAK,CAAC6D,OAAO,CAACnD,IAAI,CAACb,IAAI,CAAC;EAC1B,CAAC;EACDiE,mBAAmBA,CAACjE,IAAI,EAAEG,KAAK,EAAE;IAC/B,IAAIH,IAAI,CAACkE,MAAM,KAAK/D,KAAK,CAACgE,QAAQ,IAAIC,eAAe,CAACpE,IAAI,CAAC,EAAE;IAC7D,IAAIA,IAAI,CAAC+B,IAAI,CAACpB,IAAI,KAAK,KAAK,EAAER,KAAK,CAACkE,IAAI,CAACxD,IAAI,CAACb,IAAI,CAAC;EACrD;AACF,CAAC;AAEM,SAASsE,YAAYA,CAC1BvD,QAA0B,EAC1BwD,QAAkB,EAClBC,qBAA4D,EAC5D;EACA,MAAML,QAAQ,GAAGpD,QAAQ,CAACgB,IAAI;EAC9B,MAAM5B,KAA8B,GAAG;IACrC2D,eAAe,EAAE,EAAE;IACnBE,OAAO,EAAE,EAAE;IACXf,WAAW,EAAE,EAAE;IACfQ,qBAAqB,EAAE,CAAC;IACxBD,wBAAwB,EAAE,CAAC;IAC3Ba,IAAI,EAAE,EAAE;IACRF;EACF,CAAC;EACDpD,QAAQ,CAACC,QAAQ,CAAC6B,gCAAgC,EAAE1C,KAAK,CAAC;EAE1D,MAAMsE,QAAQ,GAAG,EAAE;EACnB,MAAMC,aAAa,GAAG,EAAE;EACxB,MAAMC,OAAO,GAAG,EAAE;EAClB,KAAK,MAAM,CAACpE,IAAI,EAAEqE,YAAY,CAAC,IAAIJ,qBAAqB,EAAE;IACxDC,QAAQ,CAAC5D,IAAI,CAACgE,WAAC,CAACC,UAAU,CAACvE,IAAI,CAAC,CAAC;IAEjC,MAAMwE,SAAS,GAAGhE,QAAQ,CAACT,KAAK,CAAC0E,WAAW,CAACzE,IAAI,CAAC;IAClDmE,aAAa,CAAC7D,IAAI,CAACgE,WAAC,CAACC,UAAU,CAACC,SAAS,CAAC,CAAC;IAC3CJ,OAAO,CAAC9D,IAAI,CACVgE,WAAC,CAACI,oBAAoB,CAAC,GAAG,EAAEJ,WAAC,CAACC,UAAU,CAACvE,IAAI,CAAC,EAAEsE,WAAC,CAACC,UAAU,CAACC,SAAS,CAAC,CACzE,CAAC;IACD,KAAK,MAAM/E,IAAI,IAAI4E,YAAY,EAAE5E,IAAI,CAACkF,WAAW,CAACL,WAAC,CAACC,UAAU,CAACC,SAAS,CAAC,CAAC;EAC5E;EACA,KAAK,MAAMxE,IAAI,IAAIgE,QAAQ,EAAE;IAC3B,IAAIC,qBAAqB,CAACtC,GAAG,CAAC3B,IAAI,CAAC,EAAE;IACrCkE,QAAQ,CAAC5D,IAAI,CAACgE,WAAC,CAACC,UAAU,CAACvE,IAAI,CAAC,CAAC;IACjCmE,aAAa,CAAC7D,IAAI,CAACgE,WAAC,CAACC,UAAU,CAACvE,IAAI,CAAC,CAAC;EACxC;EAEA,MAAMmB,EAAE,GAAGX,QAAQ,CAACT,KAAK,CAAC0E,WAAW,CAAC,MAAM,CAAC;EAC7C,MAAMG,EAAE,GAAGN,WAAC,CAACO,kBAAkB,CAC7B,IAAI,EACJV,aAAa,EACbG,WAAC,CAACQ,OAAO,CAAClB,QAAQ,CAACmB,IAAI,CACzB,CAAC;EACD,IAAIC,IAAkB,GAAGV,WAAC,CAACW,cAAc,CAACX,WAAC,CAACC,UAAU,CAACpD,EAAE,CAAC,EAAE+C,QAAQ,CAAC;EAErE,MAAMgB,QAAQ,GAAG1E,QAAQ,CAAC2E,UAAU,CAACC,CAAC,IAAIA,CAAC,CAACjD,UAAU,CAAC,CAAC,CAAC;EACzD,IAAI+C,QAAQ,EAAE;IACZ,MAAM;MAAEG,KAAK;MAAEC;IAAU,CAAC,GAAGJ,QAAQ,CAAC1D,IAAkB;IACxDoD,EAAE,CAACS,KAAK,GAAGA,KAAK;IAChBT,EAAE,CAACU,SAAS,GAAGA,SAAS;IACxB,IAAIA,SAAS,EAAEN,IAAI,GAAGV,WAAC,CAACiB,eAAe,CAACP,IAAI,EAAE,IAAI,CAAC,CAAC,KAC/C,IAAIK,KAAK,EAAEL,IAAI,GAAGV,WAAC,CAACkB,eAAe,CAACR,IAAI,CAAC;EAChD;EAEA,MAAMS,WAAW,GACfrB,OAAO,CAACvC,MAAM,GAAG,CAAC,GACdyC,WAAC,CAACoB,mBAAmB,CAACpB,WAAC,CAACqB,kBAAkB,CAACvB,OAAO,CAAC,CAAC,GACpD,IAAI;EACV,IAAIqB,WAAW,EAAEb,EAAE,CAACG,IAAI,CAACA,IAAI,CAACzE,IAAI,CAACmF,WAAW,CAAC;EAU/C,MAAM,CAACG,OAAO,CAAC,GAAGpF,QAAQ,CAACqF,YAAY,CACrCvB,WAAC,CAACwB,mBAAmB,CAAC,KAAK,EAAE,CAACxB,WAAC,CAACyB,kBAAkB,CAACzB,WAAC,CAACC,UAAU,CAACpD,EAAE,CAAC,EAAEyD,EAAE,CAAC,CAAC,CAC3E,CAAsC;EAEtC,MAAMoB,SAAwB,GAAG,EAAE;EAEnC,MAAMC,QAAkB,GAAG,EAAE;EAC7B,KAAK,MAAML,OAAO,IAAIhG,KAAK,CAACkE,IAAI,EAAE;IAChC,MAAMoC,MAAM,GAAG,EAAE;IACjB,KAAK,MAAMC,IAAI,IAAIP,OAAO,CAACpE,IAAI,CAAC4E,YAAY,EAAE;MAC5CH,QAAQ,CAAC3F,IAAI,CAAC,GAAGL,MAAM,CAACC,IAAI,CAACoE,WAAC,CAAC+B,qBAAqB,CAACF,IAAI,CAAChF,EAAE,CAAC,CAAC,CAAC;MAC/D,IAAIgF,IAAI,CAACG,IAAI,EAAE;QACbJ,MAAM,CAAC5F,IAAI,CAACgE,WAAC,CAACI,oBAAoB,CAAC,GAAG,EAAEyB,IAAI,CAAChF,EAAE,EAAEgF,IAAI,CAACG,IAAI,CAAC,CAAC;MAC9D;IACF;IACA,IAAIJ,MAAM,CAACrE,MAAM,GAAG,CAAC,EAAE;MACrB,IAAI0E,WAAmB,GACrBL,MAAM,CAACrE,MAAM,KAAK,CAAC,GAAGqE,MAAM,CAAC,CAAC,CAAC,GAAG5B,WAAC,CAACqB,kBAAkB,CAACO,MAAM,CAAC;MAChE,IACE,CAAC5B,WAAC,CAACkC,cAAc,CAACZ,OAAO,CAACjC,MAAM,EAAE;QAAE2C,IAAI,EAAEV,OAAO,CAACpE;MAAK,CAAC,CAAC,IACzD,CAAC8C,WAAC,CAACmC,eAAe,CAACb,OAAO,CAACjC,MAAM,EAAE;QAAE+C,IAAI,EAAEd,OAAO,CAACpE;MAAK,CAAC,CAAC,EAC1D;QACA+E,WAAW,GAAGjC,WAAC,CAACoB,mBAAmB,CAACa,WAAW,CAAC;MAClD;MACAX,OAAO,CAACjB,WAAW,CAAC4B,WAAW,CAAC;IAClC,CAAC,MAAM;MACLX,OAAO,CAACe,MAAM,CAAC,CAAC;IAClB;EACF;EACA,IAAIV,QAAQ,CAACpE,MAAM,EAAE;IACnBmE,SAAS,CAAC1F,IAAI,CACZgE,WAAC,CAACwB,mBAAmB,CACnB,KAAK,EACLG,QAAQ,CAACW,GAAG,CAAC5G,IAAI,IAAIsE,WAAC,CAACyB,kBAAkB,CAACzB,WAAC,CAACC,UAAU,CAACvE,IAAI,CAAC,CAAC,CAC/D,CACF,CAAC;EACH;EAEA,IAAIJ,KAAK,CAAC2D,eAAe,CAAC1B,MAAM,KAAK,CAAC,IAAIjC,KAAK,CAAC6D,OAAO,CAAC5B,MAAM,KAAK,CAAC,EAAE;IACpEmE,SAAS,CAAC1F,IAAI,CAACgE,WAAC,CAACoB,mBAAmB,CAACV,IAAI,CAAC,CAAC;EAC7C,CAAC,MAAM;IACL,MAAM6B,YAAY,GAAGrG,QAAQ,CAACT,KAAK,CAAC0E,WAAW,CAAC,KAAK,CAAC;IACtDuB,SAAS,CAAC1F,IAAI,CACZgE,WAAC,CAACwB,mBAAmB,CAAC,KAAK,EAAE,CAC3BxB,WAAC,CAACyB,kBAAkB,CAACzB,WAAC,CAACC,UAAU,CAACsC,YAAY,CAAC,EAAE7B,IAAI,CAAC,CACvD,CACH,CAAC;IAED,MAAM8B,QAAQ,GAAG,IAAIC,GAAG,CAAS,CAAC;IAClC,KAAK,MAAMtH,IAAI,IAAIG,KAAK,CAAC2D,eAAe,EAAE;MACxC,MAAM;QAAE/B;MAAK,CAAC,GAAG/B,IAAI;MACrB,MAAM;QAAEuH,IAAI;QAAErE;MAAM,CAAC,GAAGnB,IAAI;MAC5B,IAAIxB,IAAI,GAAGgH,IAAI,KAAK,gBAAgB,GAAG,OAAO,GAAG,UAAU;MAC3D,IAAIrE,KAAK,EAAE3C,IAAI,IAAI,GAAG,GAAG2C,KAAK,CAAC3C,IAAI;MACnCP,IAAI,CAACkF,WAAW,CAACL,WAAC,CAAC2C,eAAe,CAAC3C,WAAC,CAAC4C,aAAa,CAAClH,IAAI,CAAC,CAAC,CAAC;MAC1D,IAAIyF,WAAW,EAAEhG,IAAI,CAACoG,YAAY,CAACvB,WAAC,CAAC6C,SAAS,CAAC1B,WAAW,CAAC,CAAC;MAE5D,IAAIqB,QAAQ,CAACnF,GAAG,CAAC3B,IAAI,CAAC,EAAE;MACxB8G,QAAQ,CAACvF,GAAG,CAACvB,IAAI,CAAC;MAElBgG,SAAS,CAAC1F,IAAI,CACZ8G,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC/B;AACA,YAAYhD,WAAC,CAACC,UAAU,CAACsC,YAAY,CAAE,QAAOvC,WAAC,CAAC4C,aAAa,CAAClH,IAAI,CAAE;AACpE,YAAYwB,IAAK;AACjB,OACM,CAAC;IACH;IACA,IAAI5B,KAAK,CAAC6D,OAAO,CAAC5B,MAAM,EAAE;MACxB,KAAK,MAAMpC,IAAI,IAAIG,KAAK,CAAC6D,OAAO,EAAE;QAChC,MAAM8D,GAAG,GAAG9H,IAAI,CAAC+B,IAAI,CAACgG,QAAQ,IAAI/H,IAAI,CAACM,KAAK,CAAC0H,kBAAkB,CAAC,CAAC;QACjEhI,IAAI,CAACkF,WAAW,CACdyC,cAAQ,CAACC,SAAS,CAACC,GAAI;AACjC,wBAAwBC,GAAI;AAC5B,SACQ,CAAC;MACH;MAEAvB,SAAS,CAAC1F,IAAI,CACZ8G,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC/B,qBAAqBhD,WAAC,CAACC,UAAU,CAACsC,YAAY,CAAE;AAChD,mBAAmBvC,WAAC,CAACC,UAAU,CAACsC,YAAY,CAAE;AAC9C,OACM,CAAC;IACH;EACF;EAEAjD,QAAQ,CAACmB,IAAI,GAAGT,WAAC,CAACoD,cAAc,CAAC1B,SAAS,CAAC;EAE3C,OAAOJ,OAAO;AAChB;AAEO,SAAS/B,eAAeA,CAACpE,IAAqC,EAAE;EACrE,IAAI6E,WAAC,CAACkC,cAAc,CAAC/G,IAAI,CAACkE,MAAM,CAAC,EAAE,OAAOlE,IAAI,CAACkI,GAAG,KAAK,MAAM;EAC7D,IAAIrD,WAAC,CAACmC,eAAe,CAAChH,IAAI,CAACkE,MAAM,CAAC,EAAE,OAAOlE,IAAI,CAACkI,GAAG,KAAK,MAAM;EAC9D,OAAO,KAAK;AACd;AAEA,SAAS5G,SAASA,CAAsB6G,IAAS,EAAEhD,EAAyB,EAAE;EAC5E,MAAMiD,MAAW,GAAG,EAAE;EACtB,KAAK,MAAMC,IAAI,IAAIF,IAAI,EAAE;IACvB,MAAMG,MAAM,GAAGnD,EAAE,CAACkD,IAAI,CAAC;IACvB,IAAIC,MAAM,EAAEF,MAAM,CAACvH,IAAI,CAACyH,MAAM,CAAC;EACjC;EACA,OAAOF,MAAM;AACf"}