{"_args": [["@nodelib/fs.stat@1.1.3", "/var/www/html"]], "_development": true, "_from": "@nodelib/fs.stat@1.1.3", "_id": "@nodelib/fs.stat@1.1.3", "_inBundle": false, "_integrity": "sha512-shAmDyaQC4H92APFoIaVDHCx5bStIocgvbwQyxPRrbUY20V1EYTbSDchWbuwlMG3V17cprZhA6+78JfB+3DTPw==", "_location": "/@nodelib/fs.stat", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@nodelib/fs.stat@1.1.3", "name": "@nodelib/fs.stat", "escapedName": "@nodelib%2ffs.stat", "scope": "@nodelib", "rawSpec": "1.1.3", "saveSpec": null, "fetchSpec": "1.1.3"}, "_requiredBy": ["/fast-glob"], "_resolved": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-1.1.3.tgz", "_spec": "1.1.3", "_where": "/var/www/html", "description": "Get the status of a file with some features", "engines": {"node": ">= 6"}, "keywords": ["NodeLib", "fs", "FileSystem", "file system", "stat"], "license": "MIT", "main": "out/index.js", "name": "@nodelib/fs.stat", "repository": {"type": "git", "url": "https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.stat"}, "scripts": {"build": "npm run clean && npm run lint && npm run compile && npm test", "clean": "<PERSON><PERSON><PERSON> out", "compile": "tsc -b .", "compile:watch": "tsc -p . --watch --sourceMap", "lint": "tslint \"src/**/*.ts\" -p . -t stylish", "test": "mocha \"out/**/*.spec.js\" -s 0", "watch": "npm run clean && npm run compile:watch"}, "typings": "out/index.d.ts", "version": "1.1.3"}