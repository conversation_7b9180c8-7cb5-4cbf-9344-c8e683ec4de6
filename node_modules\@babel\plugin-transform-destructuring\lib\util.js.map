{"version": 3, "names": ["_core", "require", "isPureVoid", "node", "t", "isUnaryExpression", "operator", "isPureish", "argument", "unshiftForXStatementBody", "statementPath", "newStatements", "ensureBlock", "scope", "bodyScopeBindings", "get", "bindings", "hasShadowedBlockScopedBindings", "Object", "keys", "some", "name", "hasBinding", "body", "blockStatement", "unshift", "hasArrayRest", "pattern", "elements", "elem", "isRestElement", "hasObjectRest", "properties", "prop", "STOP_TRAVERSAL", "arrayUnpackVisitor", "ancestors", "state", "length", "isIdentifier", "isReferenced", "de<PERSON>t", "DestructuringTransformer", "constructor", "opts", "blockHoist", "arrayRefSet", "nodes", "kind", "iterableIsArray", "arrayLikeIsIterable", "objectRestNoSymbols", "useBuiltIns", "addHelper", "Set", "getExtendsHelper", "memberExpression", "identifier", "buildVariableAssignment", "id", "init", "op", "isMemberExpression", "expressionStatement", "assignmentExpression", "cloneNode", "buildUndefinedNode", "nodeInit", "variableDeclaration", "variableDeclarator", "_blockHoist", "buildVariableDeclaration", "declar", "push", "_init", "isObjectPattern", "pushObjectPattern", "isArrayPattern", "pushArrayPattern", "isAssignmentPattern", "pushAssignmentPattern", "toArray", "count", "has", "left", "right", "valueRef", "tempId", "generateUidIdentifierBasedOnNode", "tempConditional", "conditionalExpression", "binaryExpression", "isPattern", "patternId", "generateUidIdentifier", "pushObjectRest", "objRef", "spreadProp", "spreadPropIndex", "value", "buildObjectExcludingKeys", "slice", "pushObjectProperty", "propRef", "isLiteral", "key", "computed", "callExpression", "isStatic", "temp", "copiedPattern", "i", "isPure", "assign", "canUnpackArrayPattern", "arr", "isArrayExpression", "isSpreadElement", "isCallExpression", "getBindingIdentifiers", "traverse", "e", "pushUnpackedArrayPattern", "holeToUndefined", "el", "arrayExpression", "map", "arrayRef", "add", "elemRef", "numericLiteral", "ref", "memo", "maybeGenerateMemoised", "exports", "<PERSON><PERSON><PERSON><PERSON>", "allLiteral", "hasTemplateLiteral", "stringLiteral", "isTemplateLiteral", "String", "isPrivateName", "extendsHelper", "objectExpression", "sequenceExpression", "keyExpression", "isProgram", "block", "programScope", "getProgramParent", "convertVariableDeclaration", "path", "nodeKind", "nodeLoc", "loc", "declarations", "destructuring", "inherits", "tail", "nodesOut", "isVariableDeclaration", "isExpressionStatement", "expression", "expr", "arguments", "isForStatement", "parent", "v", "replaceWith", "replaceWithMultiple", "crawl", "convertAssignmentExpression", "parentPath", "isSequenceExpression", "isCompletionRecord", "isArrowFunctionExpression", "returnStatement"], "sources": ["../src/util.ts"], "sourcesContent": ["import { types as t } from \"@babel/core\";\nimport type { File } from \"@babel/core\";\nimport type { Scope, NodePath } from \"@babel/traverse\";\nimport type { TraversalAncestors } from \"@babel/types\";\n\nfunction isPureVoid(node: t.Node) {\n  return (\n    t.isUnaryExpression(node) &&\n    node.operator === \"void\" &&\n    t.isPureish(node.argument)\n  );\n}\n\nexport function unshiftForXStatementBody(\n  statementPath: NodePath<t.ForXStatement>,\n  newStatements: t.Statement[],\n) {\n  statementPath.ensureBlock();\n  const { scope, node } = statementPath;\n  const bodyScopeBindings = statementPath.get(\"body\").scope.bindings;\n  const hasShadowedBlockScopedBindings = Object.keys(bodyScopeBindings).some(\n    name => scope.hasBinding(name),\n  );\n\n  if (hasShadowedBlockScopedBindings) {\n    // handle shadowed variables referenced in computed keys:\n    // var a = 0;for (const { #x: x, [a++]: y } of z) { const a = 1; }\n    node.body = t.blockStatement([...newStatements, node.body]);\n  } else {\n    node.body.body.unshift(...newStatements);\n  }\n}\n\n/**\n * Test if an ArrayPattern's elements contain any RestElements.\n */\n\nfunction hasArrayRest(pattern: t.ArrayPattern) {\n  return pattern.elements.some(elem => t.isRestElement(elem));\n}\n\n/**\n * Test if an ObjectPattern's properties contain any RestElements.\n */\n\nfunction hasObjectRest(pattern: t.ObjectPattern) {\n  return pattern.properties.some(prop => t.isRestElement(prop));\n}\n\ninterface UnpackableArrayExpression extends t.ArrayExpression {\n  elements: (null | t.Expression)[];\n}\n\nconst STOP_TRAVERSAL = {};\n\ninterface ArrayUnpackVisitorState {\n  deopt: boolean;\n  bindings: Record<string, t.Identifier>;\n}\n\n// NOTE: This visitor is meant to be used via t.traverse\nconst arrayUnpackVisitor = (\n  node: t.Node,\n  ancestors: TraversalAncestors,\n  state: ArrayUnpackVisitorState,\n) => {\n  if (!ancestors.length) {\n    // Top-level node: this is the array literal.\n    return;\n  }\n\n  if (\n    t.isIdentifier(node) &&\n    t.isReferenced(node, ancestors[ancestors.length - 1].node) &&\n    state.bindings[node.name]\n  ) {\n    state.deopt = true;\n    throw STOP_TRAVERSAL;\n  }\n};\n\nexport type DestructuringTransformerNode =\n  | t.VariableDeclaration\n  | t.ExpressionStatement\n  | t.ReturnStatement;\n\ninterface DestructuringTransformerOption {\n  blockHoist?: number;\n  operator?: t.AssignmentExpression[\"operator\"];\n  nodes?: DestructuringTransformerNode[];\n  kind?: t.VariableDeclaration[\"kind\"];\n  scope: Scope;\n  arrayLikeIsIterable: boolean;\n  iterableIsArray: boolean;\n  objectRestNoSymbols: boolean;\n  useBuiltIns: boolean;\n  addHelper: File[\"addHelper\"];\n}\nexport class DestructuringTransformer {\n  private blockHoist: number;\n  private operator: t.AssignmentExpression[\"operator\"];\n  arrayRefSet: Set<string>;\n  private nodes: DestructuringTransformerNode[];\n  private scope: Scope;\n  private kind: t.VariableDeclaration[\"kind\"];\n  private iterableIsArray: boolean;\n  private arrayLikeIsIterable: boolean;\n  private objectRestNoSymbols: boolean;\n  private useBuiltIns: boolean;\n  private addHelper: File[\"addHelper\"];\n  constructor(opts: DestructuringTransformerOption) {\n    this.blockHoist = opts.blockHoist;\n    this.operator = opts.operator;\n    this.arrayRefSet = new Set();\n    this.nodes = opts.nodes || [];\n    this.scope = opts.scope;\n    this.kind = opts.kind;\n    this.iterableIsArray = opts.iterableIsArray;\n    this.arrayLikeIsIterable = opts.arrayLikeIsIterable;\n    this.objectRestNoSymbols = opts.objectRestNoSymbols;\n    this.useBuiltIns = opts.useBuiltIns;\n    this.addHelper = opts.addHelper;\n  }\n\n  getExtendsHelper() {\n    return this.useBuiltIns\n      ? t.memberExpression(t.identifier(\"Object\"), t.identifier(\"assign\"))\n      : this.addHelper(\"extends\");\n  }\n\n  buildVariableAssignment(\n    id: t.AssignmentExpression[\"left\"],\n    init: t.Expression,\n  ) {\n    let op = this.operator;\n    if (t.isMemberExpression(id)) op = \"=\";\n\n    let node: t.ExpressionStatement | t.VariableDeclaration;\n\n    if (op) {\n      node = t.expressionStatement(\n        t.assignmentExpression(\n          op,\n          id,\n          t.cloneNode(init) || this.scope.buildUndefinedNode(),\n        ),\n      );\n    } else {\n      let nodeInit: t.Expression;\n\n      if ((this.kind === \"const\" || this.kind === \"using\") && init === null) {\n        nodeInit = this.scope.buildUndefinedNode();\n      } else {\n        nodeInit = t.cloneNode(init);\n      }\n\n      node = t.variableDeclaration(this.kind, [\n        t.variableDeclarator(id, nodeInit),\n      ]);\n    }\n\n    //@ts-expect-error(todo): document block hoist property\n    node._blockHoist = this.blockHoist;\n\n    return node;\n  }\n\n  buildVariableDeclaration(id: t.Identifier, init: t.Expression) {\n    const declar = t.variableDeclaration(\"var\", [\n      t.variableDeclarator(t.cloneNode(id), t.cloneNode(init)),\n    ]);\n    // @ts-expect-error todo(flow->ts): avoid mutations\n    declar._blockHoist = this.blockHoist;\n    return declar;\n  }\n\n  push(id: t.LVal, _init: t.Expression | null) {\n    const init = t.cloneNode(_init);\n    if (t.isObjectPattern(id)) {\n      this.pushObjectPattern(id, init);\n    } else if (t.isArrayPattern(id)) {\n      this.pushArrayPattern(id, init);\n    } else if (t.isAssignmentPattern(id)) {\n      this.pushAssignmentPattern(id, init);\n    } else {\n      this.nodes.push(this.buildVariableAssignment(id, init));\n    }\n  }\n\n  toArray(node: t.Expression, count?: boolean | number) {\n    if (\n      this.iterableIsArray ||\n      (t.isIdentifier(node) && this.arrayRefSet.has(node.name))\n    ) {\n      return node;\n    } else {\n      return this.scope.toArray(node, count, this.arrayLikeIsIterable);\n    }\n  }\n\n  pushAssignmentPattern(\n    { left, right }: t.AssignmentPattern,\n    valueRef: t.Expression | null,\n  ) {\n    // handle array init with void 0. This also happens when\n    // the value was originally a hole.\n    // const [x = 42] = [void 0,];\n    // -> const x = 42;\n    if (isPureVoid(valueRef)) {\n      this.push(left, right);\n      return;\n    }\n\n    // we need to assign the current value of the assignment to avoid evaluating\n    // it more than once\n    const tempId = this.scope.generateUidIdentifierBasedOnNode(valueRef);\n\n    this.nodes.push(this.buildVariableDeclaration(tempId, valueRef));\n\n    const tempConditional = t.conditionalExpression(\n      t.binaryExpression(\n        \"===\",\n        t.cloneNode(tempId),\n        this.scope.buildUndefinedNode(),\n      ),\n      right,\n      t.cloneNode(tempId),\n    );\n\n    if (t.isPattern(left)) {\n      let patternId;\n      let node;\n\n      if (\n        this.kind === \"const\" ||\n        this.kind === \"let\" ||\n        this.kind === \"using\"\n      ) {\n        patternId = this.scope.generateUidIdentifier(tempId.name);\n        node = this.buildVariableDeclaration(patternId, tempConditional);\n      } else {\n        patternId = tempId;\n\n        node = t.expressionStatement(\n          t.assignmentExpression(\"=\", t.cloneNode(tempId), tempConditional),\n        );\n      }\n\n      this.nodes.push(node);\n      this.push(left, patternId);\n    } else {\n      this.nodes.push(this.buildVariableAssignment(left, tempConditional));\n    }\n  }\n\n  pushObjectRest(\n    pattern: t.ObjectPattern,\n    objRef: t.Expression,\n    spreadProp: t.RestElement,\n    spreadPropIndex: number,\n  ) {\n    const value = buildObjectExcludingKeys(\n      pattern.properties.slice(0, spreadPropIndex) as t.ObjectProperty[],\n      objRef,\n      this.scope,\n      name => this.addHelper(name),\n      this.objectRestNoSymbols,\n      this.useBuiltIns,\n    );\n    this.nodes.push(this.buildVariableAssignment(spreadProp.argument, value));\n  }\n\n  pushObjectProperty(prop: t.ObjectProperty, propRef: t.Expression) {\n    if (t.isLiteral(prop.key)) prop.computed = true;\n\n    const pattern = prop.value as t.LVal;\n    const objRef = t.memberExpression(\n      t.cloneNode(propRef),\n      prop.key,\n      prop.computed,\n    );\n\n    if (t.isPattern(pattern)) {\n      this.push(pattern, objRef);\n    } else {\n      this.nodes.push(this.buildVariableAssignment(pattern, objRef));\n    }\n  }\n\n  pushObjectPattern(pattern: t.ObjectPattern, objRef: t.Expression) {\n    // https://github.com/babel/babel/issues/681\n\n    if (!pattern.properties.length) {\n      this.nodes.push(\n        t.expressionStatement(\n          t.callExpression(\n            this.addHelper(\"objectDestructuringEmpty\"),\n            isPureVoid(objRef) ? [] : [objRef],\n          ),\n        ),\n      );\n      return;\n    }\n\n    // if we have more than one properties in this pattern and the objectRef is a\n    // member expression then we need to assign it to a temporary variable so it's\n    // only evaluated once\n\n    if (pattern.properties.length > 1 && !this.scope.isStatic(objRef)) {\n      const temp = this.scope.generateUidIdentifierBasedOnNode(objRef);\n      this.nodes.push(this.buildVariableDeclaration(temp, objRef));\n      objRef = temp;\n    }\n\n    // Replace impure computed key expressions if we have a rest parameter\n    if (hasObjectRest(pattern)) {\n      let copiedPattern: t.ObjectPattern;\n      for (let i = 0; i < pattern.properties.length; i++) {\n        const prop = pattern.properties[i];\n        if (t.isRestElement(prop)) {\n          break;\n        }\n        const key = prop.key;\n        if (prop.computed && !this.scope.isPure(key)) {\n          const name = this.scope.generateUidIdentifierBasedOnNode(key);\n          this.nodes.push(\n            //@ts-expect-error PrivateName has been handled by destructuring-private\n            this.buildVariableDeclaration(name, key),\n          );\n          if (!copiedPattern) {\n            copiedPattern = pattern = {\n              ...pattern,\n              properties: pattern.properties.slice(),\n            };\n          }\n          copiedPattern.properties[i] = {\n            ...prop,\n            key: name,\n          };\n        }\n      }\n    }\n\n    for (let i = 0; i < pattern.properties.length; i++) {\n      const prop = pattern.properties[i];\n      if (t.isRestElement(prop)) {\n        this.pushObjectRest(pattern, objRef, prop, i);\n      } else {\n        this.pushObjectProperty(prop, objRef);\n      }\n    }\n  }\n\n  canUnpackArrayPattern(\n    pattern: t.ArrayPattern,\n    arr: t.Expression,\n  ): arr is UnpackableArrayExpression {\n    // not an array so there's no way we can deal with this\n    if (!t.isArrayExpression(arr)) return false;\n\n    // pattern has less elements than the array and doesn't have a rest so some\n    // elements wont be evaluated\n    if (pattern.elements.length > arr.elements.length) return;\n    if (\n      pattern.elements.length < arr.elements.length &&\n      !hasArrayRest(pattern)\n    ) {\n      return false;\n    }\n\n    for (const elem of pattern.elements) {\n      // deopt on holes\n      if (!elem) return false;\n\n      // deopt on member expressions as they may be included in the RHS\n      if (t.isMemberExpression(elem)) return false;\n    }\n\n    for (const elem of arr.elements) {\n      // deopt on spread elements\n      if (t.isSpreadElement(elem)) return false;\n\n      // deopt call expressions as they might change values of LHS variables\n      if (t.isCallExpression(elem)) return false;\n\n      // deopt on member expressions as they may be getter/setters and have side-effects\n      if (t.isMemberExpression(elem)) return false;\n    }\n\n    // deopt on reference to left side identifiers\n    const bindings = t.getBindingIdentifiers(pattern);\n    const state: ArrayUnpackVisitorState = { deopt: false, bindings };\n\n    try {\n      t.traverse(arr, arrayUnpackVisitor, state);\n    } catch (e) {\n      if (e !== STOP_TRAVERSAL) throw e;\n    }\n\n    return !state.deopt;\n  }\n\n  pushUnpackedArrayPattern(\n    pattern: t.ArrayPattern,\n    arr: UnpackableArrayExpression,\n  ) {\n    const holeToUndefined = (el: t.Expression) =>\n      el ?? this.scope.buildUndefinedNode();\n\n    for (let i = 0; i < pattern.elements.length; i++) {\n      const elem = pattern.elements[i];\n      if (t.isRestElement(elem)) {\n        this.push(\n          elem.argument,\n          t.arrayExpression(arr.elements.slice(i).map(holeToUndefined)),\n        );\n      } else {\n        this.push(elem, holeToUndefined(arr.elements[i]));\n      }\n    }\n  }\n\n  pushArrayPattern(pattern: t.ArrayPattern, arrayRef: t.Expression | null) {\n    if (arrayRef === null) {\n      this.nodes.push(\n        t.expressionStatement(\n          t.callExpression(this.addHelper(\"objectDestructuringEmpty\"), []),\n        ),\n      );\n      return;\n    }\n    if (!pattern.elements) return;\n\n    // optimise basic array destructuring of an array expression\n    //\n    // we can't do this to a pattern of unequal size to it's right hand\n    // array expression as then there will be values that wont be evaluated\n    //\n    // eg: let [a, b] = [1, 2];\n\n    if (this.canUnpackArrayPattern(pattern, arrayRef)) {\n      this.pushUnpackedArrayPattern(pattern, arrayRef);\n      return;\n    }\n\n    // if we have a rest then we need all the elements so don't tell\n    // `scope.toArray` to only get a certain amount\n\n    const count = !hasArrayRest(pattern) && pattern.elements.length;\n\n    // so we need to ensure that the `arrayRef` is an array, `scope.toArray` will\n    // return a locally bound identifier if it's been inferred to be an array,\n    // otherwise it'll be a call to a helper that will ensure it's one\n\n    const toArray = this.toArray(arrayRef, count);\n\n    if (t.isIdentifier(toArray)) {\n      // we've been given an identifier so it must have been inferred to be an\n      // array\n      arrayRef = toArray;\n    } else {\n      arrayRef = this.scope.generateUidIdentifierBasedOnNode(arrayRef);\n      this.arrayRefSet.add(arrayRef.name);\n      this.nodes.push(this.buildVariableDeclaration(arrayRef, toArray));\n    }\n\n    for (let i = 0; i < pattern.elements.length; i++) {\n      const elem = pattern.elements[i];\n\n      // hole\n      if (!elem) continue;\n\n      let elemRef;\n\n      if (t.isRestElement(elem)) {\n        elemRef = this.toArray(arrayRef);\n        elemRef = t.callExpression(\n          t.memberExpression(elemRef, t.identifier(\"slice\")),\n          [t.numericLiteral(i)],\n        );\n\n        // set the element to the rest element argument since we've dealt with it\n        // being a rest already\n        this.push(elem.argument, elemRef);\n      } else {\n        elemRef = t.memberExpression(arrayRef, t.numericLiteral(i), true);\n        this.push(elem, elemRef);\n      }\n    }\n  }\n\n  init(pattern: t.LVal, ref: t.Expression) {\n    // trying to destructure a value that we can't evaluate more than once so we\n    // need to save it to a variable\n\n    if (!t.isArrayExpression(ref) && !t.isMemberExpression(ref)) {\n      const memo = this.scope.maybeGenerateMemoised(ref, true);\n      if (memo) {\n        this.nodes.push(this.buildVariableDeclaration(memo, t.cloneNode(ref)));\n        ref = memo;\n      }\n    }\n\n    this.push(pattern, ref);\n\n    return this.nodes;\n  }\n}\n\ninterface ExcludingKey {\n  key: t.Expression | t.PrivateName;\n  computed: boolean;\n}\n\nexport function buildObjectExcludingKeys<T extends ExcludingKey>(\n  excludedKeys: T[],\n  objRef: t.Expression,\n  scope: Scope,\n  addHelper: File[\"addHelper\"],\n  objectRestNoSymbols: boolean,\n  useBuiltIns: boolean,\n): t.CallExpression {\n  // get all the keys that appear in this object before the current spread\n\n  const keys = [];\n  let allLiteral = true;\n  let hasTemplateLiteral = false;\n  for (let i = 0; i < excludedKeys.length; i++) {\n    const prop = excludedKeys[i];\n    const key = prop.key;\n    if (t.isIdentifier(key) && !prop.computed) {\n      keys.push(t.stringLiteral(key.name));\n    } else if (t.isTemplateLiteral(key)) {\n      keys.push(t.cloneNode(key));\n      hasTemplateLiteral = true;\n    } else if (t.isLiteral(key)) {\n      // @ts-expect-error todo(flow->ts) NullLiteral\n      keys.push(t.stringLiteral(String(key.value)));\n    } else if (t.isPrivateName(key)) {\n      // private key is not enumerable\n    } else {\n      keys.push(t.cloneNode(key));\n      allLiteral = false;\n    }\n  }\n\n  let value;\n  if (keys.length === 0) {\n    const extendsHelper = useBuiltIns\n      ? t.memberExpression(t.identifier(\"Object\"), t.identifier(\"assign\"))\n      : addHelper(\"extends\");\n    value = t.callExpression(extendsHelper, [\n      t.objectExpression([]),\n      t.sequenceExpression([\n        t.callExpression(addHelper(\"objectDestructuringEmpty\"), [\n          t.cloneNode(objRef),\n        ]),\n        t.cloneNode(objRef),\n      ]),\n    ]);\n  } else {\n    let keyExpression: t.Expression = t.arrayExpression(keys);\n\n    if (!allLiteral) {\n      keyExpression = t.callExpression(\n        t.memberExpression(keyExpression, t.identifier(\"map\")),\n        [addHelper(\"toPropertyKey\")],\n      );\n    } else if (!hasTemplateLiteral && !t.isProgram(scope.block)) {\n      // Hoist definition of excluded keys, so that it's not created each time.\n      const programScope = scope.getProgramParent();\n      const id = programScope.generateUidIdentifier(\"excluded\");\n\n      programScope.push({\n        id,\n        init: keyExpression,\n        kind: \"const\",\n      });\n\n      keyExpression = t.cloneNode(id);\n    }\n\n    value = t.callExpression(\n      addHelper(`objectWithoutProperties${objectRestNoSymbols ? \"Loose\" : \"\"}`),\n      [t.cloneNode(objRef), keyExpression],\n    );\n  }\n  return value;\n}\n\nexport function convertVariableDeclaration(\n  path: NodePath<t.VariableDeclaration>,\n  addHelper: File[\"addHelper\"],\n  arrayLikeIsIterable: boolean,\n  iterableIsArray: boolean,\n  objectRestNoSymbols: boolean,\n  useBuiltIns: boolean,\n) {\n  const { node, scope } = path;\n\n  const nodeKind = node.kind;\n  const nodeLoc = node.loc;\n  const nodes = [];\n\n  for (let i = 0; i < node.declarations.length; i++) {\n    const declar = node.declarations[i];\n\n    const patternId = declar.init;\n    const pattern = declar.id;\n\n    const destructuring: DestructuringTransformer =\n      new DestructuringTransformer({\n        // @ts-expect-error(todo): avoid internal properties access\n        blockHoist: node._blockHoist,\n        nodes: nodes,\n        scope: scope,\n        kind: node.kind,\n        iterableIsArray,\n        arrayLikeIsIterable,\n        useBuiltIns,\n        objectRestNoSymbols,\n        addHelper,\n      });\n\n    if (t.isPattern(pattern)) {\n      destructuring.init(pattern, patternId);\n\n      if (+i !== node.declarations.length - 1) {\n        // we aren't the last declarator so let's just make the\n        // last transformed node inherit from us\n        t.inherits(nodes[nodes.length - 1], declar);\n      }\n    } else {\n      nodes.push(\n        t.inherits(\n          destructuring.buildVariableAssignment(pattern, patternId),\n          declar,\n        ),\n      );\n    }\n  }\n\n  let tail: t.VariableDeclaration | null = null;\n  let nodesOut = [];\n  for (const node of nodes) {\n    if (t.isVariableDeclaration(node)) {\n      if (tail !== null) {\n        // Create a single compound declarations\n        tail.declarations.push(...node.declarations);\n        continue;\n      } else {\n        // Make sure the original node kind is used for each compound declaration\n        node.kind = nodeKind;\n        tail = node;\n      }\n    } else {\n      tail = null;\n    }\n    // Propagate the original declaration node's location\n    if (!node.loc) {\n      node.loc = nodeLoc;\n    }\n    nodesOut.push(node);\n  }\n\n  if (\n    nodesOut.length === 2 &&\n    t.isVariableDeclaration(nodesOut[0]) &&\n    t.isExpressionStatement(nodesOut[1]) &&\n    t.isCallExpression(nodesOut[1].expression) &&\n    nodesOut[0].declarations.length === 1\n  ) {\n    // This can only happen when we generate this code:\n    //    var _ref = DESTRUCTURED_VALUE;\n    //     babelHelpers.objectDestructuringEmpty(_ref);\n    // Since pushing those two statements to the for loop .init will require an IIFE,\n    // we can optimize them to\n    //     babelHelpers.objectDestructuringEmpty(DESTRUCTURED_VALUE);\n    const expr = nodesOut[1].expression;\n    expr.arguments = [nodesOut[0].declarations[0].init];\n    nodesOut = [expr];\n  } else {\n    // We must keep nodes all are expressions or statements, so `replaceWithMultiple` can work.\n    if (\n      t.isForStatement(path.parent, { init: node }) &&\n      !nodesOut.some(v => t.isVariableDeclaration(v))\n    ) {\n      for (let i = 0; i < nodesOut.length; i++) {\n        const node: t.Node = nodesOut[i];\n        if (t.isExpressionStatement(node)) {\n          nodesOut[i] = node.expression;\n        }\n      }\n    }\n  }\n\n  if (nodesOut.length === 1) {\n    path.replaceWith(nodesOut[0]);\n  } else {\n    path.replaceWithMultiple(nodesOut);\n  }\n  scope.crawl();\n}\n\nexport function convertAssignmentExpression(\n  path: NodePath<t.AssignmentExpression>,\n  addHelper: File[\"addHelper\"],\n  arrayLikeIsIterable: boolean,\n  iterableIsArray: boolean,\n  objectRestNoSymbols: boolean,\n  useBuiltIns: boolean,\n) {\n  const { node, scope, parentPath } = path;\n\n  const nodes: DestructuringTransformerNode[] = [];\n\n  const destructuring = new DestructuringTransformer({\n    operator: node.operator,\n    scope: scope,\n    nodes: nodes,\n    arrayLikeIsIterable,\n    iterableIsArray,\n    objectRestNoSymbols,\n    useBuiltIns,\n    addHelper,\n  });\n\n  let ref: t.Identifier | void;\n  if (\n    (!parentPath.isExpressionStatement() &&\n      !parentPath.isSequenceExpression()) ||\n    path.isCompletionRecord()\n  ) {\n    ref = scope.generateUidIdentifierBasedOnNode(node.right, \"ref\");\n\n    nodes.push(\n      t.variableDeclaration(\"var\", [t.variableDeclarator(ref, node.right)]),\n    );\n\n    if (t.isArrayExpression(node.right)) {\n      destructuring.arrayRefSet.add(ref.name);\n    }\n  }\n\n  destructuring.init(node.left, ref || node.right);\n\n  if (ref) {\n    if (parentPath.isArrowFunctionExpression()) {\n      path.replaceWith(t.blockStatement([]));\n      nodes.push(t.returnStatement(t.cloneNode(ref)));\n    } else {\n      nodes.push(t.expressionStatement(t.cloneNode(ref)));\n    }\n  }\n\n  path.replaceWithMultiple(nodes);\n  scope.crawl();\n}\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAKA,SAASC,UAAUA,CAACC,IAAY,EAAE;EAChC,OACEC,WAAC,CAACC,iBAAiB,CAACF,IAAI,CAAC,IACzBA,IAAI,CAACG,QAAQ,KAAK,MAAM,IACxBF,WAAC,CAACG,SAAS,CAACJ,IAAI,CAACK,QAAQ,CAAC;AAE9B;AAEO,SAASC,wBAAwBA,CACtCC,aAAwC,EACxCC,aAA4B,EAC5B;EACAD,aAAa,CAACE,WAAW,CAAC,CAAC;EAC3B,MAAM;IAAEC,KAAK;IAAEV;EAAK,CAAC,GAAGO,aAAa;EACrC,MAAMI,iBAAiB,GAAGJ,aAAa,CAACK,GAAG,CAAC,MAAM,CAAC,CAACF,KAAK,CAACG,QAAQ;EAClE,MAAMC,8BAA8B,GAAGC,MAAM,CAACC,IAAI,CAACL,iBAAiB,CAAC,CAACM,IAAI,CACxEC,IAAI,IAAIR,KAAK,CAACS,UAAU,CAACD,IAAI,CAC/B,CAAC;EAED,IAAIJ,8BAA8B,EAAE;IAGlCd,IAAI,CAACoB,IAAI,GAAGnB,WAAC,CAACoB,cAAc,CAAC,CAAC,GAAGb,aAAa,EAAER,IAAI,CAACoB,IAAI,CAAC,CAAC;EAC7D,CAAC,MAAM;IACLpB,IAAI,CAACoB,IAAI,CAACA,IAAI,CAACE,OAAO,CAAC,GAAGd,aAAa,CAAC;EAC1C;AACF;AAMA,SAASe,YAAYA,CAACC,OAAuB,EAAE;EAC7C,OAAOA,OAAO,CAACC,QAAQ,CAACR,IAAI,CAACS,IAAI,IAAIzB,WAAC,CAAC0B,aAAa,CAACD,IAAI,CAAC,CAAC;AAC7D;AAMA,SAASE,aAAaA,CAACJ,OAAwB,EAAE;EAC/C,OAAOA,OAAO,CAACK,UAAU,CAACZ,IAAI,CAACa,IAAI,IAAI7B,WAAC,CAAC0B,aAAa,CAACG,IAAI,CAAC,CAAC;AAC/D;AAMA,MAAMC,cAAc,GAAG,CAAC,CAAC;AAQzB,MAAMC,kBAAkB,GAAGA,CACzBhC,IAAY,EACZiC,SAA6B,EAC7BC,KAA8B,KAC3B;EACH,IAAI,CAACD,SAAS,CAACE,MAAM,EAAE;IAErB;EACF;EAEA,IACElC,WAAC,CAACmC,YAAY,CAACpC,IAAI,CAAC,IACpBC,WAAC,CAACoC,YAAY,CAACrC,IAAI,EAAEiC,SAAS,CAACA,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC,CAACnC,IAAI,CAAC,IAC1DkC,KAAK,CAACrB,QAAQ,CAACb,IAAI,CAACkB,IAAI,CAAC,EACzB;IACAgB,KAAK,CAACI,KAAK,GAAG,IAAI;IAClB,MAAMP,cAAc;EACtB;AACF,CAAC;AAmBM,MAAMQ,wBAAwB,CAAC;EAYpCC,WAAWA,CAACC,IAAoC,EAAE;IAAA,KAX1CC,UAAU;IAAA,KACVvC,QAAQ;IAAA,KAChBwC,WAAW;IAAA,KACHC,KAAK;IAAA,KACLlC,KAAK;IAAA,KACLmC,IAAI;IAAA,KACJC,eAAe;IAAA,KACfC,mBAAmB;IAAA,KACnBC,mBAAmB;IAAA,KACnBC,WAAW;IAAA,KACXC,SAAS;IAEf,IAAI,CAACR,UAAU,GAAGD,IAAI,CAACC,UAAU;IACjC,IAAI,CAACvC,QAAQ,GAAGsC,IAAI,CAACtC,QAAQ;IAC7B,IAAI,CAACwC,WAAW,GAAG,IAAIQ,GAAG,CAAC,CAAC;IAC5B,IAAI,CAACP,KAAK,GAAGH,IAAI,CAACG,KAAK,IAAI,EAAE;IAC7B,IAAI,CAAClC,KAAK,GAAG+B,IAAI,CAAC/B,KAAK;IACvB,IAAI,CAACmC,IAAI,GAAGJ,IAAI,CAACI,IAAI;IACrB,IAAI,CAACC,eAAe,GAAGL,IAAI,CAACK,eAAe;IAC3C,IAAI,CAACC,mBAAmB,GAAGN,IAAI,CAACM,mBAAmB;IACnD,IAAI,CAACC,mBAAmB,GAAGP,IAAI,CAACO,mBAAmB;IACnD,IAAI,CAACC,WAAW,GAAGR,IAAI,CAACQ,WAAW;IACnC,IAAI,CAACC,SAAS,GAAGT,IAAI,CAACS,SAAS;EACjC;EAEAE,gBAAgBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACH,WAAW,GACnBhD,WAAC,CAACoD,gBAAgB,CAACpD,WAAC,CAACqD,UAAU,CAAC,QAAQ,CAAC,EAAErD,WAAC,CAACqD,UAAU,CAAC,QAAQ,CAAC,CAAC,GAClE,IAAI,CAACJ,SAAS,CAAC,SAAS,CAAC;EAC/B;EAEAK,uBAAuBA,CACrBC,EAAkC,EAClCC,IAAkB,EAClB;IACA,IAAIC,EAAE,GAAG,IAAI,CAACvD,QAAQ;IACtB,IAAIF,WAAC,CAAC0D,kBAAkB,CAACH,EAAE,CAAC,EAAEE,EAAE,GAAG,GAAG;IAEtC,IAAI1D,IAAmD;IAEvD,IAAI0D,EAAE,EAAE;MACN1D,IAAI,GAAGC,WAAC,CAAC2D,mBAAmB,CAC1B3D,WAAC,CAAC4D,oBAAoB,CACpBH,EAAE,EACFF,EAAE,EACFvD,WAAC,CAAC6D,SAAS,CAACL,IAAI,CAAC,IAAI,IAAI,CAAC/C,KAAK,CAACqD,kBAAkB,CAAC,CACrD,CACF,CAAC;IACH,CAAC,MAAM;MACL,IAAIC,QAAsB;MAE1B,IAAI,CAAC,IAAI,CAACnB,IAAI,KAAK,OAAO,IAAI,IAAI,CAACA,IAAI,KAAK,OAAO,KAAKY,IAAI,KAAK,IAAI,EAAE;QACrEO,QAAQ,GAAG,IAAI,CAACtD,KAAK,CAACqD,kBAAkB,CAAC,CAAC;MAC5C,CAAC,MAAM;QACLC,QAAQ,GAAG/D,WAAC,CAAC6D,SAAS,CAACL,IAAI,CAAC;MAC9B;MAEAzD,IAAI,GAAGC,WAAC,CAACgE,mBAAmB,CAAC,IAAI,CAACpB,IAAI,EAAE,CACtC5C,WAAC,CAACiE,kBAAkB,CAACV,EAAE,EAAEQ,QAAQ,CAAC,CACnC,CAAC;IACJ;IAGAhE,IAAI,CAACmE,WAAW,GAAG,IAAI,CAACzB,UAAU;IAElC,OAAO1C,IAAI;EACb;EAEAoE,wBAAwBA,CAACZ,EAAgB,EAAEC,IAAkB,EAAE;IAC7D,MAAMY,MAAM,GAAGpE,WAAC,CAACgE,mBAAmB,CAAC,KAAK,EAAE,CAC1ChE,WAAC,CAACiE,kBAAkB,CAACjE,WAAC,CAAC6D,SAAS,CAACN,EAAE,CAAC,EAAEvD,WAAC,CAAC6D,SAAS,CAACL,IAAI,CAAC,CAAC,CACzD,CAAC;IAEFY,MAAM,CAACF,WAAW,GAAG,IAAI,CAACzB,UAAU;IACpC,OAAO2B,MAAM;EACf;EAEAC,IAAIA,CAACd,EAAU,EAAEe,KAA0B,EAAE;IAC3C,MAAMd,IAAI,GAAGxD,WAAC,CAAC6D,SAAS,CAACS,KAAK,CAAC;IAC/B,IAAItE,WAAC,CAACuE,eAAe,CAAChB,EAAE,CAAC,EAAE;MACzB,IAAI,CAACiB,iBAAiB,CAACjB,EAAE,EAAEC,IAAI,CAAC;IAClC,CAAC,MAAM,IAAIxD,WAAC,CAACyE,cAAc,CAAClB,EAAE,CAAC,EAAE;MAC/B,IAAI,CAACmB,gBAAgB,CAACnB,EAAE,EAAEC,IAAI,CAAC;IACjC,CAAC,MAAM,IAAIxD,WAAC,CAAC2E,mBAAmB,CAACpB,EAAE,CAAC,EAAE;MACpC,IAAI,CAACqB,qBAAqB,CAACrB,EAAE,EAAEC,IAAI,CAAC;IACtC,CAAC,MAAM;MACL,IAAI,CAACb,KAAK,CAAC0B,IAAI,CAAC,IAAI,CAACf,uBAAuB,CAACC,EAAE,EAAEC,IAAI,CAAC,CAAC;IACzD;EACF;EAEAqB,OAAOA,CAAC9E,IAAkB,EAAE+E,KAAwB,EAAE;IACpD,IACE,IAAI,CAACjC,eAAe,IACnB7C,WAAC,CAACmC,YAAY,CAACpC,IAAI,CAAC,IAAI,IAAI,CAAC2C,WAAW,CAACqC,GAAG,CAAChF,IAAI,CAACkB,IAAI,CAAE,EACzD;MACA,OAAOlB,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAI,CAACU,KAAK,CAACoE,OAAO,CAAC9E,IAAI,EAAE+E,KAAK,EAAE,IAAI,CAAChC,mBAAmB,CAAC;IAClE;EACF;EAEA8B,qBAAqBA,CACnB;IAAEI,IAAI;IAAEC;EAA2B,CAAC,EACpCC,QAA6B,EAC7B;IAKA,IAAIpF,UAAU,CAACoF,QAAQ,CAAC,EAAE;MACxB,IAAI,CAACb,IAAI,CAACW,IAAI,EAAEC,KAAK,CAAC;MACtB;IACF;IAIA,MAAME,MAAM,GAAG,IAAI,CAAC1E,KAAK,CAAC2E,gCAAgC,CAACF,QAAQ,CAAC;IAEpE,IAAI,CAACvC,KAAK,CAAC0B,IAAI,CAAC,IAAI,CAACF,wBAAwB,CAACgB,MAAM,EAAED,QAAQ,CAAC,CAAC;IAEhE,MAAMG,eAAe,GAAGrF,WAAC,CAACsF,qBAAqB,CAC7CtF,WAAC,CAACuF,gBAAgB,CAChB,KAAK,EACLvF,WAAC,CAAC6D,SAAS,CAACsB,MAAM,CAAC,EACnB,IAAI,CAAC1E,KAAK,CAACqD,kBAAkB,CAAC,CAChC,CAAC,EACDmB,KAAK,EACLjF,WAAC,CAAC6D,SAAS,CAACsB,MAAM,CACpB,CAAC;IAED,IAAInF,WAAC,CAACwF,SAAS,CAACR,IAAI,CAAC,EAAE;MACrB,IAAIS,SAAS;MACb,IAAI1F,IAAI;MAER,IACE,IAAI,CAAC6C,IAAI,KAAK,OAAO,IACrB,IAAI,CAACA,IAAI,KAAK,KAAK,IACnB,IAAI,CAACA,IAAI,KAAK,OAAO,EACrB;QACA6C,SAAS,GAAG,IAAI,CAAChF,KAAK,CAACiF,qBAAqB,CAACP,MAAM,CAAClE,IAAI,CAAC;QACzDlB,IAAI,GAAG,IAAI,CAACoE,wBAAwB,CAACsB,SAAS,EAAEJ,eAAe,CAAC;MAClE,CAAC,MAAM;QACLI,SAAS,GAAGN,MAAM;QAElBpF,IAAI,GAAGC,WAAC,CAAC2D,mBAAmB,CAC1B3D,WAAC,CAAC4D,oBAAoB,CAAC,GAAG,EAAE5D,WAAC,CAAC6D,SAAS,CAACsB,MAAM,CAAC,EAAEE,eAAe,CAClE,CAAC;MACH;MAEA,IAAI,CAAC1C,KAAK,CAAC0B,IAAI,CAACtE,IAAI,CAAC;MACrB,IAAI,CAACsE,IAAI,CAACW,IAAI,EAAES,SAAS,CAAC;IAC5B,CAAC,MAAM;MACL,IAAI,CAAC9C,KAAK,CAAC0B,IAAI,CAAC,IAAI,CAACf,uBAAuB,CAAC0B,IAAI,EAAEK,eAAe,CAAC,CAAC;IACtE;EACF;EAEAM,cAAcA,CACZpE,OAAwB,EACxBqE,MAAoB,EACpBC,UAAyB,EACzBC,eAAuB,EACvB;IACA,MAAMC,KAAK,GAAGC,wBAAwB,CACpCzE,OAAO,CAACK,UAAU,CAACqE,KAAK,CAAC,CAAC,EAAEH,eAAe,CAAC,EAC5CF,MAAM,EACN,IAAI,CAACnF,KAAK,EACVQ,IAAI,IAAI,IAAI,CAACgC,SAAS,CAAChC,IAAI,CAAC,EAC5B,IAAI,CAAC8B,mBAAmB,EACxB,IAAI,CAACC,WACP,CAAC;IACD,IAAI,CAACL,KAAK,CAAC0B,IAAI,CAAC,IAAI,CAACf,uBAAuB,CAACuC,UAAU,CAACzF,QAAQ,EAAE2F,KAAK,CAAC,CAAC;EAC3E;EAEAG,kBAAkBA,CAACrE,IAAsB,EAAEsE,OAAqB,EAAE;IAChE,IAAInG,WAAC,CAACoG,SAAS,CAACvE,IAAI,CAACwE,GAAG,CAAC,EAAExE,IAAI,CAACyE,QAAQ,GAAG,IAAI;IAE/C,MAAM/E,OAAO,GAAGM,IAAI,CAACkE,KAAe;IACpC,MAAMH,MAAM,GAAG5F,WAAC,CAACoD,gBAAgB,CAC/BpD,WAAC,CAAC6D,SAAS,CAACsC,OAAO,CAAC,EACpBtE,IAAI,CAACwE,GAAG,EACRxE,IAAI,CAACyE,QACP,CAAC;IAED,IAAItG,WAAC,CAACwF,SAAS,CAACjE,OAAO,CAAC,EAAE;MACxB,IAAI,CAAC8C,IAAI,CAAC9C,OAAO,EAAEqE,MAAM,CAAC;IAC5B,CAAC,MAAM;MACL,IAAI,CAACjD,KAAK,CAAC0B,IAAI,CAAC,IAAI,CAACf,uBAAuB,CAAC/B,OAAO,EAAEqE,MAAM,CAAC,CAAC;IAChE;EACF;EAEApB,iBAAiBA,CAACjD,OAAwB,EAAEqE,MAAoB,EAAE;IAGhE,IAAI,CAACrE,OAAO,CAACK,UAAU,CAACM,MAAM,EAAE;MAC9B,IAAI,CAACS,KAAK,CAAC0B,IAAI,CACbrE,WAAC,CAAC2D,mBAAmB,CACnB3D,WAAC,CAACuG,cAAc,CACd,IAAI,CAACtD,SAAS,CAAC,0BAA0B,CAAC,EAC1CnD,UAAU,CAAC8F,MAAM,CAAC,GAAG,EAAE,GAAG,CAACA,MAAM,CACnC,CACF,CACF,CAAC;MACD;IACF;IAMA,IAAIrE,OAAO,CAACK,UAAU,CAACM,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAACzB,KAAK,CAAC+F,QAAQ,CAACZ,MAAM,CAAC,EAAE;MACjE,MAAMa,IAAI,GAAG,IAAI,CAAChG,KAAK,CAAC2E,gCAAgC,CAACQ,MAAM,CAAC;MAChE,IAAI,CAACjD,KAAK,CAAC0B,IAAI,CAAC,IAAI,CAACF,wBAAwB,CAACsC,IAAI,EAAEb,MAAM,CAAC,CAAC;MAC5DA,MAAM,GAAGa,IAAI;IACf;IAGA,IAAI9E,aAAa,CAACJ,OAAO,CAAC,EAAE;MAC1B,IAAImF,aAA8B;MAClC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpF,OAAO,CAACK,UAAU,CAACM,MAAM,EAAEyE,CAAC,EAAE,EAAE;QAClD,MAAM9E,IAAI,GAAGN,OAAO,CAACK,UAAU,CAAC+E,CAAC,CAAC;QAClC,IAAI3G,WAAC,CAAC0B,aAAa,CAACG,IAAI,CAAC,EAAE;UACzB;QACF;QACA,MAAMwE,GAAG,GAAGxE,IAAI,CAACwE,GAAG;QACpB,IAAIxE,IAAI,CAACyE,QAAQ,IAAI,CAAC,IAAI,CAAC7F,KAAK,CAACmG,MAAM,CAACP,GAAG,CAAC,EAAE;UAC5C,MAAMpF,IAAI,GAAG,IAAI,CAACR,KAAK,CAAC2E,gCAAgC,CAACiB,GAAG,CAAC;UAC7D,IAAI,CAAC1D,KAAK,CAAC0B,IAAI,CAEb,IAAI,CAACF,wBAAwB,CAAClD,IAAI,EAAEoF,GAAG,CACzC,CAAC;UACD,IAAI,CAACK,aAAa,EAAE;YAClBA,aAAa,GAAGnF,OAAO,GAAAT,MAAA,CAAA+F,MAAA,KAClBtF,OAAO;cACVK,UAAU,EAAEL,OAAO,CAACK,UAAU,CAACqE,KAAK,CAAC;YAAC,EACvC;UACH;UACAS,aAAa,CAAC9E,UAAU,CAAC+E,CAAC,CAAC,GAAA7F,MAAA,CAAA+F,MAAA,KACtBhF,IAAI;YACPwE,GAAG,EAAEpF;UAAI,EACV;QACH;MACF;IACF;IAEA,KAAK,IAAI0F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpF,OAAO,CAACK,UAAU,CAACM,MAAM,EAAEyE,CAAC,EAAE,EAAE;MAClD,MAAM9E,IAAI,GAAGN,OAAO,CAACK,UAAU,CAAC+E,CAAC,CAAC;MAClC,IAAI3G,WAAC,CAAC0B,aAAa,CAACG,IAAI,CAAC,EAAE;QACzB,IAAI,CAAC8D,cAAc,CAACpE,OAAO,EAAEqE,MAAM,EAAE/D,IAAI,EAAE8E,CAAC,CAAC;MAC/C,CAAC,MAAM;QACL,IAAI,CAACT,kBAAkB,CAACrE,IAAI,EAAE+D,MAAM,CAAC;MACvC;IACF;EACF;EAEAkB,qBAAqBA,CACnBvF,OAAuB,EACvBwF,GAAiB,EACiB;IAElC,IAAI,CAAC/G,WAAC,CAACgH,iBAAiB,CAACD,GAAG,CAAC,EAAE,OAAO,KAAK;IAI3C,IAAIxF,OAAO,CAACC,QAAQ,CAACU,MAAM,GAAG6E,GAAG,CAACvF,QAAQ,CAACU,MAAM,EAAE;IACnD,IACEX,OAAO,CAACC,QAAQ,CAACU,MAAM,GAAG6E,GAAG,CAACvF,QAAQ,CAACU,MAAM,IAC7C,CAACZ,YAAY,CAACC,OAAO,CAAC,EACtB;MACA,OAAO,KAAK;IACd;IAEA,KAAK,MAAME,IAAI,IAAIF,OAAO,CAACC,QAAQ,EAAE;MAEnC,IAAI,CAACC,IAAI,EAAE,OAAO,KAAK;MAGvB,IAAIzB,WAAC,CAAC0D,kBAAkB,CAACjC,IAAI,CAAC,EAAE,OAAO,KAAK;IAC9C;IAEA,KAAK,MAAMA,IAAI,IAAIsF,GAAG,CAACvF,QAAQ,EAAE;MAE/B,IAAIxB,WAAC,CAACiH,eAAe,CAACxF,IAAI,CAAC,EAAE,OAAO,KAAK;MAGzC,IAAIzB,WAAC,CAACkH,gBAAgB,CAACzF,IAAI,CAAC,EAAE,OAAO,KAAK;MAG1C,IAAIzB,WAAC,CAAC0D,kBAAkB,CAACjC,IAAI,CAAC,EAAE,OAAO,KAAK;IAC9C;IAGA,MAAMb,QAAQ,GAAGZ,WAAC,CAACmH,qBAAqB,CAAC5F,OAAO,CAAC;IACjD,MAAMU,KAA8B,GAAG;MAAEI,KAAK,EAAE,KAAK;MAAEzB;IAAS,CAAC;IAEjE,IAAI;MACFZ,WAAC,CAACoH,QAAQ,CAACL,GAAG,EAAEhF,kBAAkB,EAAEE,KAAK,CAAC;IAC5C,CAAC,CAAC,OAAOoF,CAAC,EAAE;MACV,IAAIA,CAAC,KAAKvF,cAAc,EAAE,MAAMuF,CAAC;IACnC;IAEA,OAAO,CAACpF,KAAK,CAACI,KAAK;EACrB;EAEAiF,wBAAwBA,CACtB/F,OAAuB,EACvBwF,GAA8B,EAC9B;IACA,MAAMQ,eAAe,GAAIC,EAAgB,IACvCA,EAAE,WAAFA,EAAE,GAAI,IAAI,CAAC/G,KAAK,CAACqD,kBAAkB,CAAC,CAAC;IAEvC,KAAK,IAAI6C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpF,OAAO,CAACC,QAAQ,CAACU,MAAM,EAAEyE,CAAC,EAAE,EAAE;MAChD,MAAMlF,IAAI,GAAGF,OAAO,CAACC,QAAQ,CAACmF,CAAC,CAAC;MAChC,IAAI3G,WAAC,CAAC0B,aAAa,CAACD,IAAI,CAAC,EAAE;QACzB,IAAI,CAAC4C,IAAI,CACP5C,IAAI,CAACrB,QAAQ,EACbJ,WAAC,CAACyH,eAAe,CAACV,GAAG,CAACvF,QAAQ,CAACyE,KAAK,CAACU,CAAC,CAAC,CAACe,GAAG,CAACH,eAAe,CAAC,CAC9D,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAAClD,IAAI,CAAC5C,IAAI,EAAE8F,eAAe,CAACR,GAAG,CAACvF,QAAQ,CAACmF,CAAC,CAAC,CAAC,CAAC;MACnD;IACF;EACF;EAEAjC,gBAAgBA,CAACnD,OAAuB,EAAEoG,QAA6B,EAAE;IACvE,IAAIA,QAAQ,KAAK,IAAI,EAAE;MACrB,IAAI,CAAChF,KAAK,CAAC0B,IAAI,CACbrE,WAAC,CAAC2D,mBAAmB,CACnB3D,WAAC,CAACuG,cAAc,CAAC,IAAI,CAACtD,SAAS,CAAC,0BAA0B,CAAC,EAAE,EAAE,CACjE,CACF,CAAC;MACD;IACF;IACA,IAAI,CAAC1B,OAAO,CAACC,QAAQ,EAAE;IASvB,IAAI,IAAI,CAACsF,qBAAqB,CAACvF,OAAO,EAAEoG,QAAQ,CAAC,EAAE;MACjD,IAAI,CAACL,wBAAwB,CAAC/F,OAAO,EAAEoG,QAAQ,CAAC;MAChD;IACF;IAKA,MAAM7C,KAAK,GAAG,CAACxD,YAAY,CAACC,OAAO,CAAC,IAAIA,OAAO,CAACC,QAAQ,CAACU,MAAM;IAM/D,MAAM2C,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC8C,QAAQ,EAAE7C,KAAK,CAAC;IAE7C,IAAI9E,WAAC,CAACmC,YAAY,CAAC0C,OAAO,CAAC,EAAE;MAG3B8C,QAAQ,GAAG9C,OAAO;IACpB,CAAC,MAAM;MACL8C,QAAQ,GAAG,IAAI,CAAClH,KAAK,CAAC2E,gCAAgC,CAACuC,QAAQ,CAAC;MAChE,IAAI,CAACjF,WAAW,CAACkF,GAAG,CAACD,QAAQ,CAAC1G,IAAI,CAAC;MACnC,IAAI,CAAC0B,KAAK,CAAC0B,IAAI,CAAC,IAAI,CAACF,wBAAwB,CAACwD,QAAQ,EAAE9C,OAAO,CAAC,CAAC;IACnE;IAEA,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpF,OAAO,CAACC,QAAQ,CAACU,MAAM,EAAEyE,CAAC,EAAE,EAAE;MAChD,MAAMlF,IAAI,GAAGF,OAAO,CAACC,QAAQ,CAACmF,CAAC,CAAC;MAGhC,IAAI,CAAClF,IAAI,EAAE;MAEX,IAAIoG,OAAO;MAEX,IAAI7H,WAAC,CAAC0B,aAAa,CAACD,IAAI,CAAC,EAAE;QACzBoG,OAAO,GAAG,IAAI,CAAChD,OAAO,CAAC8C,QAAQ,CAAC;QAChCE,OAAO,GAAG7H,WAAC,CAACuG,cAAc,CACxBvG,WAAC,CAACoD,gBAAgB,CAACyE,OAAO,EAAE7H,WAAC,CAACqD,UAAU,CAAC,OAAO,CAAC,CAAC,EAClD,CAACrD,WAAC,CAAC8H,cAAc,CAACnB,CAAC,CAAC,CACtB,CAAC;QAID,IAAI,CAACtC,IAAI,CAAC5C,IAAI,CAACrB,QAAQ,EAAEyH,OAAO,CAAC;MACnC,CAAC,MAAM;QACLA,OAAO,GAAG7H,WAAC,CAACoD,gBAAgB,CAACuE,QAAQ,EAAE3H,WAAC,CAAC8H,cAAc,CAACnB,CAAC,CAAC,EAAE,IAAI,CAAC;QACjE,IAAI,CAACtC,IAAI,CAAC5C,IAAI,EAAEoG,OAAO,CAAC;MAC1B;IACF;EACF;EAEArE,IAAIA,CAACjC,OAAe,EAAEwG,GAAiB,EAAE;IAIvC,IAAI,CAAC/H,WAAC,CAACgH,iBAAiB,CAACe,GAAG,CAAC,IAAI,CAAC/H,WAAC,CAAC0D,kBAAkB,CAACqE,GAAG,CAAC,EAAE;MAC3D,MAAMC,IAAI,GAAG,IAAI,CAACvH,KAAK,CAACwH,qBAAqB,CAACF,GAAG,EAAE,IAAI,CAAC;MACxD,IAAIC,IAAI,EAAE;QACR,IAAI,CAACrF,KAAK,CAAC0B,IAAI,CAAC,IAAI,CAACF,wBAAwB,CAAC6D,IAAI,EAAEhI,WAAC,CAAC6D,SAAS,CAACkE,GAAG,CAAC,CAAC,CAAC;QACtEA,GAAG,GAAGC,IAAI;MACZ;IACF;IAEA,IAAI,CAAC3D,IAAI,CAAC9C,OAAO,EAAEwG,GAAG,CAAC;IAEvB,OAAO,IAAI,CAACpF,KAAK;EACnB;AACF;AAACuF,OAAA,CAAA5F,wBAAA,GAAAA,wBAAA;AAOM,SAAS0D,wBAAwBA,CACtCmC,YAAiB,EACjBvC,MAAoB,EACpBnF,KAAY,EACZwC,SAA4B,EAC5BF,mBAA4B,EAC5BC,WAAoB,EACF;EAGlB,MAAMjC,IAAI,GAAG,EAAE;EACf,IAAIqH,UAAU,GAAG,IAAI;EACrB,IAAIC,kBAAkB,GAAG,KAAK;EAC9B,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,YAAY,CAACjG,MAAM,EAAEyE,CAAC,EAAE,EAAE;IAC5C,MAAM9E,IAAI,GAAGsG,YAAY,CAACxB,CAAC,CAAC;IAC5B,MAAMN,GAAG,GAAGxE,IAAI,CAACwE,GAAG;IACpB,IAAIrG,WAAC,CAACmC,YAAY,CAACkE,GAAG,CAAC,IAAI,CAACxE,IAAI,CAACyE,QAAQ,EAAE;MACzCvF,IAAI,CAACsD,IAAI,CAACrE,WAAC,CAACsI,aAAa,CAACjC,GAAG,CAACpF,IAAI,CAAC,CAAC;IACtC,CAAC,MAAM,IAAIjB,WAAC,CAACuI,iBAAiB,CAAClC,GAAG,CAAC,EAAE;MACnCtF,IAAI,CAACsD,IAAI,CAACrE,WAAC,CAAC6D,SAAS,CAACwC,GAAG,CAAC,CAAC;MAC3BgC,kBAAkB,GAAG,IAAI;IAC3B,CAAC,MAAM,IAAIrI,WAAC,CAACoG,SAAS,CAACC,GAAG,CAAC,EAAE;MAE3BtF,IAAI,CAACsD,IAAI,CAACrE,WAAC,CAACsI,aAAa,CAACE,MAAM,CAACnC,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC;IAC/C,CAAC,MAAM,IAAI/F,WAAC,CAACyI,aAAa,CAACpC,GAAG,CAAC,EAAE,CAEjC,CAAC,MAAM;MACLtF,IAAI,CAACsD,IAAI,CAACrE,WAAC,CAAC6D,SAAS,CAACwC,GAAG,CAAC,CAAC;MAC3B+B,UAAU,GAAG,KAAK;IACpB;EACF;EAEA,IAAIrC,KAAK;EACT,IAAIhF,IAAI,CAACmB,MAAM,KAAK,CAAC,EAAE;IACrB,MAAMwG,aAAa,GAAG1F,WAAW,GAC7BhD,WAAC,CAACoD,gBAAgB,CAACpD,WAAC,CAACqD,UAAU,CAAC,QAAQ,CAAC,EAAErD,WAAC,CAACqD,UAAU,CAAC,QAAQ,CAAC,CAAC,GAClEJ,SAAS,CAAC,SAAS,CAAC;IACxB8C,KAAK,GAAG/F,WAAC,CAACuG,cAAc,CAACmC,aAAa,EAAE,CACtC1I,WAAC,CAAC2I,gBAAgB,CAAC,EAAE,CAAC,EACtB3I,WAAC,CAAC4I,kBAAkB,CAAC,CACnB5I,WAAC,CAACuG,cAAc,CAACtD,SAAS,CAAC,0BAA0B,CAAC,EAAE,CACtDjD,WAAC,CAAC6D,SAAS,CAAC+B,MAAM,CAAC,CACpB,CAAC,EACF5F,WAAC,CAAC6D,SAAS,CAAC+B,MAAM,CAAC,CACpB,CAAC,CACH,CAAC;EACJ,CAAC,MAAM;IACL,IAAIiD,aAA2B,GAAG7I,WAAC,CAACyH,eAAe,CAAC1G,IAAI,CAAC;IAEzD,IAAI,CAACqH,UAAU,EAAE;MACfS,aAAa,GAAG7I,WAAC,CAACuG,cAAc,CAC9BvG,WAAC,CAACoD,gBAAgB,CAACyF,aAAa,EAAE7I,WAAC,CAACqD,UAAU,CAAC,KAAK,CAAC,CAAC,EACtD,CAACJ,SAAS,CAAC,eAAe,CAAC,CAC7B,CAAC;IACH,CAAC,MAAM,IAAI,CAACoF,kBAAkB,IAAI,CAACrI,WAAC,CAAC8I,SAAS,CAACrI,KAAK,CAACsI,KAAK,CAAC,EAAE;MAE3D,MAAMC,YAAY,GAAGvI,KAAK,CAACwI,gBAAgB,CAAC,CAAC;MAC7C,MAAM1F,EAAE,GAAGyF,YAAY,CAACtD,qBAAqB,CAAC,UAAU,CAAC;MAEzDsD,YAAY,CAAC3E,IAAI,CAAC;QAChBd,EAAE;QACFC,IAAI,EAAEqF,aAAa;QACnBjG,IAAI,EAAE;MACR,CAAC,CAAC;MAEFiG,aAAa,GAAG7I,WAAC,CAAC6D,SAAS,CAACN,EAAE,CAAC;IACjC;IAEAwC,KAAK,GAAG/F,WAAC,CAACuG,cAAc,CACtBtD,SAAS,CAAE,0BAAyBF,mBAAmB,GAAG,OAAO,GAAG,EAAG,EAAC,CAAC,EACzE,CAAC/C,WAAC,CAAC6D,SAAS,CAAC+B,MAAM,CAAC,EAAEiD,aAAa,CACrC,CAAC;EACH;EACA,OAAO9C,KAAK;AACd;AAEO,SAASmD,0BAA0BA,CACxCC,IAAqC,EACrClG,SAA4B,EAC5BH,mBAA4B,EAC5BD,eAAwB,EACxBE,mBAA4B,EAC5BC,WAAoB,EACpB;EACA,MAAM;IAAEjD,IAAI;IAAEU;EAAM,CAAC,GAAG0I,IAAI;EAE5B,MAAMC,QAAQ,GAAGrJ,IAAI,CAAC6C,IAAI;EAC1B,MAAMyG,OAAO,GAAGtJ,IAAI,CAACuJ,GAAG;EACxB,MAAM3G,KAAK,GAAG,EAAE;EAEhB,KAAK,IAAIgE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5G,IAAI,CAACwJ,YAAY,CAACrH,MAAM,EAAEyE,CAAC,EAAE,EAAE;IACjD,MAAMvC,MAAM,GAAGrE,IAAI,CAACwJ,YAAY,CAAC5C,CAAC,CAAC;IAEnC,MAAMlB,SAAS,GAAGrB,MAAM,CAACZ,IAAI;IAC7B,MAAMjC,OAAO,GAAG6C,MAAM,CAACb,EAAE;IAEzB,MAAMiG,aAAuC,GAC3C,IAAIlH,wBAAwB,CAAC;MAE3BG,UAAU,EAAE1C,IAAI,CAACmE,WAAW;MAC5BvB,KAAK,EAAEA,KAAK;MACZlC,KAAK,EAAEA,KAAK;MACZmC,IAAI,EAAE7C,IAAI,CAAC6C,IAAI;MACfC,eAAe;MACfC,mBAAmB;MACnBE,WAAW;MACXD,mBAAmB;MACnBE;IACF,CAAC,CAAC;IAEJ,IAAIjD,WAAC,CAACwF,SAAS,CAACjE,OAAO,CAAC,EAAE;MACxBiI,aAAa,CAAChG,IAAI,CAACjC,OAAO,EAAEkE,SAAS,CAAC;MAEtC,IAAI,CAACkB,CAAC,KAAK5G,IAAI,CAACwJ,YAAY,CAACrH,MAAM,GAAG,CAAC,EAAE;QAGvClC,WAAC,CAACyJ,QAAQ,CAAC9G,KAAK,CAACA,KAAK,CAACT,MAAM,GAAG,CAAC,CAAC,EAAEkC,MAAM,CAAC;MAC7C;IACF,CAAC,MAAM;MACLzB,KAAK,CAAC0B,IAAI,CACRrE,WAAC,CAACyJ,QAAQ,CACRD,aAAa,CAAClG,uBAAuB,CAAC/B,OAAO,EAAEkE,SAAS,CAAC,EACzDrB,MACF,CACF,CAAC;IACH;EACF;EAEA,IAAIsF,IAAkC,GAAG,IAAI;EAC7C,IAAIC,QAAQ,GAAG,EAAE;EACjB,KAAK,MAAM5J,IAAI,IAAI4C,KAAK,EAAE;IACxB,IAAI3C,WAAC,CAAC4J,qBAAqB,CAAC7J,IAAI,CAAC,EAAE;MACjC,IAAI2J,IAAI,KAAK,IAAI,EAAE;QAEjBA,IAAI,CAACH,YAAY,CAAClF,IAAI,CAAC,GAAGtE,IAAI,CAACwJ,YAAY,CAAC;QAC5C;MACF,CAAC,MAAM;QAELxJ,IAAI,CAAC6C,IAAI,GAAGwG,QAAQ;QACpBM,IAAI,GAAG3J,IAAI;MACb;IACF,CAAC,MAAM;MACL2J,IAAI,GAAG,IAAI;IACb;IAEA,IAAI,CAAC3J,IAAI,CAACuJ,GAAG,EAAE;MACbvJ,IAAI,CAACuJ,GAAG,GAAGD,OAAO;IACpB;IACAM,QAAQ,CAACtF,IAAI,CAACtE,IAAI,CAAC;EACrB;EAEA,IACE4J,QAAQ,CAACzH,MAAM,KAAK,CAAC,IACrBlC,WAAC,CAAC4J,qBAAqB,CAACD,QAAQ,CAAC,CAAC,CAAC,CAAC,IACpC3J,WAAC,CAAC6J,qBAAqB,CAACF,QAAQ,CAAC,CAAC,CAAC,CAAC,IACpC3J,WAAC,CAACkH,gBAAgB,CAACyC,QAAQ,CAAC,CAAC,CAAC,CAACG,UAAU,CAAC,IAC1CH,QAAQ,CAAC,CAAC,CAAC,CAACJ,YAAY,CAACrH,MAAM,KAAK,CAAC,EACrC;IAOA,MAAM6H,IAAI,GAAGJ,QAAQ,CAAC,CAAC,CAAC,CAACG,UAAU;IACnCC,IAAI,CAACC,SAAS,GAAG,CAACL,QAAQ,CAAC,CAAC,CAAC,CAACJ,YAAY,CAAC,CAAC,CAAC,CAAC/F,IAAI,CAAC;IACnDmG,QAAQ,GAAG,CAACI,IAAI,CAAC;EACnB,CAAC,MAAM;IAEL,IACE/J,WAAC,CAACiK,cAAc,CAACd,IAAI,CAACe,MAAM,EAAE;MAAE1G,IAAI,EAAEzD;IAAK,CAAC,CAAC,IAC7C,CAAC4J,QAAQ,CAAC3I,IAAI,CAACmJ,CAAC,IAAInK,WAAC,CAAC4J,qBAAqB,CAACO,CAAC,CAAC,CAAC,EAC/C;MACA,KAAK,IAAIxD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgD,QAAQ,CAACzH,MAAM,EAAEyE,CAAC,EAAE,EAAE;QACxC,MAAM5G,IAAY,GAAG4J,QAAQ,CAAChD,CAAC,CAAC;QAChC,IAAI3G,WAAC,CAAC6J,qBAAqB,CAAC9J,IAAI,CAAC,EAAE;UACjC4J,QAAQ,CAAChD,CAAC,CAAC,GAAG5G,IAAI,CAAC+J,UAAU;QAC/B;MACF;IACF;EACF;EAEA,IAAIH,QAAQ,CAACzH,MAAM,KAAK,CAAC,EAAE;IACzBiH,IAAI,CAACiB,WAAW,CAACT,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC/B,CAAC,MAAM;IACLR,IAAI,CAACkB,mBAAmB,CAACV,QAAQ,CAAC;EACpC;EACAlJ,KAAK,CAAC6J,KAAK,CAAC,CAAC;AACf;AAEO,SAASC,2BAA2BA,CACzCpB,IAAsC,EACtClG,SAA4B,EAC5BH,mBAA4B,EAC5BD,eAAwB,EACxBE,mBAA4B,EAC5BC,WAAoB,EACpB;EACA,MAAM;IAAEjD,IAAI;IAAEU,KAAK;IAAE+J;EAAW,CAAC,GAAGrB,IAAI;EAExC,MAAMxG,KAAqC,GAAG,EAAE;EAEhD,MAAM6G,aAAa,GAAG,IAAIlH,wBAAwB,CAAC;IACjDpC,QAAQ,EAAEH,IAAI,CAACG,QAAQ;IACvBO,KAAK,EAAEA,KAAK;IACZkC,KAAK,EAAEA,KAAK;IACZG,mBAAmB;IACnBD,eAAe;IACfE,mBAAmB;IACnBC,WAAW;IACXC;EACF,CAAC,CAAC;EAEF,IAAI8E,GAAwB;EAC5B,IACG,CAACyC,UAAU,CAACX,qBAAqB,CAAC,CAAC,IAClC,CAACW,UAAU,CAACC,oBAAoB,CAAC,CAAC,IACpCtB,IAAI,CAACuB,kBAAkB,CAAC,CAAC,EACzB;IACA3C,GAAG,GAAGtH,KAAK,CAAC2E,gCAAgC,CAACrF,IAAI,CAACkF,KAAK,EAAE,KAAK,CAAC;IAE/DtC,KAAK,CAAC0B,IAAI,CACRrE,WAAC,CAACgE,mBAAmB,CAAC,KAAK,EAAE,CAAChE,WAAC,CAACiE,kBAAkB,CAAC8D,GAAG,EAAEhI,IAAI,CAACkF,KAAK,CAAC,CAAC,CACtE,CAAC;IAED,IAAIjF,WAAC,CAACgH,iBAAiB,CAACjH,IAAI,CAACkF,KAAK,CAAC,EAAE;MACnCuE,aAAa,CAAC9G,WAAW,CAACkF,GAAG,CAACG,GAAG,CAAC9G,IAAI,CAAC;IACzC;EACF;EAEAuI,aAAa,CAAChG,IAAI,CAACzD,IAAI,CAACiF,IAAI,EAAE+C,GAAG,IAAIhI,IAAI,CAACkF,KAAK,CAAC;EAEhD,IAAI8C,GAAG,EAAE;IACP,IAAIyC,UAAU,CAACG,yBAAyB,CAAC,CAAC,EAAE;MAC1CxB,IAAI,CAACiB,WAAW,CAACpK,WAAC,CAACoB,cAAc,CAAC,EAAE,CAAC,CAAC;MACtCuB,KAAK,CAAC0B,IAAI,CAACrE,WAAC,CAAC4K,eAAe,CAAC5K,WAAC,CAAC6D,SAAS,CAACkE,GAAG,CAAC,CAAC,CAAC;IACjD,CAAC,MAAM;MACLpF,KAAK,CAAC0B,IAAI,CAACrE,WAAC,CAAC2D,mBAAmB,CAAC3D,WAAC,CAAC6D,SAAS,CAACkE,GAAG,CAAC,CAAC,CAAC;IACrD;EACF;EAEAoB,IAAI,CAACkB,mBAAmB,CAAC1H,KAAK,CAAC;EAC/BlC,KAAK,CAAC6J,KAAK,CAAC,CAAC;AACf"}