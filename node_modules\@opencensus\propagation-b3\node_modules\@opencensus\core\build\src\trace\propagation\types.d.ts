/**
 * Copyright 2018, OpenCensus Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { SpanContext } from '../model/types';
/**
 * An transport and environment neutral API for getting request headers.
 */
export interface HeaderGetter {
    getHeader(name: string): string | string[] | undefined;
}
/**
 * A transport and environment neutral API for setting headers.
 */
export interface HeaderSetter {
    setHeader(name: string, value: string): void;
}
/**
 *  Propagation interface
 */
export interface Propagation {
    extract(getter: HeaderGetter): SpanContext | null;
    inject(setter: <PERSON><PERSON><PERSON><PERSON><PERSON>, spanContext: SpanContext): void;
    generate(): SpanContext;
}
