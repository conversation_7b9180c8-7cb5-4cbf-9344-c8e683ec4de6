"use strict";
/**
 * Copyright 2018, OpenCensus Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * Validates that an object reference passed as a parameter to the calling
 * method is not null.
 *
 * @param {T} reference An object reference.
 * @param {string} errorMessage The exception message to use if the check fails.
 * @returns {T} An object reference.
 */
function validateNotNull(reference, errorMessage) {
    if (reference === null || reference === undefined) {
        throw new Error(`Missing mandatory ${errorMessage} parameter`);
    }
    return reference;
}
exports.validateNotNull = validateNotNull;
/**
 * Validates that an array passed as a parameter doesn't contain null element.
 *
 * @param {T} list The argument list to check for null.
 * @param {string} errorMessage The exception message to use if the check fails.
 */
function validateArrayElementsNotNull(array, errorMessage) {
    const areAllDefined = array.every(element => element !== null && typeof element !== 'undefined');
    if (!areAllDefined) {
        throw new Error(`${errorMessage} elements should not be a NULL`);
    }
}
exports.validateArrayElementsNotNull = validateArrayElementsNotNull;
//# sourceMappingURL=validations.js.map