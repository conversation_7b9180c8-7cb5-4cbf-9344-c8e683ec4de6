{"version": 3, "names": ["_utils", "require", "BABEL_POLYFILL_DEPRECATION", "NO_DIRECT_POLYFILL_IMPORT", "_default", "template", "regenerator", "deprecated", "usage", "name", "visitor", "ImportDeclaration", "path", "src", "getImportSource", "isPolyfillSource", "console", "warn", "replace", "remove", "replaceWithMultiple", "ast", "replaceWith", "Program", "get", "for<PERSON>ach", "bodyPath", "getRequireSource"], "sources": ["../../src/polyfills/babel-polyfill.ts"], "sourcesContent": ["import { getImportSource, getRequireSource, isPolyfillSource } from \"./utils\";\n\nimport type { NodePath } from \"@babel/traverse\";\nimport type * as t from \"@babel/types\";\n\nconst BABEL_POLYFILL_DEPRECATION = `\n  \\`@babel/polyfill\\` is deprecated. Please, use required parts of \\`core-js\\`\n  and \\`regenerator-runtime/runtime\\` separately`;\n\nconst NO_DIRECT_POLYFILL_IMPORT = `\n  When setting \\`useBuiltIns: 'usage'\\`, polyfills are automatically imported when needed.\n  Please remove the direct import of \\`SPECIFIER\\` or use \\`useBuiltIns: 'entry'\\` instead.`;\n\nexport default function (\n  { template }: any,\n  { regenerator, deprecated, usage }: any,\n) {\n  return {\n    name: \"preset-env/replace-babel-polyfill\",\n    visitor: {\n      ImportDeclaration(path: NodePath<t.ImportDeclaration>) {\n        const src = getImportSource(path);\n        if (usage && isPolyfillSource(src)) {\n          console.warn(NO_DIRECT_POLYFILL_IMPORT.replace(\"SPECIFIER\", src));\n          if (!deprecated) path.remove();\n        } else if (src === \"@babel/polyfill\") {\n          if (deprecated) {\n            console.warn(BABEL_POLYFILL_DEPRECATION);\n          } else if (regenerator) {\n            path.replaceWithMultiple(template.ast`\n              import \"core-js\";\n              import \"regenerator-runtime/runtime.js\";\n            `);\n          } else {\n            path.replaceWith(template.ast`\n              import \"core-js\";\n            `);\n          }\n        }\n      },\n      Program(path: NodePath<t.Program>) {\n        path.get(\"body\").forEach(bodyPath => {\n          const src = getRequireSource(bodyPath);\n          if (usage && isPolyfillSource(src)) {\n            console.warn(NO_DIRECT_POLYFILL_IMPORT.replace(\"SPECIFIER\", src));\n            if (!deprecated) bodyPath.remove();\n          } else if (src === \"@babel/polyfill\") {\n            if (deprecated) {\n              console.warn(BABEL_POLYFILL_DEPRECATION);\n            } else if (regenerator) {\n              bodyPath.replaceWithMultiple(template.ast`\n                require(\"core-js\");\n                require(\"regenerator-runtime/runtime.js\");\n              `);\n            } else {\n              bodyPath.replaceWith(template.ast`\n                require(\"core-js\");\n              `);\n            }\n          }\n        });\n      },\n    },\n  };\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAKA,MAAMC,0BAA0B,GAAI;AACpC;AACA,iDAAiD;AAEjD,MAAMC,yBAAyB,GAAI;AACnC;AACA,4FAA4F;AAE7E,SAAAC,SACb;EAAEC;AAAc,CAAC,EACjB;EAAEC,WAAW;EAAEC,UAAU;EAAEC;AAAW,CAAC,EACvC;EACA,OAAO;IACLC,IAAI,EAAE,mCAAmC;IACzCC,OAAO,EAAE;MACPC,iBAAiBA,CAACC,IAAmC,EAAE;QACrD,MAAMC,GAAG,GAAG,IAAAC,sBAAe,EAACF,IAAI,CAAC;QACjC,IAAIJ,KAAK,IAAI,IAAAO,uBAAgB,EAACF,GAAG,CAAC,EAAE;UAClCG,OAAO,CAACC,IAAI,CAACd,yBAAyB,CAACe,OAAO,CAAC,WAAW,EAAEL,GAAG,CAAC,CAAC;UACjE,IAAI,CAACN,UAAU,EAAEK,IAAI,CAACO,MAAM,CAAC,CAAC;QAChC,CAAC,MAAM,IAAIN,GAAG,KAAK,iBAAiB,EAAE;UACpC,IAAIN,UAAU,EAAE;YACdS,OAAO,CAACC,IAAI,CAACf,0BAA0B,CAAC;UAC1C,CAAC,MAAM,IAAII,WAAW,EAAE;YACtBM,IAAI,CAACQ,mBAAmB,CAACf,QAAQ,CAACgB,GAAI;AAClD;AACA;AACA,aAAa,CAAC;UACJ,CAAC,MAAM;YACLT,IAAI,CAACU,WAAW,CAACjB,QAAQ,CAACgB,GAAI;AAC1C;AACA,aAAa,CAAC;UACJ;QACF;MACF,CAAC;MACDE,OAAOA,CAACX,IAAyB,EAAE;QACjCA,IAAI,CAACY,GAAG,CAAC,MAAM,CAAC,CAACC,OAAO,CAACC,QAAQ,IAAI;UACnC,MAAMb,GAAG,GAAG,IAAAc,uBAAgB,EAACD,QAAQ,CAAC;UACtC,IAAIlB,KAAK,IAAI,IAAAO,uBAAgB,EAACF,GAAG,CAAC,EAAE;YAClCG,OAAO,CAACC,IAAI,CAACd,yBAAyB,CAACe,OAAO,CAAC,WAAW,EAAEL,GAAG,CAAC,CAAC;YACjE,IAAI,CAACN,UAAU,EAAEmB,QAAQ,CAACP,MAAM,CAAC,CAAC;UACpC,CAAC,MAAM,IAAIN,GAAG,KAAK,iBAAiB,EAAE;YACpC,IAAIN,UAAU,EAAE;cACdS,OAAO,CAACC,IAAI,CAACf,0BAA0B,CAAC;YAC1C,CAAC,MAAM,IAAII,WAAW,EAAE;cACtBoB,QAAQ,CAACN,mBAAmB,CAACf,QAAQ,CAACgB,GAAI;AACxD;AACA;AACA,eAAe,CAAC;YACJ,CAAC,MAAM;cACLK,QAAQ,CAACJ,WAAW,CAACjB,QAAQ,CAACgB,GAAI;AAChD;AACA,eAAe,CAAC;YACJ;UACF;QACF,CAAC,CAAC;MACJ;IACF;EACF,CAAC;AACH"}