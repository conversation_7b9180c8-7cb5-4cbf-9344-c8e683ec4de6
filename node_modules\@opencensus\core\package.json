{"_from": "@opencensus/core@0.0.9", "_id": "@opencensus/core@0.0.9", "_inBundle": false, "_integrity": "sha512-31Q4VWtbzXpVUd2m9JS6HEaPjlKvNMOiF7lWKNmXF84yUcgfAFL5re7/hjDmdyQbOp32oGc+RFV78jXIldVz6Q==", "_location": "/@opencensus/core", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@opencensus/core@0.0.9", "name": "@opencensus/core", "escapedName": "@opencensus%2fcore", "scope": "@opencensus", "rawSpec": "0.0.9", "saveSpec": null, "fetchSpec": "0.0.9"}, "_requiredBy": ["/@pm2/io"], "_resolved": "https://registry.npmjs.org/@opencensus/core/-/core-0.0.9.tgz", "_shasum": "b16f775435ee309433e4126af194d37313fc93b3", "_spec": "@opencensus/core@0.0.9", "_where": "/var/www/html/node_modules/@pm2/io", "author": {"name": "Google Inc."}, "bugs": {"url": "https://github.com/census-instrumentation/opencensus-node/issues"}, "bundleDependencies": false, "dependencies": {"continuation-local-storage": "^3.2.1", "log-driver": "^1.2.7", "semver": "^5.5.0", "shimmer": "^1.2.0", "uuid": "^3.2.1"}, "deprecated": false, "description": "OpenCensus is a toolkit for collecting application performance and behavior data.", "devDependencies": {"@types/continuation-local-storage": "^3.2.1", "@types/mocha": "^5.2.5", "@types/node": "^10.12.12", "@types/once": "^1.4.0", "@types/semver": "^5.5.0", "@types/shimmer": "^1.0.1", "@types/uuid": "^3.4.3", "codecov": "^3.1.0", "gts": "^0.9.0", "intercept-stdout": "^0.1.2", "mocha": "^5.0.4", "ncp": "^2.0.0", "nyc": "13.1.0", "ts-node": "^7.0.1", "typescript": "~2.9.0"}, "engines": {"node": ">=6.0"}, "files": ["build/src/**/*.js", "build/src/**/*.d.ts", "doc", "CHANGELOG.md", "LICENSE", "README.md"], "homepage": "https://github.com/census-instrumentation/opencensus-node#readme", "keywords": ["opencensus", "nodejs", "tracing", "profiling", "metrics", "stats"], "license": "Apache-2.0", "main": "build/src/index.js", "name": "@opencensus/core", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/census-instrumentation/opencensus-node.git"}, "scripts": {"check": "gts check", "clean": "rimraf build/*", "codecov": "nyc report --reporter=json && codecov -f coverage/*.json", "compile": "tsc -p .", "compile:release": "tsc -p tsconfig-release.json", "fix": "gts fix", "posttest": "npm run check", "prepare": "npm run compile:release", "pretest": "npm run compile", "test": "nyc mocha build/test/**/*.js"}, "types": "build/src/index.d.ts", "version": "0.0.9"}