{"_from": "@opencensus/core@^0.0.8", "_id": "@opencensus/core@0.0.8", "_inBundle": false, "_integrity": "sha512-yUFT59SFhGMYQgX0PhoTR0LBff2BEhPrD9io1jWfF/VDbakRfs6Pq60rjv0Z7iaTav5gQlttJCX2+VPxFWCuoQ==", "_location": "/@opencensus/propagation-b3/@opencensus/core", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@opencensus/core@^0.0.8", "name": "@opencensus/core", "escapedName": "@opencensus%2fcore", "scope": "@opencensus", "rawSpec": "^0.0.8", "saveSpec": null, "fetchSpec": "^0.0.8"}, "_requiredBy": ["/@opencensus/propagation-b3"], "_resolved": "https://registry.npmjs.org/@opencensus/core/-/core-0.0.8.tgz", "_shasum": "df01f200c2d2fbfe14dae129a1a86fb87286db92", "_spec": "@opencensus/core@^0.0.8", "_where": "/var/www/html/node_modules/@opencensus/propagation-b3", "author": {"name": "Google Inc."}, "bugs": {"url": "https://github.com/census-instrumentation/opencensus-node/issues"}, "bundleDependencies": false, "dependencies": {"continuation-local-storage": "^3.2.1", "log-driver": "^1.2.7", "semver": "^5.5.0", "shimmer": "^1.2.0", "uuid": "^3.2.1"}, "deprecated": false, "description": "OpenCensus is a toolkit for collecting application performance and behavior data.", "devDependencies": {"@types/continuation-local-storage": "^3.2.1", "@types/mocha": "^5.2.5", "@types/node": "^10.12.12", "@types/once": "^1.4.0", "@types/semver": "^5.5.0", "@types/shimmer": "^1.0.1", "@types/uuid": "^3.4.3", "gts": "^0.9.0", "intercept-stdout": "^0.1.2", "mocha": "^5.0.4", "ncp": "^2.0.0", "nyc": "11.6.0", "ts-node": "^7.0.1", "typescript": "~2.6.1"}, "engines": {"node": ">=6.0"}, "files": ["build/src/**/*.js", "build/src/**/*.d.ts", "doc", "CHANGELOG.md", "LICENSE", "README.md"], "homepage": "https://github.com/census-instrumentation/opencensus-node#readme", "keywords": ["opencensus", "nodejs", "tracing", "profiling"], "license": "Apache-2.0", "main": "build/src/index.js", "name": "@opencensus/core", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/census-instrumentation/opencensus-node.git"}, "scripts": {"check": "gts check", "clean": "rimraf build/*", "compile": "tsc -p .", "fix": "gts fix", "posttest": "npm run check", "prepare": "npm run compile", "pretest": "npm run compile", "test": "nyc -x '**/test/**' --reporter=html --reporter=text mocha 'build/test/**/*.js'"}, "types": "build/src/index.d.ts", "version": "0.0.8"}