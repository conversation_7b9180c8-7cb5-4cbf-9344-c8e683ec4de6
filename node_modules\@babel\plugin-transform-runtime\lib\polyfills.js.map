{"version": 3, "names": ["_babelPluginPolyfillCorejs", "require", "_babelPluginPolyfillCorejs2", "_babelPluginPolyfillRegenerator", "pluginCorejs2", "_pluginCorejs2", "default", "pluginCorejs3", "_pluginCorejs3", "pluginRegenerator", "_pluginRegenerator", "pluginsCompat", "createCorejsPlugin", "plugin", "options", "regeneratorPlugin", "api", "_", "filename", "Object", "assign", "inherits", "createRegeneratorPlugin", "useRuntimeRegenerator", "undefined", "createBasePolyfillsPlugin", "corejs", "regenerator", "runtimeVersion", "absoluteImports", "proposals", "rawVersion", "version", "Boolean", "corejsVersion", "Number", "includes", "Error", "JSON", "stringify", "polyfillOpts", "method", "useBabelRuntime", "ext"], "sources": ["../src/polyfills.ts"], "sourcesContent": ["// TODO(Babel 8): Remove at least support for babel-plugin-polyfill-regenerator,\n// which isn't needed anymore, and babel-plugin-polyfill-corejs2, since core-js\n// 2 isn't maintained anymore.\n// Consider also removing babel-plugin-polyfill-corejs3 from here, and ask users\n// to explicitly enable it in their Babel configuration files.\n\nimport type { PluginAPI, PluginObject } from \"@babel/core\";\nimport _pluginCorejs2 from \"babel-plugin-polyfill-corejs2\";\nimport _pluginCorejs3 from \"babel-plugin-polyfill-corejs3\";\nimport _pluginRegenerator from \"babel-plugin-polyfill-regenerator\";\nconst pluginCorejs2 = (_pluginCorejs2.default ||\n  _pluginCorejs2) as typeof _pluginCorejs2.default;\nconst pluginCorejs3 = (_pluginCorejs3.default ||\n  _pluginCorejs3) as typeof _pluginCorejs3.default;\nconst pluginRegenerator = (_pluginRegenerator.default ||\n  _pluginRegenerator) as typeof _pluginRegenerator.default;\n\nimport type { Options } from \"./index\";\n\nconst pluginsCompat = \"#__secret_key__@babel/runtime__compatibility\";\n\ninterface CoreJS2PluginOptions {\n  absoluteImports: boolean;\n  method: \"usage-pure\";\n  [pluginsCompat]: {\n    runtimeVersion: string;\n    useBabelRuntime: boolean;\n    ext: string;\n  };\n}\n\ninterface RegeneratorPluginOptions {\n  absoluteImports: boolean;\n  method: \"usage-pure\";\n  [pluginsCompat]: {\n    useBabelRuntime: boolean;\n  };\n}\n\ninterface CoreJS3PluginOptions {\n  absoluteImports: boolean;\n  method: \"usage-pure\";\n  proposals: boolean;\n  version: number;\n  [pluginsCompat]: {\n    useBabelRuntime: boolean;\n    ext: string;\n  };\n}\n\nfunction createCorejsPlugin<Options extends {}>(\n  plugin: (api: PluginAPI, options: Options, filename: string) => PluginObject,\n  options: Options,\n  regeneratorPlugin: (\n    api: PluginAPI,\n    options: RegeneratorPluginOptions,\n    filename: string,\n  ) => PluginObject,\n): (api: PluginAPI, options: {}, filename: string) => PluginObject {\n  return (api: PluginAPI, _: {}, filename: string) => {\n    return {\n      ...plugin(api, options, filename),\n      inherits: regeneratorPlugin,\n    };\n  };\n}\n\nfunction createRegeneratorPlugin(\n  options: RegeneratorPluginOptions,\n  useRuntimeRegenerator: boolean,\n): (\n  api: PluginAPI,\n  options: RegeneratorPluginOptions,\n  filename: string,\n) => PluginObject {\n  if (!useRuntimeRegenerator) return undefined;\n  return (api, _, filename) => {\n    return pluginRegenerator(api, options, filename);\n  };\n}\n\nexport function createBasePolyfillsPlugin(\n  { corejs, regenerator: useRuntimeRegenerator = true }: Options,\n  runtimeVersion: string,\n  absoluteImports: boolean,\n) {\n  let proposals = false;\n  let rawVersion;\n\n  if (typeof corejs === \"object\" && corejs !== null) {\n    rawVersion = corejs.version;\n    proposals = Boolean(corejs.proposals);\n  } else {\n    rawVersion = corejs;\n  }\n\n  const corejsVersion = rawVersion ? Number(rawVersion) : false;\n\n  if (![false, 2, 3].includes(corejsVersion)) {\n    throw new Error(\n      `The \\`core-js\\` version must be false, 2 or 3, but got ${JSON.stringify(\n        rawVersion,\n      )}.`,\n    );\n  }\n\n  if (proposals && (!corejsVersion || corejsVersion < 3)) {\n    throw new Error(\n      \"The 'proposals' option is only supported when using 'corejs: 3'\",\n    );\n  }\n\n  if (typeof useRuntimeRegenerator !== \"boolean\") {\n    throw new Error(\n      \"The 'regenerator' option must be undefined, or a boolean.\",\n    );\n  }\n\n  const polyfillOpts = {\n    method: \"usage-pure\",\n    absoluteImports,\n    [pluginsCompat]: { useBabelRuntime: true, runtimeVersion, ext: \"\" },\n  } as const;\n\n  return corejsVersion === 2\n    ? createCorejsPlugin<CoreJS2PluginOptions>(\n        pluginCorejs2,\n        polyfillOpts,\n        createRegeneratorPlugin(polyfillOpts, useRuntimeRegenerator),\n      )\n    : corejsVersion === 3\n    ? createCorejsPlugin<CoreJS3PluginOptions>(\n        pluginCorejs3,\n        { version: 3, proposals, ...polyfillOpts },\n        createRegeneratorPlugin(polyfillOpts, useRuntimeRegenerator),\n      )\n    : createRegeneratorPlugin(polyfillOpts, useRuntimeRegenerator);\n}\n"], "mappings": ";;;;;;AAOA,IAAAA,0BAAA,GAAAC,OAAA;AACA,IAAAC,2BAAA,GAAAD,OAAA;AACA,IAAAE,+BAAA,GAAAF,OAAA;AACA,MAAMG,aAAa,GAAIC,0BAAc,CAACC,OAAO,IAC3CD,0BAAgD;AAClD,MAAME,aAAa,GAAIC,2BAAc,CAACF,OAAO,IAC3CE,2BAAgD;AAClD,MAAMC,iBAAiB,GAAIC,+BAAkB,CAACJ,OAAO,IACnDI,+BAAwD;AAI1D,MAAMC,aAAa,GAAG,8CAA8C;AA+BpE,SAASC,kBAAkBA,CACzBC,MAA4E,EAC5EC,OAAgB,EAChBC,iBAIiB,EACgD;EACjE,OAAO,CAACC,GAAc,EAAEC,CAAK,EAAEC,QAAgB,KAAK;IAClD,OAAAC,MAAA,CAAAC,MAAA,KACKP,MAAM,CAACG,GAAG,EAAEF,OAAO,EAAEI,QAAQ,CAAC;MACjCG,QAAQ,EAAEN;IAAiB;EAE/B,CAAC;AACH;AAEA,SAASO,uBAAuBA,CAC9BR,OAAiC,EACjCS,qBAA8B,EAKd;EAChB,IAAI,CAACA,qBAAqB,EAAE,OAAOC,SAAS;EAC5C,OAAO,CAACR,GAAG,EAAEC,CAAC,EAAEC,QAAQ,KAAK;IAC3B,OAAOT,iBAAiB,CAACO,GAAG,EAAEF,OAAO,EAAEI,QAAQ,CAAC;EAClD,CAAC;AACH;AAEO,SAASO,yBAAyBA,CACvC;EAAEC,MAAM;EAAEC,WAAW,EAAEJ,qBAAqB,GAAG;AAAc,CAAC,EAC9DK,cAAsB,EACtBC,eAAwB,EACxB;EACA,IAAIC,SAAS,GAAG,KAAK;EACrB,IAAIC,UAAU;EAEd,IAAI,OAAOL,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;IACjDK,UAAU,GAAGL,MAAM,CAACM,OAAO;IAC3BF,SAAS,GAAGG,OAAO,CAACP,MAAM,CAACI,SAAS,CAAC;EACvC,CAAC,MAAM;IACLC,UAAU,GAAGL,MAAM;EACrB;EAEA,MAAMQ,aAAa,GAAGH,UAAU,GAAGI,MAAM,CAACJ,UAAU,CAAC,GAAG,KAAK;EAE7D,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAACK,QAAQ,CAACF,aAAa,CAAC,EAAE;IAC1C,MAAM,IAAIG,KAAK,CACZ,0DAAyDC,IAAI,CAACC,SAAS,CACtER,UACF,CAAE,GACJ,CAAC;EACH;EAEA,IAAID,SAAS,KAAK,CAACI,aAAa,IAAIA,aAAa,GAAG,CAAC,CAAC,EAAE;IACtD,MAAM,IAAIG,KAAK,CACb,iEACF,CAAC;EACH;EAEA,IAAI,OAAOd,qBAAqB,KAAK,SAAS,EAAE;IAC9C,MAAM,IAAIc,KAAK,CACb,2DACF,CAAC;EACH;EAEA,MAAMG,YAAY,GAAG;IACnBC,MAAM,EAAE,YAAY;IACpBZ,eAAe;IACf,CAAClB,aAAa,GAAG;MAAE+B,eAAe,EAAE,IAAI;MAAEd,cAAc;MAAEe,GAAG,EAAE;IAAG;EACpE,CAAU;EAEV,OAAOT,aAAa,KAAK,CAAC,GACtBtB,kBAAkB,CAChBR,aAAa,EACboC,YAAY,EACZlB,uBAAuB,CAACkB,YAAY,EAAEjB,qBAAqB,CAC7D,CAAC,GACDW,aAAa,KAAK,CAAC,GACnBtB,kBAAkB,CAChBL,aAAa,EAAAY,MAAA,CAAAC,MAAA;IACXY,OAAO,EAAE,CAAC;IAAEF;EAAS,GAAKU,YAAY,GACxClB,uBAAuB,CAACkB,YAAY,EAAEjB,qBAAqB,CAC7D,CAAC,GACDD,uBAAuB,CAACkB,YAAY,EAAEjB,qBAAqB,CAAC;AAClE"}