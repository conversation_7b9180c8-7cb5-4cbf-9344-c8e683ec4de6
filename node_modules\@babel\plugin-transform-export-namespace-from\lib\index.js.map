{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_pluginSyntaxExportNamespaceFrom", "_core", "_default", "declare", "api", "assertVersion", "name", "inherits", "syntaxExportNamespaceFrom", "default", "visitor", "ExportNamedDeclaration", "path", "_exported$name", "node", "scope", "specifiers", "index", "t", "isExportDefaultSpecifier", "isExportNamespaceSpecifier", "nodes", "push", "exportNamedDeclaration", "shift", "source", "specifier", "exported", "uid", "generateUidIdentifier", "value", "importDeclaration", "importNamespaceSpecifier", "cloneNode", "exportSpecifier", "length", "replaceWithMultiple", "registerDeclaration", "exports"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport syntaxExportNamespace<PERSON>rom from \"@babel/plugin-syntax-export-namespace-from\";\nimport { types as t } from \"@babel/core\";\n\nexport default declare(api => {\n  api.assertVersion(7);\n\n  return {\n    name: \"transform-export-namespace-from\",\n    inherits: syntaxExportNamespaceFrom.default,\n\n    visitor: {\n      ExportNamedDeclaration(path) {\n        const { node, scope } = path;\n        const { specifiers } = node;\n\n        const index = t.isExportDefaultSpecifier(specifiers[0]) ? 1 : 0;\n        if (!t.isExportNamespaceSpecifier(specifiers[index])) return;\n\n        const nodes = [];\n\n        if (index === 1) {\n          nodes.push(\n            t.exportNamedDeclaration(null, [specifiers.shift()], node.source),\n          );\n        }\n\n        const specifier = specifiers.shift();\n        const { exported } = specifier;\n        const uid = scope.generateUidIdentifier(\n          // @ts-expect-error Identifier ?? StringLiteral\n          exported.name ?? exported.value,\n        );\n\n        nodes.push(\n          t.importDeclaration(\n            [t.importNamespaceSpecifier(uid)],\n            t.cloneNode(node.source),\n          ),\n          t.exportNamedDeclaration(null, [\n            t.exportSpecifier(t.cloneNode(uid), exported),\n          ]),\n        );\n\n        if (node.specifiers.length >= 1) {\n          nodes.push(node);\n        }\n\n        const [importDeclaration] = path.replaceWithMultiple(nodes);\n        path.scope.registerDeclaration(importDeclaration);\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,gCAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAAyC,IAAAG,QAAA,GAE1B,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAC,CAAC,CAAC;EAEpB,OAAO;IACLC,IAAI,EAAE,iCAAiC;IACvCC,QAAQ,EAAEC,gCAAyB,CAACC,OAAO;IAE3CC,OAAO,EAAE;MACPC,sBAAsBA,CAACC,IAAI,EAAE;QAAA,IAAAC,cAAA;QAC3B,MAAM;UAAEC,IAAI;UAAEC;QAAM,CAAC,GAAGH,IAAI;QAC5B,MAAM;UAAEI;QAAW,CAAC,GAAGF,IAAI;QAE3B,MAAMG,KAAK,GAAGC,WAAC,CAACC,wBAAwB,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QAC/D,IAAI,CAACE,WAAC,CAACE,0BAA0B,CAACJ,UAAU,CAACC,KAAK,CAAC,CAAC,EAAE;QAEtD,MAAMI,KAAK,GAAG,EAAE;QAEhB,IAAIJ,KAAK,KAAK,CAAC,EAAE;UACfI,KAAK,CAACC,IAAI,CACRJ,WAAC,CAACK,sBAAsB,CAAC,IAAI,EAAE,CAACP,UAAU,CAACQ,KAAK,CAAC,CAAC,CAAC,EAAEV,IAAI,CAACW,MAAM,CAClE,CAAC;QACH;QAEA,MAAMC,SAAS,GAAGV,UAAU,CAACQ,KAAK,CAAC,CAAC;QACpC,MAAM;UAAEG;QAAS,CAAC,GAAGD,SAAS;QAC9B,MAAME,GAAG,GAAGb,KAAK,CAACc,qBAAqB,EAAAhB,cAAA,GAErCc,QAAQ,CAACrB,IAAI,YAAAO,cAAA,GAAIc,QAAQ,CAACG,KAC5B,CAAC;QAEDT,KAAK,CAACC,IAAI,CACRJ,WAAC,CAACa,iBAAiB,CACjB,CAACb,WAAC,CAACc,wBAAwB,CAACJ,GAAG,CAAC,CAAC,EACjCV,WAAC,CAACe,SAAS,CAACnB,IAAI,CAACW,MAAM,CACzB,CAAC,EACDP,WAAC,CAACK,sBAAsB,CAAC,IAAI,EAAE,CAC7BL,WAAC,CAACgB,eAAe,CAAChB,WAAC,CAACe,SAAS,CAACL,GAAG,CAAC,EAAED,QAAQ,CAAC,CAC9C,CACH,CAAC;QAED,IAAIb,IAAI,CAACE,UAAU,CAACmB,MAAM,IAAI,CAAC,EAAE;UAC/Bd,KAAK,CAACC,IAAI,CAACR,IAAI,CAAC;QAClB;QAEA,MAAM,CAACiB,iBAAiB,CAAC,GAAGnB,IAAI,CAACwB,mBAAmB,CAACf,KAAK,CAAC;QAC3DT,IAAI,CAACG,KAAK,CAACsB,mBAAmB,CAACN,iBAAiB,CAAC;MACnD;IACF;EACF,CAAC;AACH,CAAC,CAAC;AAAAO,OAAA,CAAA7B,OAAA,GAAAP,QAAA"}