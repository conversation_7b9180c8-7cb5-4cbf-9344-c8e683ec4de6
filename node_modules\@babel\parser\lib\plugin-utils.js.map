{"version": 3, "names": ["_estree", "require", "_flow", "_jsx", "_typescript", "_placeholders", "_v8intrinsic", "hasPlugin", "plugins", "expectedConfig", "expectedName", "expectedOptions", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "expectedOptionsIsEmpty", "length", "some", "p", "pluginName", "pluginOptions", "key", "getPluginOption", "name", "option", "plugin", "find", "Array", "isArray", "PIPELINE_PROPOSALS", "TOPIC_TOKENS", "RECORD_AND_TUPLE_SYNTAX_TYPES", "validatePlugins", "Error", "decoratorsBeforeExport", "allowCallParenthesized", "proposal", "includes", "proposalList", "map", "join", "tupleSyntaxIsHash", "syntaxType", "topicToken", "tokenList", "t", "moduleAttributesVersionPluginOption", "error", "missingPlugins", "mixinPlugins", "estree", "jsx", "flow", "typescript", "v8intrinsic", "placeholders", "exports", "mixinPluginNames"], "sources": ["../src/plugin-utils.ts"], "sourcesContent": ["import type Parser from \"./parser\";\nimport type {\n  ParserPluginWithOptions,\n  PluginConfig,\n  PluginOptions,\n} from \"./typings\";\n\nexport type Plugin = PluginConfig;\n\nexport type PluginList = PluginConfig[];\n\nexport type MixinPlugin = (superClass: { new (...args: any): Parser }) => {\n  new (...args: any): Parser;\n};\n\n// This function’s second parameter accepts either a string (plugin name) or an\n// array pair (plugin name and options object). If an options object is given,\n// then each value is non-recursively checked for identity with the actual\n// option value of each plugin in the first argument (which is an array of\n// plugin names or array pairs).\nexport function hasPlugin(\n  plugins: PluginList,\n  expectedConfig: PluginConfig,\n): boolean {\n  // The expectedOptions object is by default an empty object if the given\n  // expectedConfig argument does not give an options object (i.e., if it is a\n  // string).\n  const [expectedName, expectedOptions] =\n    typeof expectedConfig === \"string\" ? [expectedConfig, {}] : expectedConfig;\n\n  const expectedKeys = Object.keys(expectedOptions);\n\n  const expectedOptionsIsEmpty = expectedKeys.length === 0;\n\n  return plugins.some(p => {\n    if (typeof p === \"string\") {\n      return expectedOptionsIsEmpty && p === expectedName;\n    } else {\n      const [pluginName, pluginOptions] = p;\n      if (pluginName !== expectedName) {\n        return false;\n      }\n      for (const key of expectedKeys) {\n        // @ts-expect-error key may not exist in plugin options\n        if (pluginOptions[key] !== expectedOptions[key]) {\n          return false;\n        }\n      }\n      return true;\n    }\n  });\n}\n\nexport function getPluginOption<\n  PluginName extends ParserPluginWithOptions[0],\n  OptionName extends keyof PluginOptions<PluginName>,\n>(plugins: PluginList, name: PluginName, option: OptionName) {\n  const plugin = plugins.find(plugin => {\n    if (Array.isArray(plugin)) {\n      return plugin[0] === name;\n    } else {\n      return plugin === name;\n    }\n  });\n\n  if (plugin && Array.isArray(plugin) && plugin.length > 1) {\n    return (plugin[1] as PluginOptions<PluginName>)[option];\n  }\n\n  return null;\n}\n\nconst PIPELINE_PROPOSALS = [\"minimal\", \"fsharp\", \"hack\", \"smart\"];\nconst TOPIC_TOKENS = [\"^^\", \"@@\", \"^\", \"%\", \"#\"];\nconst RECORD_AND_TUPLE_SYNTAX_TYPES = [\"hash\", \"bar\"];\n\nexport function validatePlugins(plugins: PluginList) {\n  if (hasPlugin(plugins, \"decorators\")) {\n    if (hasPlugin(plugins, \"decorators-legacy\")) {\n      throw new Error(\n        \"Cannot use the decorators and decorators-legacy plugin together\",\n      );\n    }\n\n    const decoratorsBeforeExport = getPluginOption(\n      plugins,\n      \"decorators\",\n      \"decoratorsBeforeExport\",\n    );\n    if (\n      decoratorsBeforeExport != null &&\n      typeof decoratorsBeforeExport !== \"boolean\"\n    ) {\n      throw new Error(\n        \"'decoratorsBeforeExport' must be a boolean, if specified.\",\n      );\n    }\n\n    const allowCallParenthesized = getPluginOption(\n      plugins,\n      \"decorators\",\n      \"allowCallParenthesized\",\n    );\n    if (\n      allowCallParenthesized != null &&\n      typeof allowCallParenthesized !== \"boolean\"\n    ) {\n      throw new Error(\"'allowCallParenthesized' must be a boolean.\");\n    }\n  }\n\n  if (hasPlugin(plugins, \"flow\") && hasPlugin(plugins, \"typescript\")) {\n    throw new Error(\"Cannot combine flow and typescript plugins.\");\n  }\n\n  if (hasPlugin(plugins, \"placeholders\") && hasPlugin(plugins, \"v8intrinsic\")) {\n    throw new Error(\"Cannot combine placeholders and v8intrinsic plugins.\");\n  }\n\n  if (hasPlugin(plugins, \"pipelineOperator\")) {\n    const proposal = getPluginOption(plugins, \"pipelineOperator\", \"proposal\");\n\n    if (!PIPELINE_PROPOSALS.includes(proposal)) {\n      const proposalList = PIPELINE_PROPOSALS.map(p => `\"${p}\"`).join(\", \");\n      throw new Error(\n        `\"pipelineOperator\" requires \"proposal\" option whose value must be one of: ${proposalList}.`,\n      );\n    }\n\n    const tupleSyntaxIsHash = hasPlugin(plugins, [\n      \"recordAndTuple\",\n      { syntaxType: \"hash\" },\n    ]);\n\n    if (proposal === \"hack\") {\n      if (hasPlugin(plugins, \"placeholders\")) {\n        throw new Error(\n          \"Cannot combine placeholders plugin and Hack-style pipes.\",\n        );\n      }\n\n      if (hasPlugin(plugins, \"v8intrinsic\")) {\n        throw new Error(\n          \"Cannot combine v8intrinsic plugin and Hack-style pipes.\",\n        );\n      }\n\n      const topicToken = getPluginOption(\n        plugins,\n        \"pipelineOperator\",\n        \"topicToken\",\n      );\n\n      if (!TOPIC_TOKENS.includes(topicToken)) {\n        const tokenList = TOPIC_TOKENS.map(t => `\"${t}\"`).join(\", \");\n\n        throw new Error(\n          `\"pipelineOperator\" in \"proposal\": \"hack\" mode also requires a \"topicToken\" option whose value must be one of: ${tokenList}.`,\n        );\n      }\n\n      if (topicToken === \"#\" && tupleSyntaxIsHash) {\n        throw new Error(\n          'Plugin conflict between `[\"pipelineOperator\", { proposal: \"hack\", topicToken: \"#\" }]` and `[\"recordAndtuple\", { syntaxType: \"hash\"}]`.',\n        );\n      }\n    } else if (proposal === \"smart\" && tupleSyntaxIsHash) {\n      throw new Error(\n        'Plugin conflict between `[\"pipelineOperator\", { proposal: \"smart\" }]` and `[\"recordAndtuple\", { syntaxType: \"hash\"}]`.',\n      );\n    }\n  }\n\n  if (hasPlugin(plugins, \"moduleAttributes\")) {\n    if (process.env.BABEL_8_BREAKING) {\n      throw new Error(\n        \"`moduleAttributes` has been removed in Babel 8, please use `importAttributes` parser plugin, or `@babel/plugin-syntax-import-attributes`.\",\n      );\n    } else {\n      if (\n        hasPlugin(plugins, \"importAssertions\") ||\n        hasPlugin(plugins, \"importAttributes\")\n      ) {\n        throw new Error(\n          \"Cannot combine importAssertions, importAttributes and moduleAttributes plugins.\",\n        );\n      }\n      const moduleAttributesVersionPluginOption = getPluginOption(\n        plugins,\n        \"moduleAttributes\",\n        \"version\",\n      );\n      if (moduleAttributesVersionPluginOption !== \"may-2020\") {\n        throw new Error(\n          \"The 'moduleAttributes' plugin requires a 'version' option,\" +\n            \" representing the last proposal update. Currently, the\" +\n            \" only supported value is 'may-2020'.\",\n        );\n      }\n    }\n  }\n  if (\n    hasPlugin(plugins, \"importAssertions\") &&\n    hasPlugin(plugins, \"importAttributes\")\n  ) {\n    throw new Error(\n      \"Cannot combine importAssertions and importAttributes plugins.\",\n    );\n  }\n\n  if (\n    hasPlugin(plugins, \"recordAndTuple\") &&\n    getPluginOption(plugins, \"recordAndTuple\", \"syntaxType\") != null &&\n    !RECORD_AND_TUPLE_SYNTAX_TYPES.includes(\n      getPluginOption(plugins, \"recordAndTuple\", \"syntaxType\"),\n    )\n  ) {\n    throw new Error(\n      \"The 'syntaxType' option of the 'recordAndTuple' plugin must be one of: \" +\n        RECORD_AND_TUPLE_SYNTAX_TYPES.map(p => `'${p}'`).join(\", \"),\n    );\n  }\n\n  if (\n    hasPlugin(plugins, \"asyncDoExpressions\") &&\n    !hasPlugin(plugins, \"doExpressions\")\n  ) {\n    const error = new Error(\n      \"'asyncDoExpressions' requires 'doExpressions', please add 'doExpressions' to parser plugins.\",\n    );\n    // @ts-expect-error so @babel/core can provide better error message\n    error.missingPlugins = \"doExpressions\";\n    throw error;\n  }\n}\n\n// These plugins are defined using a mixin which extends the parser class.\n\nimport estree from \"./plugins/estree\";\nimport flow from \"./plugins/flow\";\nimport jsx from \"./plugins/jsx\";\nimport typescript from \"./plugins/typescript\";\nimport placeholders from \"./plugins/placeholders\";\nimport v8intrinsic from \"./plugins/v8intrinsic\";\n\n// NOTE: order is important. estree must come first; placeholders must come last.\nexport const mixinPlugins = {\n  estree,\n  jsx,\n  flow,\n  typescript,\n  v8intrinsic,\n  placeholders,\n};\n\nexport const mixinPluginNames = Object.keys(mixinPlugins) as ReadonlyArray<\n  \"estree\" | \"jsx\" | \"flow\" | \"typescript\" | \"v8intrinsic\" | \"placeholders\"\n>;\n"], "mappings": ";;;;;;;;;AA8OA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,IAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAH,OAAA;AACA,IAAAI,aAAA,GAAAJ,OAAA;AACA,IAAAK,YAAA,GAAAL,OAAA;AA/NO,SAASM,SAASA,CACvBC,OAAmB,EACnBC,cAA4B,EACnB;EAIT,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GACnC,OAAOF,cAAc,KAAK,QAAQ,GAAG,CAACA,cAAc,EAAE,CAAC,CAAC,CAAC,GAAGA,cAAc;EAE5E,MAAMG,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACH,eAAe,CAAC;EAEjD,MAAMI,sBAAsB,GAAGH,YAAY,CAACI,MAAM,KAAK,CAAC;EAExD,OAAOR,OAAO,CAACS,IAAI,CAACC,CAAC,IAAI;IACvB,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MACzB,OAAOH,sBAAsB,IAAIG,CAAC,KAAKR,YAAY;IACrD,CAAC,MAAM;MACL,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGF,CAAC;MACrC,IAAIC,UAAU,KAAKT,YAAY,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,KAAK,MAAMW,GAAG,IAAIT,YAAY,EAAE;QAE9B,IAAIQ,aAAa,CAACC,GAAG,CAAC,KAAKV,eAAe,CAACU,GAAG,CAAC,EAAE;UAC/C,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb;EACF,CAAC,CAAC;AACJ;AAEO,SAASC,eAAeA,CAG7Bd,OAAmB,EAAEe,IAAgB,EAAEC,MAAkB,EAAE;EAC3D,MAAMC,MAAM,GAAGjB,OAAO,CAACkB,IAAI,CAACD,MAAM,IAAI;IACpC,IAAIE,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;MACzB,OAAOA,MAAM,CAAC,CAAC,CAAC,KAAKF,IAAI;IAC3B,CAAC,MAAM;MACL,OAAOE,MAAM,KAAKF,IAAI;IACxB;EACF,CAAC,CAAC;EAEF,IAAIE,MAAM,IAAIE,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,IAAIA,MAAM,CAACT,MAAM,GAAG,CAAC,EAAE;IACxD,OAAQS,MAAM,CAAC,CAAC,CAAC,CAA+BD,MAAM,CAAC;EACzD;EAEA,OAAO,IAAI;AACb;AAEA,MAAMK,kBAAkB,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC;AACjE,MAAMC,YAAY,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAChD,MAAMC,6BAA6B,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC;AAE9C,SAASC,eAAeA,CAACxB,OAAmB,EAAE;EACnD,IAAID,SAAS,CAACC,OAAO,EAAE,YAAY,CAAC,EAAE;IACpC,IAAID,SAAS,CAACC,OAAO,EAAE,mBAAmB,CAAC,EAAE;MAC3C,MAAM,IAAIyB,KAAK,CACb,iEACF,CAAC;IACH;IAEA,MAAMC,sBAAsB,GAAGZ,eAAe,CAC5Cd,OAAO,EACP,YAAY,EACZ,wBACF,CAAC;IACD,IACE0B,sBAAsB,IAAI,IAAI,IAC9B,OAAOA,sBAAsB,KAAK,SAAS,EAC3C;MACA,MAAM,IAAID,KAAK,CACb,2DACF,CAAC;IACH;IAEA,MAAME,sBAAsB,GAAGb,eAAe,CAC5Cd,OAAO,EACP,YAAY,EACZ,wBACF,CAAC;IACD,IACE2B,sBAAsB,IAAI,IAAI,IAC9B,OAAOA,sBAAsB,KAAK,SAAS,EAC3C;MACA,MAAM,IAAIF,KAAK,CAAC,6CAA6C,CAAC;IAChE;EACF;EAEA,IAAI1B,SAAS,CAACC,OAAO,EAAE,MAAM,CAAC,IAAID,SAAS,CAACC,OAAO,EAAE,YAAY,CAAC,EAAE;IAClE,MAAM,IAAIyB,KAAK,CAAC,6CAA6C,CAAC;EAChE;EAEA,IAAI1B,SAAS,CAACC,OAAO,EAAE,cAAc,CAAC,IAAID,SAAS,CAACC,OAAO,EAAE,aAAa,CAAC,EAAE;IAC3E,MAAM,IAAIyB,KAAK,CAAC,sDAAsD,CAAC;EACzE;EAEA,IAAI1B,SAAS,CAACC,OAAO,EAAE,kBAAkB,CAAC,EAAE;IAC1C,MAAM4B,QAAQ,GAAGd,eAAe,CAACd,OAAO,EAAE,kBAAkB,EAAE,UAAU,CAAC;IAEzE,IAAI,CAACqB,kBAAkB,CAACQ,QAAQ,CAACD,QAAQ,CAAC,EAAE;MAC1C,MAAME,YAAY,GAAGT,kBAAkB,CAACU,GAAG,CAACrB,CAAC,IAAK,IAAGA,CAAE,GAAE,CAAC,CAACsB,IAAI,CAAC,IAAI,CAAC;MACrE,MAAM,IAAIP,KAAK,CACZ,6EAA4EK,YAAa,GAC5F,CAAC;IACH;IAEA,MAAMG,iBAAiB,GAAGlC,SAAS,CAACC,OAAO,EAAE,CAC3C,gBAAgB,EAChB;MAAEkC,UAAU,EAAE;IAAO,CAAC,CACvB,CAAC;IAEF,IAAIN,QAAQ,KAAK,MAAM,EAAE;MACvB,IAAI7B,SAAS,CAACC,OAAO,EAAE,cAAc,CAAC,EAAE;QACtC,MAAM,IAAIyB,KAAK,CACb,0DACF,CAAC;MACH;MAEA,IAAI1B,SAAS,CAACC,OAAO,EAAE,aAAa,CAAC,EAAE;QACrC,MAAM,IAAIyB,KAAK,CACb,yDACF,CAAC;MACH;MAEA,MAAMU,UAAU,GAAGrB,eAAe,CAChCd,OAAO,EACP,kBAAkB,EAClB,YACF,CAAC;MAED,IAAI,CAACsB,YAAY,CAACO,QAAQ,CAACM,UAAU,CAAC,EAAE;QACtC,MAAMC,SAAS,GAAGd,YAAY,CAACS,GAAG,CAACM,CAAC,IAAK,IAAGA,CAAE,GAAE,CAAC,CAACL,IAAI,CAAC,IAAI,CAAC;QAE5D,MAAM,IAAIP,KAAK,CACZ,iHAAgHW,SAAU,GAC7H,CAAC;MACH;MAEA,IAAID,UAAU,KAAK,GAAG,IAAIF,iBAAiB,EAAE;QAC3C,MAAM,IAAIR,KAAK,CACb,wIACF,CAAC;MACH;IACF,CAAC,MAAM,IAAIG,QAAQ,KAAK,OAAO,IAAIK,iBAAiB,EAAE;MACpD,MAAM,IAAIR,KAAK,CACb,wHACF,CAAC;IACH;EACF;EAEA,IAAI1B,SAAS,CAACC,OAAO,EAAE,kBAAkB,CAAC,EAAE;IAKnC;MACL,IACED,SAAS,CAACC,OAAO,EAAE,kBAAkB,CAAC,IACtCD,SAAS,CAACC,OAAO,EAAE,kBAAkB,CAAC,EACtC;QACA,MAAM,IAAIyB,KAAK,CACb,iFACF,CAAC;MACH;MACA,MAAMa,mCAAmC,GAAGxB,eAAe,CACzDd,OAAO,EACP,kBAAkB,EAClB,SACF,CAAC;MACD,IAAIsC,mCAAmC,KAAK,UAAU,EAAE;QACtD,MAAM,IAAIb,KAAK,CACb,4DAA4D,GAC1D,wDAAwD,GACxD,sCACJ,CAAC;MACH;IACF;EACF;EACA,IACE1B,SAAS,CAACC,OAAO,EAAE,kBAAkB,CAAC,IACtCD,SAAS,CAACC,OAAO,EAAE,kBAAkB,CAAC,EACtC;IACA,MAAM,IAAIyB,KAAK,CACb,+DACF,CAAC;EACH;EAEA,IACE1B,SAAS,CAACC,OAAO,EAAE,gBAAgB,CAAC,IACpCc,eAAe,CAACd,OAAO,EAAE,gBAAgB,EAAE,YAAY,CAAC,IAAI,IAAI,IAChE,CAACuB,6BAA6B,CAACM,QAAQ,CACrCf,eAAe,CAACd,OAAO,EAAE,gBAAgB,EAAE,YAAY,CACzD,CAAC,EACD;IACA,MAAM,IAAIyB,KAAK,CACb,yEAAyE,GACvEF,6BAA6B,CAACQ,GAAG,CAACrB,CAAC,IAAK,IAAGA,CAAE,GAAE,CAAC,CAACsB,IAAI,CAAC,IAAI,CAC9D,CAAC;EACH;EAEA,IACEjC,SAAS,CAACC,OAAO,EAAE,oBAAoB,CAAC,IACxC,CAACD,SAAS,CAACC,OAAO,EAAE,eAAe,CAAC,EACpC;IACA,MAAMuC,KAAK,GAAG,IAAId,KAAK,CACrB,8FACF,CAAC;IAEDc,KAAK,CAACC,cAAc,GAAG,eAAe;IACtC,MAAMD,KAAK;EACb;AACF;AAYO,MAAME,YAAY,GAAG;EAC1BC,MAAM,EAANA,eAAM;EACNC,GAAG,EAAHA,YAAG;EACHC,IAAI,EAAJA,aAAI;EACJC,UAAU,EAAVA,mBAAU;EACVC,WAAW,EAAXA,oBAAW;EACXC,YAAY,EAAZA;AACF,CAAC;AAACC,OAAA,CAAAP,YAAA,GAAAA,YAAA;AAEK,MAAMQ,gBAAgB,GAAG5C,MAAM,CAACC,IAAI,CAACmC,YAAY,CAEvD;AAACO,OAAA,CAAAC,gBAAA,GAAAA,gBAAA"}