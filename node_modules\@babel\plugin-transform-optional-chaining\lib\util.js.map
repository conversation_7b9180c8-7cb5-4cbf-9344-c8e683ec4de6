{"version": 3, "names": ["_helperSkipTransparentExpressionWrappers", "require", "willPathCastToBoolean", "path", "maybeWrapped", "findOutermostTransparentParent", "node", "parentPath", "isLogicalExpression", "operator", "right", "isSequenceExpression", "expressions", "length", "isConditional", "test", "isUnaryExpression", "isLoop", "findParent", "p", "isTransparentExprWrapper"], "sources": ["../src/util.ts"], "sourcesContent": ["import type { NodePath } from \"@babel/traverse\";\nimport { isTransparentExprWrapper } from \"@babel/helper-skip-transparent-expression-wrappers\";\n/**\n * Test if a NodePath will be cast to boolean when evaluated.\n * It respects transparent expression wrappers defined in\n * \"@babel/helper-skip-transparent-expression-wrappers\"\n *\n * @example\n * // returns true\n * const nodePathADotB = NodePath(\"if (a.b) {}\").get(\"test\"); // a.b\n * willPathCastToBoolean(nodePathADotB)\n * @example\n * // returns false\n * willPathCastToBoolean(NodePath(\"a.b\"))\n * @param {NodePath} path\n * @returns {boolean}\n */\nexport function willPathCastToBoolean(path: NodePath): boolean {\n  const maybeWrapped = findOutermostTransparentParent(path);\n  const { node, parentPath } = maybeWrapped;\n  if (parentPath.isLogicalExpression()) {\n    const { operator, right } = parentPath.node;\n    if (\n      operator === \"&&\" ||\n      operator === \"||\" ||\n      (operator === \"??\" && node === right)\n    ) {\n      return willPathCastToBoolean(parentPath);\n    }\n  }\n  if (parentPath.isSequenceExpression()) {\n    const { expressions } = parentPath.node;\n    if (expressions[expressions.length - 1] === node) {\n      return willPathCastToBoolean(parentPath);\n    } else {\n      // if it is in the middle of a sequence expression, we don't\n      // care the return value so just cast to boolean for smaller\n      // output\n      return true;\n    }\n  }\n  return (\n    parentPath.isConditional({ test: node }) ||\n    parentPath.isUnaryExpression({ operator: \"!\" }) ||\n    parentPath.isLoop({ test: node })\n  );\n}\n\n/**\n * Return the outermost transparent expression wrapper of a given path,\n * otherwise returns path itself.\n * @example\n * const nodePathADotB = NodePath(\"(a.b as any)\").get(\"expression\"); // a.b\n * // returns NodePath(\"(a.b as any)\")\n * findOutermostTransparentParent(nodePathADotB);\n * @param {NodePath} path\n * @returns {NodePath}\n */\nexport function findOutermostTransparentParent(path: NodePath): NodePath {\n  let maybeWrapped = path;\n  path.findParent(p => {\n    if (!isTransparentExprWrapper(p.node)) return true;\n    maybeWrapped = p;\n  });\n  return maybeWrapped;\n}\n"], "mappings": ";;;;;;;AACA,IAAAA,wCAAA,GAAAC,OAAA;AAgBO,SAASC,qBAAqBA,CAACC,IAAc,EAAW;EAC7D,MAAMC,YAAY,GAAGC,8BAA8B,CAACF,IAAI,CAAC;EACzD,MAAM;IAAEG,IAAI;IAAEC;EAAW,CAAC,GAAGH,YAAY;EACzC,IAAIG,UAAU,CAACC,mBAAmB,CAAC,CAAC,EAAE;IACpC,MAAM;MAAEC,QAAQ;MAAEC;IAAM,CAAC,GAAGH,UAAU,CAACD,IAAI;IAC3C,IACEG,QAAQ,KAAK,IAAI,IACjBA,QAAQ,KAAK,IAAI,IAChBA,QAAQ,KAAK,IAAI,IAAIH,IAAI,KAAKI,KAAM,EACrC;MACA,OAAOR,qBAAqB,CAACK,UAAU,CAAC;IAC1C;EACF;EACA,IAAIA,UAAU,CAACI,oBAAoB,CAAC,CAAC,EAAE;IACrC,MAAM;MAAEC;IAAY,CAAC,GAAGL,UAAU,CAACD,IAAI;IACvC,IAAIM,WAAW,CAACA,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC,KAAKP,IAAI,EAAE;MAChD,OAAOJ,qBAAqB,CAACK,UAAU,CAAC;IAC1C,CAAC,MAAM;MAIL,OAAO,IAAI;IACb;EACF;EACA,OACEA,UAAU,CAACO,aAAa,CAAC;IAAEC,IAAI,EAAET;EAAK,CAAC,CAAC,IACxCC,UAAU,CAACS,iBAAiB,CAAC;IAAEP,QAAQ,EAAE;EAAI,CAAC,CAAC,IAC/CF,UAAU,CAACU,MAAM,CAAC;IAAEF,IAAI,EAAET;EAAK,CAAC,CAAC;AAErC;AAYO,SAASD,8BAA8BA,CAACF,IAAc,EAAY;EACvE,IAAIC,YAAY,GAAGD,IAAI;EACvBA,IAAI,CAACe,UAAU,CAACC,CAAC,IAAI;IACnB,IAAI,CAAC,IAAAC,iEAAwB,EAACD,CAAC,CAACb,IAAI,CAAC,EAAE,OAAO,IAAI;IAClDF,YAAY,GAAGe,CAAC;EAClB,CAAC,CAAC;EACF,OAAOf,YAAY;AACrB"}