{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_pluginSyntaxJsonStrings", "_default", "declare", "api", "assertVersion", "regex", "replace", "match", "escapes", "separator", "isEscaped", "length", "charCodeAt", "toString", "name", "inherits", "syntaxJsonStrings", "default", "visitor", "DirectiveLiteral|StringLiteral", "node", "extra", "raw", "exports"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport syntaxJsonStrings from \"@babel/plugin-syntax-json-strings\";\nimport type * as t from \"@babel/types\";\nimport type { NodePath } from \"@babel/traverse\";\n\nexport default declare(api => {\n  api.assertVersion(7);\n  const regex = /(\\\\*)([\\u2028\\u2029])/g;\n  function replace(match: string, escapes: string, separator: string) {\n    // If there's an odd number, that means the separator itself was escaped.\n    // \"\\X\" escapes X.\n    // \"\\\\X\" escapes the backslash, so X is unescaped.\n    const isEscaped = escapes.length % 2 === 1;\n    if (isEscaped) return match;\n\n    return `${escapes}\\\\u${separator.charCodeAt(0).toString(16)}`;\n  }\n\n  return {\n    name: \"transform-json-strings\",\n    inherits: syntaxJsonStrings.default,\n\n    visitor: {\n      \"DirectiveLiteral|StringLiteral\"({\n        node,\n      }: NodePath<t.DirectiveLiteral | t.StringLiteral>) {\n        const { extra } = node;\n        if (!extra?.raw) return;\n\n        extra.raw = (extra.raw as string).replace(regex, replace);\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,wBAAA,GAAAD,OAAA;AAAkE,IAAAE,QAAA,GAInD,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAC,CAAC,CAAC;EACpB,MAAMC,KAAK,GAAG,wBAAwB;EACtC,SAASC,OAAOA,CAACC,KAAa,EAAEC,OAAe,EAAEC,SAAiB,EAAE;IAIlE,MAAMC,SAAS,GAAGF,OAAO,CAACG,MAAM,GAAG,CAAC,KAAK,CAAC;IAC1C,IAAID,SAAS,EAAE,OAAOH,KAAK;IAE3B,OAAQ,GAAEC,OAAQ,MAAKC,SAAS,CAACG,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAE,EAAC;EAC/D;EAEA,OAAO;IACLC,IAAI,EAAE,wBAAwB;IAC9BC,QAAQ,EAAEC,wBAAiB,CAACC,OAAO;IAEnCC,OAAO,EAAE;MACP,gCAAgCC,CAAC;QAC/BC;MAC8C,CAAC,EAAE;QACjD,MAAM;UAAEC;QAAM,CAAC,GAAGD,IAAI;QACtB,IAAI,EAACC,KAAK,YAALA,KAAK,CAAEC,GAAG,GAAE;QAEjBD,KAAK,CAACC,GAAG,GAAID,KAAK,CAACC,GAAG,CAAYhB,OAAO,CAACD,KAAK,EAAEC,OAAO,CAAC;MAC3D;IACF;EACF,CAAC;AACH,CAAC,CAAC;AAAAiB,OAAA,CAAAN,OAAA,GAAAhB,QAAA"}