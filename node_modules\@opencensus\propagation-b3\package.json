{"_from": "@opencensus/propagation-b3@0.0.8", "_id": "@opencensus/propagation-b3@0.0.8", "_inBundle": false, "_integrity": "sha512-PffXX2AL8Sh0VHQ52jJC4u3T0H6wDK6N/4bg7xh4ngMYOIi13aR1kzVvX1sVDBgfGwDOkMbl4c54Xm3tlPx/+A==", "_location": "/@opencensus/propagation-b3", "_phantomChildren": {"continuation-local-storage": "3.2.1", "log-driver": "1.2.7", "semver": "5.7.0", "shimmer": "1.2.1", "uuid": "3.3.2"}, "_requested": {"type": "version", "registry": true, "raw": "@opencensus/propagation-b3@0.0.8", "name": "@opencensus/propagation-b3", "escapedName": "@opencensus%2fpropagation-b3", "scope": "@opencensus", "rawSpec": "0.0.8", "saveSpec": null, "fetchSpec": "0.0.8"}, "_requiredBy": ["/@pm2/io"], "_resolved": "https://registry.npmjs.org/@opencensus/propagation-b3/-/propagation-b3-0.0.8.tgz", "_shasum": "0751e6fd75f09400d9d3c419001e9e15a0df68e9", "_spec": "@opencensus/propagation-b3@0.0.8", "_where": "/var/www/html/node_modules/@pm2/io", "author": {"name": "Google Inc."}, "bugs": {"url": "https://github.com/census-instrumentation/opencensus-node/issues"}, "bundleDependencies": false, "dependencies": {"@opencensus/core": "^0.0.8", "uuid": "^3.2.1"}, "deprecated": false, "description": "Opencensus propagation package for B3 format.", "devDependencies": {"@types/mocha": "^5.2.5", "@types/node": "^10.12.12", "@types/uuid": "^3.4.3", "gts": "^0.9.0", "mocha": "^5.0.4", "ncp": "^2.0.0", "nyc": "^11.7.1", "rimraf": "^2.6.2", "ts-node": "^7.0.1", "typescript": "~2.7.2"}, "engines": {"node": ">=6.0"}, "files": ["build/src/**/*.js", "build/src/**/*.d.ts", "doc", "CHANGELOG.md", "LICENSE", "README.md"], "homepage": "https://github.com/census-instrumentation/opencensus-node#readme", "keywords": ["opencensus", "nodejs", "tracing", "profiling"], "license": "Apache-2.0", "main": "build/src/index.js", "name": "@opencensus/propagation-b3", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/census-instrumentation/opencensus-node.git"}, "scripts": {"check": "gts check", "clean": "rimraf build/*", "compile": "tsc -p .", "fix": "gts fix", "posttest": "npm run check", "prepare": "npm run compile", "pretest": "npm run compile", "test": "nyc -x '**/test/**' --reporter=html --reporter=text mocha 'build/test/**/*.js'"}, "types": "build/src/index.d.ts", "version": "0.0.8"}