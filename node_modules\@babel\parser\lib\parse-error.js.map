{"version": 3, "names": ["_location", "require", "_credentials", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "exports", "defineProperty", "enumerable", "get", "_moduleErrors", "_standardErrors", "_strictModeErrors", "_pipelineOperatorErrors", "_excluded", "_excluded2", "_objectWithoutPropertiesLoose", "source", "excluded", "target", "sourceKeys", "i", "length", "indexOf", "toParseErrorConstructor", "_ref", "toMessage", "properties", "constructor", "loc", "details", "instantiate", "SyntaxError", "assign", "clone", "overrides", "Position", "line", "column", "index", "value", "message", "set", "pos", "reflect", "missingPlugin", "ParseErrorEnum", "argument", "syntaxPlugin", "Array", "isArray", "parseErrorTemplates", "ParseErrorConstructors", "reasonCode", "template", "_ref2", "rest", "code", "ParseErrorCode", "Errors", "ModuleErrors", "StandardErrors", "StrictModeErrors", "PipelineOperatorErrors"], "sources": ["../src/parse-error.ts"], "sourcesContent": ["import { Position } from \"./util/location\";\nimport {\n  instantiate,\n  ParseErrorCode,\n  type ParseErrorCredentials,\n  type ToMessage,\n  type SyntaxPlugin,\n} from \"./parse-error/credentials\";\nimport type { Undone } from \"./parser/node\";\nimport type { Node } from \"./types\";\n\n// Babel uses \"normal\" SyntaxErrors for it's errors, but adds some extra\n// functionality. This functionality is defined in the\n// `ParseErrorSpecification` interface below. We may choose to change to someday\n// give our errors their own full-blown class, but until then this allow us to\n// keep all the desirable properties of SyntaxErrors (like their name in stack\n// traces, etc.), and also allows us to punt on any publicly facing\n// class-hierarchy decisions until Babel 8.\ninterface ParseErrorSpecification<ErrorDetails> {\n  // Look, these *could* be readonly, but then <PERSON> complains when we initially\n  // set them. We could do a whole dance and make a special interface that's not\n  // readonly for when we create the error, then cast it to the readonly\n  // interface for public use, but the previous implementation didn't have them\n  // as readonly, so let's just not worry about it for now.\n  code: ParseErrorCode;\n  reasonCode: string;\n  syntaxPlugin?: SyntaxPlugin;\n  missingPlugin?: string | string[];\n  loc: Position;\n  details: ErrorDetails;\n\n  // We should consider removing this as it now just contains the same\n  // information as `loc.index`.\n  // pos: number;\n}\n\nexport type ParseError<ErrorDetails> = SyntaxError &\n  ParseErrorSpecification<ErrorDetails>;\n\n// By `ParseErrorConstructor`, we mean something like the new-less style\n// `ErrorConstructor`[1], since `ParseError`'s are not themselves actually\n// separate classes from `SyntaxError`'s.\n//\n// 1. https://github.com/microsoft/TypeScript/blob/v4.5.5/lib/lib.es5.d.ts#L1027\nexport type ParseErrorConstructor<ErrorDetails> = (a: {\n  loc: Position;\n  details: ErrorDetails;\n}) => ParseError<ErrorDetails>;\n\nfunction toParseErrorConstructor<ErrorDetails extends object>({\n  toMessage,\n  ...properties\n}: ParseErrorCredentials<ErrorDetails>): ParseErrorConstructor<ErrorDetails> {\n  type ConstructorArgument = {\n    loc: Position;\n    details: ErrorDetails;\n  };\n\n  return function constructor({ loc, details }: ConstructorArgument) {\n    return instantiate(\n      SyntaxError,\n      { ...properties, loc },\n      {\n        clone(\n          overrides: {\n            loc?: Position;\n            details?: ErrorDetails;\n          } = {},\n        ) {\n          const loc = (overrides.loc || {}) as Partial<Position>;\n          return constructor({\n            loc: new Position(\n              \"line\" in loc ? loc.line : this.loc.line,\n              \"column\" in loc ? loc.column : this.loc.column,\n              \"index\" in loc ? loc.index : this.loc.index,\n            ),\n            details: { ...this.details, ...overrides.details },\n          });\n        },\n        details: { value: details, enumerable: false },\n        message: {\n          get(this: ConstructorArgument): string {\n            return `${toMessage(this.details)} (${this.loc.line}:${\n              this.loc.column\n            })`;\n          },\n          set(value: string) {\n            Object.defineProperty(this, \"message\", { value });\n          },\n        },\n        pos: { reflect: \"loc.index\", enumerable: true },\n        missingPlugin: \"missingPlugin\" in details && {\n          reflect: \"details.missingPlugin\",\n          enumerable: true,\n        },\n      },\n    ) as ParseError<ErrorDetails>;\n  };\n}\n\ntype ParseErrorTemplate =\n  | string\n  | ToMessage<any>\n  | { message: string | ToMessage<any> };\n\ntype ParseErrorTemplates = { [reasonCode: string]: ParseErrorTemplate };\n\n// This is the templated form of `ParseErrorEnum`.\n//\n// Note: We could factor out the return type calculation into something like\n// `ParseErrorConstructor<T extends ParseErrorTemplates>`, and then we could\n// reuse it in the non-templated form of `ParseErrorEnum`, but TypeScript\n// doesn't seem to drill down that far when showing you the computed type of\n// an object in an editor, so we'll leave it inlined for now.\nexport function ParseErrorEnum(a: TemplateStringsArray): <\n  T extends ParseErrorTemplates,\n>(\n  parseErrorTemplates: T,\n) => {\n  [K in keyof T]: ParseErrorConstructor<\n    T[K] extends { message: string | ToMessage<any> }\n      ? T[K][\"message\"] extends ToMessage<any>\n        ? Parameters<T[K][\"message\"]>[0]\n        : {}\n      : T[K] extends ToMessage<any>\n      ? Parameters<T[K]>[0]\n      : {}\n  >;\n};\n\nexport function ParseErrorEnum<T extends ParseErrorTemplates>(\n  parseErrorTemplates: T,\n  syntaxPlugin?: SyntaxPlugin,\n): {\n  [K in keyof T]: ParseErrorConstructor<\n    T[K] extends { message: string | ToMessage<any> }\n      ? T[K][\"message\"] extends ToMessage<any>\n        ? Parameters<T[K][\"message\"]>[0]\n        : {}\n      : T[K] extends ToMessage<any>\n      ? Parameters<T[K]>[0]\n      : {}\n  >;\n};\n\n// You call `ParseErrorEnum` with a mapping from `ReasonCode`'s to either:\n//\n// 1. a static error message,\n// 2. `toMessage` functions that define additional necessary `details` needed by\n//    the `ParseError`, or\n// 3. Objects that contain a `message` of one of the above and overridden `code`\n//    and/or `reasonCode`:\n//\n// ParseErrorEnum `optionalSyntaxPlugin` ({\n//   ErrorWithStaticMessage: \"message\",\n//   ErrorWithDynamicMessage: ({ type } : { type: string }) => `${type}`),\n//   ErrorWithOverriddenCodeAndOrReasonCode: {\n//     message: ({ type }: { type: string }) => `${type}`),\n//     code: ParseErrorCode.SourceTypeModuleError,\n//     ...(BABEL_8_BREAKING ? { } : { reasonCode: \"CustomErrorReasonCode\" })\n//   }\n// });\n//\nexport function ParseErrorEnum(\n  argument: TemplateStringsArray | ParseErrorTemplates,\n  syntaxPlugin?: SyntaxPlugin,\n) {\n  // If the first parameter is an array, that means we were called with a tagged\n  // template literal. Extract the syntaxPlugin from this, and call again in\n  // the \"normalized\" form.\n  if (Array.isArray(argument)) {\n    return (parseErrorTemplates: ParseErrorTemplates) =>\n      ParseErrorEnum(parseErrorTemplates, argument[0]);\n  }\n\n  const ParseErrorConstructors = {} as Record<\n    string,\n    ParseErrorConstructor<unknown>\n  >;\n\n  for (const reasonCode of Object.keys(argument)) {\n    const template = (argument as ParseErrorTemplates)[reasonCode];\n    const { message, ...rest } =\n      typeof template === \"string\"\n        ? { message: () => template }\n        : typeof template === \"function\"\n        ? { message: template }\n        : template;\n    const toMessage = typeof message === \"string\" ? () => message : message;\n\n    ParseErrorConstructors[reasonCode] = toParseErrorConstructor({\n      code: ParseErrorCode.SyntaxError,\n      reasonCode,\n      toMessage,\n      ...(syntaxPlugin ? { syntaxPlugin } : {}),\n      ...rest,\n    });\n  }\n\n  return ParseErrorConstructors;\n}\n\nexport type RaiseProperties<ErrorDetails> = {\n  at: Position | Undone<Node>;\n} & ErrorDetails;\n\nimport ModuleErrors from \"./parse-error/module-errors\";\nimport StandardErrors from \"./parse-error/standard-errors\";\nimport StrictModeErrors from \"./parse-error/strict-mode-errors\";\nimport PipelineOperatorErrors from \"./parse-error/pipeline-operator-errors\";\n\nexport const Errors = {\n  ...ParseErrorEnum(ModuleErrors),\n  ...ParseErrorEnum(StandardErrors),\n  ...ParseErrorEnum(StrictModeErrors),\n  ...ParseErrorEnum`pipelineOperator`(PipelineOperatorErrors),\n};\n\nexport type { LValAncestor } from \"./parse-error/standard-errors\";\n\nexport * from \"./parse-error/credentials\";\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AA2NAE,MAAA,CAAAC,IAAA,CAAAF,YAAA,EAAAG,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAJ,YAAA,CAAAI,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAZ,YAAA,CAAAI,GAAA;IAAA;EAAA;AAAA;AAdA,IAAAS,aAAA,GAAAd,OAAA;AACA,IAAAe,eAAA,GAAAf,OAAA;AACA,IAAAgB,iBAAA,GAAAhB,OAAA;AACA,IAAAiB,uBAAA,GAAAjB,OAAA;AAA4E,MAAAkB,SAAA;EAAAC,UAAA;AAAA,SAAAC,8BAAAC,MAAA,EAAAC,QAAA,QAAAD,MAAA,yBAAAE,MAAA,WAAAC,UAAA,GAAAtB,MAAA,CAAAC,IAAA,CAAAkB,MAAA,OAAAhB,GAAA,EAAAoB,CAAA,OAAAA,CAAA,MAAAA,CAAA,GAAAD,UAAA,CAAAE,MAAA,EAAAD,CAAA,MAAApB,GAAA,GAAAmB,UAAA,CAAAC,CAAA,OAAAH,QAAA,CAAAK,OAAA,CAAAtB,GAAA,kBAAAkB,MAAA,CAAAlB,GAAA,IAAAgB,MAAA,CAAAhB,GAAA,YAAAkB,MAAA;AAhK5E,SAASK,uBAAuBA,CAAAC,IAAA,EAG6C;EAAA,IAHf;MAC5DC;IAEmC,CAAC,GAAAD,IAAA;IADjCE,UAAU,GAAAX,6BAAA,CAAAS,IAAA,EAAAX,SAAA;EAOb,OAAO,SAASc,WAAWA,CAAC;IAAEC,GAAG;IAAEC;EAA6B,CAAC,EAAE;IACjE,OAAO,IAAAC,wBAAW,EAChBC,WAAW,EAAAlC,MAAA,CAAAmC,MAAA,KACNN,UAAU;MAAEE;IAAG,IACpB;MACEK,KAAKA,CACHC,SAGC,GAAG,CAAC,CAAC,EACN;QACA,MAAMN,GAAG,GAAIM,SAAS,CAACN,GAAG,IAAI,CAAC,CAAuB;QACtD,OAAOD,WAAW,CAAC;UACjBC,GAAG,EAAE,IAAIO,kBAAQ,CACf,MAAM,IAAIP,GAAG,GAAGA,GAAG,CAACQ,IAAI,GAAG,IAAI,CAACR,GAAG,CAACQ,IAAI,EACxC,QAAQ,IAAIR,GAAG,GAAGA,GAAG,CAACS,MAAM,GAAG,IAAI,CAACT,GAAG,CAACS,MAAM,EAC9C,OAAO,IAAIT,GAAG,GAAGA,GAAG,CAACU,KAAK,GAAG,IAAI,CAACV,GAAG,CAACU,KACxC,CAAC;UACDT,OAAO,EAAAhC,MAAA,CAAAmC,MAAA,KAAO,IAAI,CAACH,OAAO,EAAKK,SAAS,CAACL,OAAO;QAClD,CAAC,CAAC;MACJ,CAAC;MACDA,OAAO,EAAE;QAAEU,KAAK,EAAEV,OAAO;QAAEtB,UAAU,EAAE;MAAM,CAAC;MAC9CiC,OAAO,EAAE;QACPhC,GAAGA,CAAA,EAAoC;UACrC,OAAQ,GAAEiB,SAAS,CAAC,IAAI,CAACI,OAAO,CAAE,KAAI,IAAI,CAACD,GAAG,CAACQ,IAAK,IAClD,IAAI,CAACR,GAAG,CAACS,MACV,GAAE;QACL,CAAC;QACDI,GAAGA,CAACF,KAAa,EAAE;UACjB1C,MAAM,CAACS,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE;YAAEiC;UAAM,CAAC,CAAC;QACnD;MACF,CAAC;MACDG,GAAG,EAAE;QAAEC,OAAO,EAAE,WAAW;QAAEpC,UAAU,EAAE;MAAK,CAAC;MAC/CqC,aAAa,EAAE,eAAe,IAAIf,OAAO,IAAI;QAC3Cc,OAAO,EAAE,uBAAuB;QAChCpC,UAAU,EAAE;MACd;IACF,CACF,CAAC;EACH,CAAC;AACH;AAiEO,SAASsC,cAAcA,CAC5BC,QAAoD,EACpDC,YAA2B,EAC3B;EAIA,IAAIC,KAAK,CAACC,OAAO,CAACH,QAAQ,CAAC,EAAE;IAC3B,OAAQI,mBAAwC,IAC9CL,cAAc,CAACK,mBAAmB,EAAEJ,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD;EAEA,MAAMK,sBAAsB,GAAG,CAAC,CAG/B;EAED,KAAK,MAAMC,UAAU,IAAIvD,MAAM,CAACC,IAAI,CAACgD,QAAQ,CAAC,EAAE;IAC9C,MAAMO,QAAQ,GAAIP,QAAQ,CAAyBM,UAAU,CAAC;IAC9D,MAAAE,KAAA,GACE,OAAOD,QAAQ,KAAK,QAAQ,GACxB;QAAEb,OAAO,EAAEA,CAAA,KAAMa;MAAS,CAAC,GAC3B,OAAOA,QAAQ,KAAK,UAAU,GAC9B;QAAEb,OAAO,EAAEa;MAAS,CAAC,GACrBA,QAAQ;MALR;QAAEb;MAAiB,CAAC,GAAAc,KAAA;MAANC,IAAI,GAAAxC,6BAAA,CAAAuC,KAAA,EAAAxC,UAAA;IAMxB,MAAMW,SAAS,GAAG,OAAOe,OAAO,KAAK,QAAQ,GAAG,MAAMA,OAAO,GAAGA,OAAO;IAEvEW,sBAAsB,CAACC,UAAU,CAAC,GAAG7B,uBAAuB,CAAA1B,MAAA,CAAAmC,MAAA;MAC1DwB,IAAI,EAAEC,2BAAc,CAAC1B,WAAW;MAChCqB,UAAU;MACV3B;IAAS,GACLsB,YAAY,GAAG;MAAEA;IAAa,CAAC,GAAG,CAAC,CAAC,EACrCQ,IAAI,CACR,CAAC;EACJ;EAEA,OAAOJ,sBAAsB;AAC/B;AAWO,MAAMO,MAAM,GAAA7D,MAAA,CAAAmC,MAAA,KACda,cAAc,CAACc,qBAAY,CAAC,EAC5Bd,cAAc,CAACe,uBAAc,CAAC,EAC9Bf,cAAc,CAACgB,yBAAgB,CAAC,EAChChB,cAAe,kBAAiB,CAACiB,+BAAsB,CAAC,CAC5D;AAACzD,OAAA,CAAAqD,MAAA,GAAAA,MAAA"}