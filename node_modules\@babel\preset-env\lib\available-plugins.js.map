{"version": 3, "names": ["_pluginSyntaxAsyncGenerators", "require", "_pluginSyntaxClassProperties", "_pluginSyntaxClassStaticBlock", "_pluginSyntaxDynamicImport", "_pluginSyntaxExportNamespaceFrom", "_pluginSyntaxImportAssertions", "_pluginSyntaxImportAttributes", "_pluginSyntaxImportMeta", "_pluginSyntaxJsonStrings", "_pluginSyntaxLogicalAssignmentOperators", "_pluginSyntaxNullishCoalescingOperator", "_pluginSyntaxNumericSeparator", "_pluginSyntaxObjectRestSpread", "_pluginSyntaxOptionalCatchBinding", "_pluginSyntaxOptionalChaining", "_pluginSyntaxPrivatePropertyInObject", "_pluginSyntaxTopLevelAwait", "_pluginTransformAsyncGeneratorFunctions", "_pluginTransformClassProperties", "_pluginTransformClassStaticBlock", "_pluginTransformDynamicImport", "_pluginTransformExportNamespaceFrom", "_pluginTransformJsonStrings", "_pluginTransformLogicalAssignmentOperators", "_pluginTransformNullishCoalescingOperator", "_pluginTransformNumericSeparator", "_pluginTransformObjectRestSpread", "_pluginTransformOptionalCatchBinding", "_pluginTransformOptionalChaining", "_pluginTransformPrivateMethods", "_pluginTransformPrivatePropertyInObject", "_pluginTransformUnicodePropertyRegex", "_pluginTransformAsyncToGenerator", "_pluginTransformArrowFunctions", "_pluginTransformBlockScopedFunctions", "_pluginTransformBlockScoping", "_pluginTransformClasses", "_pluginTransformComputedProperties", "_pluginTransformDestructuring", "_pluginTransformDotallRegex", "_pluginTransformDuplicateKeys", "_pluginTransformExponentiationOperator", "_pluginTransformForOf", "_pluginTransformFunctionName", "_pluginTransformLiterals", "_pluginTransformMemberExpressionLiterals", "_pluginTransformModulesAmd", "_pluginTransformModulesCommonjs", "_pluginTransformModulesSystemjs", "_pluginTransformModulesUmd", "_pluginTransformNamedCapturingGroupsRegex", "_pluginTransformNewTarget", "_pluginTransformObjectSuper", "_pluginTransformParameters", "_pluginTransformPropertyLiterals", "_pluginTransformRegenerator", "_pluginTransformReservedWords", "_pluginTransformShorthandProperties", "_pluginTransformSpread", "_pluginTransformStickyRegex", "_pluginTransformTemplateLiterals", "_pluginTransformTypeofSymbol", "_pluginTransformUnicodeEscapes", "_pluginTransformUnicodeRegex", "_pluginTransformUnicodeSetsRegex", "_transformAsyncArrowsInClass", "_transformEdgeDefaultParameters", "_transformEdgeFunctionName", "_transformTaggedTemplateCaching", "_transformSafariBlockShadowing", "_transformSafariForShadowing", "_pluginBugfixSafariIdDestructuringCollisionInFunctionExpression", "_pluginBugfixV8SpreadParametersInOptionalChaining", "_default", "bugfix/transform-async-arrows-in-class", "bugfixAsyncArrowsInClass", "bugfix/transform-edge-default-parameters", "bugfixEdgeDefaultParameters", "bugfix/transform-edge-function-name", "bugfixEdgeFunctionName", "bugfix/transform-safari-block-shadowing", "bugfixSafariBlockShadowing", "bugfix/transform-safari-for-shadowing", "bugfixSafariForShadowing", "bugfix/transform-safari-id-destructuring-collision-in-function-expression", "bugfixSafariIdDestructuringCollisionInFunctionExpression", "bugfix/transform-tagged-template-caching", "bugfixTaggedTemplateCaching", "bugfix/transform-v8-spread-parameters-in-optional-chaining", "bugfixV8SpreadParametersInOptionalChaining", "syntax-async-generators", "syntaxAsyncGenerators", "syntax-class-properties", "syntaxClassProperties", "syntax-class-static-block", "syntaxClassStaticBlock", "syntax-dynamic-import", "syntaxDynamicImport", "syntax-export-namespace-from", "syntaxExportNamespaceFrom", "syntax-import-assertions", "syntaxImportAssertions", "syntax-import-attributes", "syntaxImportAttributes", "syntax-import-meta", "syntaxImportMeta", "syntax-json-strings", "syntaxJsonStrings", "syntax-logical-assignment-operators", "syntaxLogicalAssignmentOperators", "syntax-nullish-coalescing-operator", "syntaxNullishCoalescingOperator", "syntax-numeric-separator", "syntaxNumericSeparator", "syntax-object-rest-spread", "syntaxObjectRestSpread", "syntax-optional-catch-binding", "syntaxOptionalCatchBinding", "syntax-optional-chaining", "syntaxOptionalChaining", "syntax-private-property-in-object", "syntaxPrivatePropertyInObject", "syntax-top-level-await", "syntaxTopLevelAwait", "syntax-unicode-sets-regex", "transform-arrow-functions", "transformArrowFunctions", "transform-async-generator-functions", "proposalAsyncGeneratorFunctions", "transform-async-to-generator", "transformAsyncToGenerator", "transform-block-scoped-functions", "transformBlockScopedFunctions", "transform-block-scoping", "transformBlockScoping", "transform-class-properties", "proposalClassProperties", "transform-class-static-block", "proposalClassStaticBlock", "transform-classes", "transformClasses", "transform-computed-properties", "transformComputedProperties", "transform-destructuring", "transformDestructuring", "transform-dotall-regex", "transformDotallRegex", "transform-duplicate-keys", "transformDuplicateKeys", "transform-dynamic-import", "proposalDynamicImport", "transform-exponentiation-operator", "transformExponentialOperator", "transform-export-namespace-from", "proposalExportNamespaceFrom", "transform-for-of", "transformForOf", "transform-function-name", "transformFunctionName", "transform-json-strings", "proposalJsonStrings", "transform-literals", "transformLiterals", "transform-logical-assignment-operators", "proposalLogicalAssignmentOperators", "transform-member-expression-literals", "transformMemberExpressionLiterals", "transform-modules-amd", "transformModulesAmd", "transform-modules-commonjs", "transformModulesCommonjs", "transform-modules-systemjs", "transformModulesSystemjs", "transform-modules-umd", "transformModulesUmd", "transform-named-capturing-groups-regex", "transformNamedCapturingGroupsRegex", "transform-new-target", "transformNewTarget", "transform-nullish-coalescing-operator", "proposalNullishCoalescingOperator", "transform-numeric-separator", "proposalNumericSeparator", "transform-object-rest-spread", "proposalObjectRestSpread", "transform-object-super", "transformObjectSuper", "transform-optional-catch-binding", "proposalOptionalCatchBinding", "transform-optional-chaining", "proposalOptionalChaining", "transform-parameters", "transformParameters", "transform-private-methods", "proposalPrivateMethods", "transform-private-property-in-object", "proposalPrivatePropertyInObject", "transform-property-literals", "transformPropertyLiterals", "transform-regenerator", "transformRegenerator", "transform-reserved-words", "transformReservedWords", "transform-shorthand-properties", "transformShorthandProperties", "transform-spread", "transformSpread", "transform-sticky-regex", "transformStickyRegex", "transform-template-literals", "transformTemplateLiterals", "transform-typeof-symbol", "transformTypeofSymbol", "transform-unicode-escapes", "transformUnicodeEscapes", "transform-unicode-property-regex", "proposalUnicodePropertyRegex", "transform-unicode-regex", "transformUnicodeRegex", "transform-unicode-sets-regex", "transformUnicodeSetsRegex", "exports", "default", "minVersions"], "sources": ["../src/available-plugins.ts"], "sourcesContent": ["/* eslint sort-keys: \"error\" */\n\ndeclare const USE_ESM: boolean;\n\nimport syntaxAsyncGenerators from \"@babel/plugin-syntax-async-generators\";\nimport syntaxClassProperties from \"@babel/plugin-syntax-class-properties\";\nimport syntaxClassStaticBlock from \"@babel/plugin-syntax-class-static-block\";\nimport syntaxDynamicImport from \"@babel/plugin-syntax-dynamic-import\";\nimport syntaxExportNamespaceFrom from \"@babel/plugin-syntax-export-namespace-from\";\nimport syntaxImportAssertions from \"@babel/plugin-syntax-import-assertions\";\nimport syntaxImportAttributes from \"@babel/plugin-syntax-import-attributes\";\nimport syntaxImportMeta from \"@babel/plugin-syntax-import-meta\";\nimport syntaxJsonStrings from \"@babel/plugin-syntax-json-strings\";\nimport syntaxLogicalAssignmentOperators from \"@babel/plugin-syntax-logical-assignment-operators\";\nimport syntaxNullishCoalescingOperator from \"@babel/plugin-syntax-nullish-coalescing-operator\";\nimport syntaxNumericSeparator from \"@babel/plugin-syntax-numeric-separator\";\nimport syntaxObjectRestSpread from \"@babel/plugin-syntax-object-rest-spread\";\nimport syntaxOptionalCatchBinding from \"@babel/plugin-syntax-optional-catch-binding\";\nimport syntaxOptionalChaining from \"@babel/plugin-syntax-optional-chaining\";\nimport syntaxPrivatePropertyInObject from \"@babel/plugin-syntax-private-property-in-object\";\nimport syntaxTopLevelAwait from \"@babel/plugin-syntax-top-level-await\";\nimport proposalAsyncGeneratorFunctions from \"@babel/plugin-transform-async-generator-functions\";\nimport proposalClassProperties from \"@babel/plugin-transform-class-properties\";\nimport proposalClassStaticBlock from \"@babel/plugin-transform-class-static-block\";\nimport proposalDynamicImport from \"@babel/plugin-transform-dynamic-import\";\nimport proposalExportNamespaceFrom from \"@babel/plugin-transform-export-namespace-from\";\nimport proposalJsonStrings from \"@babel/plugin-transform-json-strings\";\nimport proposalLogicalAssignmentOperators from \"@babel/plugin-transform-logical-assignment-operators\";\nimport proposalNullishCoalescingOperator from \"@babel/plugin-transform-nullish-coalescing-operator\";\nimport proposalNumericSeparator from \"@babel/plugin-transform-numeric-separator\";\nimport proposalObjectRestSpread from \"@babel/plugin-transform-object-rest-spread\";\nimport proposalOptionalCatchBinding from \"@babel/plugin-transform-optional-catch-binding\";\nimport proposalOptionalChaining from \"@babel/plugin-transform-optional-chaining\";\nimport proposalPrivateMethods from \"@babel/plugin-transform-private-methods\";\nimport proposalPrivatePropertyInObject from \"@babel/plugin-transform-private-property-in-object\";\nimport proposalUnicodePropertyRegex from \"@babel/plugin-transform-unicode-property-regex\";\nimport transformAsyncToGenerator from \"@babel/plugin-transform-async-to-generator\";\nimport transformArrowFunctions from \"@babel/plugin-transform-arrow-functions\";\nimport transformBlockScopedFunctions from \"@babel/plugin-transform-block-scoped-functions\";\nimport transformBlockScoping from \"@babel/plugin-transform-block-scoping\";\nimport transformClasses from \"@babel/plugin-transform-classes\";\nimport transformComputedProperties from \"@babel/plugin-transform-computed-properties\";\nimport transformDestructuring from \"@babel/plugin-transform-destructuring\";\nimport transformDotallRegex from \"@babel/plugin-transform-dotall-regex\";\nimport transformDuplicateKeys from \"@babel/plugin-transform-duplicate-keys\";\nimport transformExponentialOperator from \"@babel/plugin-transform-exponentiation-operator\";\nimport transformForOf from \"@babel/plugin-transform-for-of\";\nimport transformFunctionName from \"@babel/plugin-transform-function-name\";\nimport transformLiterals from \"@babel/plugin-transform-literals\";\nimport transformMemberExpressionLiterals from \"@babel/plugin-transform-member-expression-literals\";\nimport transformModulesAmd from \"@babel/plugin-transform-modules-amd\";\nimport transformModulesCommonjs from \"@babel/plugin-transform-modules-commonjs\";\nimport transformModulesSystemjs from \"@babel/plugin-transform-modules-systemjs\";\nimport transformModulesUmd from \"@babel/plugin-transform-modules-umd\";\nimport transformNamedCapturingGroupsRegex from \"@babel/plugin-transform-named-capturing-groups-regex\";\nimport transformNewTarget from \"@babel/plugin-transform-new-target\";\nimport transformObjectSuper from \"@babel/plugin-transform-object-super\";\nimport transformParameters from \"@babel/plugin-transform-parameters\";\nimport transformPropertyLiterals from \"@babel/plugin-transform-property-literals\";\nimport transformRegenerator from \"@babel/plugin-transform-regenerator\";\nimport transformReservedWords from \"@babel/plugin-transform-reserved-words\";\nimport transformShorthandProperties from \"@babel/plugin-transform-shorthand-properties\";\nimport transformSpread from \"@babel/plugin-transform-spread\";\nimport transformStickyRegex from \"@babel/plugin-transform-sticky-regex\";\nimport transformTemplateLiterals from \"@babel/plugin-transform-template-literals\";\nimport transformTypeofSymbol from \"@babel/plugin-transform-typeof-symbol\";\nimport transformUnicodeEscapes from \"@babel/plugin-transform-unicode-escapes\";\nimport transformUnicodeRegex from \"@babel/plugin-transform-unicode-regex\";\nimport transformUnicodeSetsRegex from \"@babel/plugin-transform-unicode-sets-regex\";\n\nimport bugfixAsyncArrowsInClass from \"@babel/preset-modules/lib/plugins/transform-async-arrows-in-class\";\nimport bugfixEdgeDefaultParameters from \"@babel/preset-modules/lib/plugins/transform-edge-default-parameters\";\nimport bugfixEdgeFunctionName from \"@babel/preset-modules/lib/plugins/transform-edge-function-name\";\nimport bugfixTaggedTemplateCaching from \"@babel/preset-modules/lib/plugins/transform-tagged-template-caching\";\nimport bugfixSafariBlockShadowing from \"@babel/preset-modules/lib/plugins/transform-safari-block-shadowing\";\nimport bugfixSafariForShadowing from \"@babel/preset-modules/lib/plugins/transform-safari-for-shadowing\";\nimport bugfixSafariIdDestructuringCollisionInFunctionExpression from \"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression\";\nimport bugfixV8SpreadParametersInOptionalChaining from \"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining\";\n\nexport default {\n  \"bugfix/transform-async-arrows-in-class\": () => bugfixAsyncArrowsInClass,\n  \"bugfix/transform-edge-default-parameters\": () => bugfixEdgeDefaultParameters,\n  \"bugfix/transform-edge-function-name\": () => bugfixEdgeFunctionName,\n  \"bugfix/transform-safari-block-shadowing\": () => bugfixSafariBlockShadowing,\n  \"bugfix/transform-safari-for-shadowing\": () => bugfixSafariForShadowing,\n  \"bugfix/transform-safari-id-destructuring-collision-in-function-expression\":\n    () => bugfixSafariIdDestructuringCollisionInFunctionExpression,\n  \"bugfix/transform-tagged-template-caching\": () => bugfixTaggedTemplateCaching,\n  \"bugfix/transform-v8-spread-parameters-in-optional-chaining\": () =>\n    bugfixV8SpreadParametersInOptionalChaining,\n  \"syntax-async-generators\": () => syntaxAsyncGenerators,\n  \"syntax-class-properties\": () => syntaxClassProperties,\n  \"syntax-class-static-block\": () => syntaxClassStaticBlock,\n  \"syntax-dynamic-import\": () => syntaxDynamicImport,\n  \"syntax-export-namespace-from\": () => syntaxExportNamespaceFrom,\n  \"syntax-import-assertions\": () => syntaxImportAssertions,\n  \"syntax-import-attributes\": () => syntaxImportAttributes,\n  \"syntax-import-meta\": () => syntaxImportMeta,\n  \"syntax-json-strings\": () => syntaxJsonStrings,\n  \"syntax-logical-assignment-operators\": () => syntaxLogicalAssignmentOperators,\n  \"syntax-nullish-coalescing-operator\": () => syntaxNullishCoalescingOperator,\n  \"syntax-numeric-separator\": () => syntaxNumericSeparator,\n  \"syntax-object-rest-spread\": () => syntaxObjectRestSpread,\n  \"syntax-optional-catch-binding\": () => syntaxOptionalCatchBinding,\n  \"syntax-optional-chaining\": () => syntaxOptionalChaining,\n  \"syntax-private-property-in-object\": () => syntaxPrivatePropertyInObject,\n  \"syntax-top-level-await\": () => syntaxTopLevelAwait,\n  // This is a CJS plugin that depends on a package from the monorepo, so it\n  // breaks using ESM. Given that ESM builds are new enough to have this\n  // syntax enabled by default, we can safely skip enabling it.\n  \"syntax-unicode-sets-regex\": USE_ESM\n    ? null\n    : // We cannot use the require call when bundling, because this is an ESM file.\n    // Babel standalone uses a modern parser, so just include a known noop plugin.\n    // Use `bind` so that it's not detected as a duplicate plugin when using it\n    // together with the TLA\n    IS_STANDALONE\n    ? // @ts-expect-error syntaxTopLevelAwait is a function when bundled\n      () => syntaxTopLevelAwait.bind()\n    : // eslint-disable-next-line no-restricted-globals\n      () => require(\"@babel/plugin-syntax-unicode-sets-regex\"),\n  \"transform-arrow-functions\": () => transformArrowFunctions,\n  \"transform-async-generator-functions\": () => proposalAsyncGeneratorFunctions,\n  \"transform-async-to-generator\": () => transformAsyncToGenerator,\n  \"transform-block-scoped-functions\": () => transformBlockScopedFunctions,\n  \"transform-block-scoping\": () => transformBlockScoping,\n  \"transform-class-properties\": () => proposalClassProperties,\n  \"transform-class-static-block\": () => proposalClassStaticBlock,\n  \"transform-classes\": () => transformClasses,\n  \"transform-computed-properties\": () => transformComputedProperties,\n  \"transform-destructuring\": () => transformDestructuring,\n  \"transform-dotall-regex\": () => transformDotallRegex,\n  \"transform-duplicate-keys\": () => transformDuplicateKeys,\n  \"transform-dynamic-import\": () => proposalDynamicImport,\n  \"transform-exponentiation-operator\": () => transformExponentialOperator,\n  \"transform-export-namespace-from\": () => proposalExportNamespaceFrom,\n  \"transform-for-of\": () => transformForOf,\n  \"transform-function-name\": () => transformFunctionName,\n  \"transform-json-strings\": () => proposalJsonStrings,\n  \"transform-literals\": () => transformLiterals,\n  \"transform-logical-assignment-operators\": () =>\n    proposalLogicalAssignmentOperators,\n  \"transform-member-expression-literals\": () =>\n    transformMemberExpressionLiterals,\n  \"transform-modules-amd\": () => transformModulesAmd,\n  \"transform-modules-commonjs\": () => transformModulesCommonjs,\n  \"transform-modules-systemjs\": () => transformModulesSystemjs,\n  \"transform-modules-umd\": () => transformModulesUmd,\n  \"transform-named-capturing-groups-regex\": () =>\n    transformNamedCapturingGroupsRegex,\n  \"transform-new-target\": () => transformNewTarget,\n  \"transform-nullish-coalescing-operator\": () =>\n    proposalNullishCoalescingOperator,\n  \"transform-numeric-separator\": () => proposalNumericSeparator,\n  \"transform-object-rest-spread\": () => proposalObjectRestSpread,\n  \"transform-object-super\": () => transformObjectSuper,\n  \"transform-optional-catch-binding\": () => proposalOptionalCatchBinding,\n  \"transform-optional-chaining\": () => proposalOptionalChaining,\n  \"transform-parameters\": () => transformParameters,\n  \"transform-private-methods\": () => proposalPrivateMethods,\n  \"transform-private-property-in-object\": () => proposalPrivatePropertyInObject,\n  \"transform-property-literals\": () => transformPropertyLiterals,\n  \"transform-regenerator\": () => transformRegenerator,\n  \"transform-reserved-words\": () => transformReservedWords,\n  \"transform-shorthand-properties\": () => transformShorthandProperties,\n  \"transform-spread\": () => transformSpread,\n  \"transform-sticky-regex\": () => transformStickyRegex,\n  \"transform-template-literals\": () => transformTemplateLiterals,\n  \"transform-typeof-symbol\": () => transformTypeofSymbol,\n  \"transform-unicode-escapes\": () => transformUnicodeEscapes,\n  \"transform-unicode-property-regex\": () => proposalUnicodePropertyRegex,\n  \"transform-unicode-regex\": () => transformUnicodeRegex,\n  \"transform-unicode-sets-regex\": () => transformUnicodeSetsRegex,\n};\n\nexport const minVersions = {\n  \"bugfix/transform-safari-id-destructuring-collision-in-function-expression\":\n    \"7.16.0\",\n  \"syntax-import-attributes\": \"7.22.0\",\n  \"transform-class-static-block\": \"7.12.0\",\n  \"transform-private-property-in-object\": \"7.10.0\",\n};\n"], "mappings": ";;;;;;AAIA,IAAAA,4BAAA,GAAAC,OAAA;AACA,IAAAC,4BAAA,GAAAD,OAAA;AACA,IAAAE,6BAAA,GAAAF,OAAA;AACA,IAAAG,0BAAA,GAAAH,OAAA;AACA,IAAAI,gCAAA,GAAAJ,OAAA;AACA,IAAAK,6BAAA,GAAAL,OAAA;AACA,IAAAM,6BAAA,GAAAN,OAAA;AACA,IAAAO,uBAAA,GAAAP,OAAA;AACA,IAAAQ,wBAAA,GAAAR,OAAA;AACA,IAAAS,uCAAA,GAAAT,OAAA;AACA,IAAAU,sCAAA,GAAAV,OAAA;AACA,IAAAW,6BAAA,GAAAX,OAAA;AACA,IAAAY,6BAAA,GAAAZ,OAAA;AACA,IAAAa,iCAAA,GAAAb,OAAA;AACA,IAAAc,6BAAA,GAAAd,OAAA;AACA,IAAAe,oCAAA,GAAAf,OAAA;AACA,IAAAgB,0BAAA,GAAAhB,OAAA;AACA,IAAAiB,uCAAA,GAAAjB,OAAA;AACA,IAAAkB,+BAAA,GAAAlB,OAAA;AACA,IAAAmB,gCAAA,GAAAnB,OAAA;AACA,IAAAoB,6BAAA,GAAApB,OAAA;AACA,IAAAqB,mCAAA,GAAArB,OAAA;AACA,IAAAsB,2BAAA,GAAAtB,OAAA;AACA,IAAAuB,0CAAA,GAAAvB,OAAA;AACA,IAAAwB,yCAAA,GAAAxB,OAAA;AACA,IAAAyB,gCAAA,GAAAzB,OAAA;AACA,IAAA0B,gCAAA,GAAA1B,OAAA;AACA,IAAA2B,oCAAA,GAAA3B,OAAA;AACA,IAAA4B,gCAAA,GAAA5B,OAAA;AACA,IAAA6B,8BAAA,GAAA7B,OAAA;AACA,IAAA8B,uCAAA,GAAA9B,OAAA;AACA,IAAA+B,oCAAA,GAAA/B,OAAA;AACA,IAAAgC,gCAAA,GAAAhC,OAAA;AACA,IAAAiC,8BAAA,GAAAjC,OAAA;AACA,IAAAkC,oCAAA,GAAAlC,OAAA;AACA,IAAAmC,4BAAA,GAAAnC,OAAA;AACA,IAAAoC,uBAAA,GAAApC,OAAA;AACA,IAAAqC,kCAAA,GAAArC,OAAA;AACA,IAAAsC,6BAAA,GAAAtC,OAAA;AACA,IAAAuC,2BAAA,GAAAvC,OAAA;AACA,IAAAwC,6BAAA,GAAAxC,OAAA;AACA,IAAAyC,sCAAA,GAAAzC,OAAA;AACA,IAAA0C,qBAAA,GAAA1C,OAAA;AACA,IAAA2C,4BAAA,GAAA3C,OAAA;AACA,IAAA4C,wBAAA,GAAA5C,OAAA;AACA,IAAA6C,wCAAA,GAAA7C,OAAA;AACA,IAAA8C,0BAAA,GAAA9C,OAAA;AACA,IAAA+C,+BAAA,GAAA/C,OAAA;AACA,IAAAgD,+BAAA,GAAAhD,OAAA;AACA,IAAAiD,0BAAA,GAAAjD,OAAA;AACA,IAAAkD,yCAAA,GAAAlD,OAAA;AACA,IAAAmD,yBAAA,GAAAnD,OAAA;AACA,IAAAoD,2BAAA,GAAApD,OAAA;AACA,IAAAqD,0BAAA,GAAArD,OAAA;AACA,IAAAsD,gCAAA,GAAAtD,OAAA;AACA,IAAAuD,2BAAA,GAAAvD,OAAA;AACA,IAAAwD,6BAAA,GAAAxD,OAAA;AACA,IAAAyD,mCAAA,GAAAzD,OAAA;AACA,IAAA0D,sBAAA,GAAA1D,OAAA;AACA,IAAA2D,2BAAA,GAAA3D,OAAA;AACA,IAAA4D,gCAAA,GAAA5D,OAAA;AACA,IAAA6D,4BAAA,GAAA7D,OAAA;AACA,IAAA8D,8BAAA,GAAA9D,OAAA;AACA,IAAA+D,4BAAA,GAAA/D,OAAA;AACA,IAAAgE,gCAAA,GAAAhE,OAAA;AAEA,IAAAiE,4BAAA,GAAAjE,OAAA;AACA,IAAAkE,+BAAA,GAAAlE,OAAA;AACA,IAAAmE,0BAAA,GAAAnE,OAAA;AACA,IAAAoE,+BAAA,GAAApE,OAAA;AACA,IAAAqE,8BAAA,GAAArE,OAAA;AACA,IAAAsE,4BAAA,GAAAtE,OAAA;AACA,IAAAuE,+DAAA,GAAAvE,OAAA;AACA,IAAAwE,iDAAA,GAAAxE,OAAA;AAAwH,IAAAyE,QAAA,GAEzG;EACb,wCAAwC,EAAEC,CAAA,KAAMC,4BAAwB;EACxE,0CAA0C,EAAEC,CAAA,KAAMC,+BAA2B;EAC7E,qCAAqC,EAAEC,CAAA,KAAMC,0BAAsB;EACnE,yCAAyC,EAAEC,CAAA,KAAMC,8BAA0B;EAC3E,uCAAuC,EAAEC,CAAA,KAAMC,4BAAwB;EACvE,2EAA2E,EACzEC,CAAA,KAAMC,uEAAwD;EAChE,0CAA0C,EAAEC,CAAA,KAAMC,+BAA2B;EAC7E,4DAA4D,EAAEC,CAAA,KAC5DC,yDAA0C;EAC5C,yBAAyB,EAAEC,CAAA,KAAMC,4BAAqB;EACtD,yBAAyB,EAAEC,CAAA,KAAMC,4BAAqB;EACtD,2BAA2B,EAAEC,CAAA,KAAMC,6BAAsB;EACzD,uBAAuB,EAAEC,CAAA,KAAMC,0BAAmB;EAClD,8BAA8B,EAAEC,CAAA,KAAMC,gCAAyB;EAC/D,0BAA0B,EAAEC,CAAA,KAAMC,qCAAsB;EACxD,0BAA0B,EAAEC,CAAA,KAAMC,qCAAsB;EACxD,oBAAoB,EAAEC,CAAA,KAAMC,uBAAgB;EAC5C,qBAAqB,EAAEC,CAAA,KAAMC,wBAAiB;EAC9C,qCAAqC,EAAEC,CAAA,KAAMC,uCAAgC;EAC7E,oCAAoC,EAAEC,CAAA,KAAMC,sCAA+B;EAC3E,0BAA0B,EAAEC,CAAA,KAAMC,6BAAsB;EACxD,2BAA2B,EAAEC,CAAA,KAAMC,6BAAsB;EACzD,+BAA+B,EAAEC,CAAA,KAAMC,iCAA0B;EACjE,0BAA0B,EAAEC,CAAA,KAAMC,6BAAsB;EACxD,mCAAmC,EAAEC,CAAA,KAAMC,oCAA6B;EACxE,wBAAwB,EAAEC,CAAA,KAAMC,0BAAmB;EAInD,2BAA2B,EAUvBC,CAAA,KAAM5H,OAAO,CAAC,yCAAyC,CAAC;EAC5D,2BAA2B,EAAE6H,CAAA,KAAMC,sCAAuB;EAC1D,qCAAqC,EAAEC,CAAA,KAAMC,+CAA+B;EAC5E,8BAA8B,EAAEC,CAAA,KAAMC,wCAAyB;EAC/D,kCAAkC,EAAEC,CAAA,KAAMC,4CAA6B;EACvE,yBAAyB,EAAEC,CAAA,KAAMC,oCAAqB;EACtD,4BAA4B,EAAEC,CAAA,KAAMC,uCAAuB;EAC3D,8BAA8B,EAAEC,CAAA,KAAMC,wCAAwB;EAC9D,mBAAmB,EAAEC,CAAA,KAAMC,+BAAgB;EAC3C,+BAA+B,EAAEC,CAAA,KAAMC,0CAA2B;EAClE,yBAAyB,EAAEC,CAAA,KAAMC,qCAAsB;EACvD,wBAAwB,EAAEC,CAAA,KAAMC,mCAAoB;EACpD,0BAA0B,EAAEC,CAAA,KAAMC,qCAAsB;EACxD,0BAA0B,EAAEC,CAAA,KAAMC,qCAAqB;EACvD,mCAAmC,EAAEC,CAAA,KAAMC,8CAA4B;EACvE,iCAAiC,EAAEC,CAAA,KAAMC,2CAA2B;EACpE,kBAAkB,EAAEC,CAAA,KAAMC,6BAAc;EACxC,yBAAyB,EAAEC,CAAA,KAAMC,oCAAqB;EACtD,wBAAwB,EAAEC,CAAA,KAAMC,mCAAmB;EACnD,oBAAoB,EAAEC,CAAA,KAAMC,gCAAiB;EAC7C,wCAAwC,EAAEC,CAAA,KACxCC,kDAAkC;EACpC,sCAAsC,EAAEC,CAAA,KACtCC,gDAAiC;EACnC,uBAAuB,EAAEC,CAAA,KAAMC,kCAAmB;EAClD,4BAA4B,EAAEC,CAAA,KAAMC,uCAAwB;EAC5D,4BAA4B,EAAEC,CAAA,KAAMC,uCAAwB;EAC5D,uBAAuB,EAAEC,CAAA,KAAMC,kCAAmB;EAClD,wCAAwC,EAAEC,CAAA,KACxCC,iDAAkC;EACpC,sBAAsB,EAAEC,CAAA,KAAMC,iCAAkB;EAChD,uCAAuC,EAAEC,CAAA,KACvCC,iDAAiC;EACnC,6BAA6B,EAAEC,CAAA,KAAMC,wCAAwB;EAC7D,8BAA8B,EAAEC,CAAA,KAAMC,wCAAwB;EAC9D,wBAAwB,EAAEC,CAAA,KAAMC,mCAAoB;EACpD,kCAAkC,EAAEC,CAAA,KAAMC,4CAA4B;EACtE,6BAA6B,EAAEC,CAAA,KAAMC,wCAAwB;EAC7D,sBAAsB,EAAEC,CAAA,KAAMC,kCAAmB;EACjD,2BAA2B,EAAEC,CAAA,KAAMC,sCAAsB;EACzD,sCAAsC,EAAEC,CAAA,KAAMC,+CAA+B;EAC7E,6BAA6B,EAAEC,CAAA,KAAMC,wCAAyB;EAC9D,uBAAuB,EAAEC,CAAA,KAAMC,mCAAoB;EACnD,0BAA0B,EAAEC,CAAA,KAAMC,qCAAsB;EACxD,gCAAgC,EAAEC,CAAA,KAAMC,2CAA4B;EACpE,kBAAkB,EAAEC,CAAA,KAAMC,8BAAe;EACzC,wBAAwB,EAAEC,CAAA,KAAMC,mCAAoB;EACpD,6BAA6B,EAAEC,CAAA,KAAMC,wCAAyB;EAC9D,yBAAyB,EAAEC,CAAA,KAAMC,oCAAqB;EACtD,2BAA2B,EAAEC,CAAA,KAAMC,sCAAuB;EAC1D,kCAAkC,EAAEC,CAAA,KAAMC,4CAA4B;EACtE,yBAAyB,EAAEC,CAAA,KAAMC,oCAAqB;EACtD,8BAA8B,EAAEC,CAAA,KAAMC;AACxC,CAAC;AAAAC,OAAA,CAAAC,OAAA,GAAArJ,QAAA;AAEM,MAAMsJ,WAAW,GAAG;EACzB,2EAA2E,EACzE,QAAQ;EACV,0BAA0B,EAAE,QAAQ;EACpC,8BAA8B,EAAE,QAAQ;EACxC,sCAAsC,EAAE;AAC1C,CAAC;AAACF,OAAA,CAAAE,WAAA,GAAAA,WAAA"}