{"version": 3, "names": ["proposalPlugins", "Set", "exports", "proposalSyntaxPlugins", "pluginSyntaxObject", "pluginSyntaxEntries", "Object", "keys", "map", "key", "pluginSyntaxMap", "Map"], "sources": ["../src/shipped-proposals.ts"], "sourcesContent": ["// TODO(Babel 8): Remove this file\n/* eslint sort-keys: \"error\" */\n// These mappings represent the transform plugins that have been\n// shipped by browsers, and are enabled by the `shippedProposals` option.\n\nconst proposalPlugins = new Set<string>();\n\n// proposal syntax plugins enabled by the `shippedProposals` option.\n// Unlike proposalPlugins above, they are independent of compiler targets.\nconst proposalSyntaxPlugins = [\n  \"syntax-import-assertions\",\n  \"syntax-import-attributes\",\n] as const;\n\n// use intermediary object to enforce alphabetical key order\nconst pluginSyntaxObject = {\n  \"transform-async-generator-functions\": \"syntax-async-generators\",\n  \"transform-class-properties\": \"syntax-class-properties\",\n  \"transform-class-static-block\": \"syntax-class-static-block\",\n  \"transform-json-strings\": \"syntax-json-strings\",\n  \"transform-nullish-coalescing-operator\": \"syntax-nullish-coalescing-operator\",\n  \"transform-numeric-separator\": \"syntax-numeric-separator\",\n  \"transform-object-rest-spread\": \"syntax-object-rest-spread\",\n  \"transform-optional-catch-binding\": \"syntax-optional-catch-binding\",\n  \"transform-optional-chaining\": \"syntax-optional-chaining\",\n  // note: we don't have syntax-private-methods\n  \"transform-private-methods\": \"syntax-class-properties\",\n  \"transform-private-property-in-object\": \"syntax-private-property-in-object\",\n  \"transform-unicode-property-regex\": null as null,\n} as const;\n\nconst pluginSyntaxEntries = Object.keys(pluginSyntaxObject).map<\n  [string, string | null]\n>(function (key) {\n  return [\n    key,\n    // @ts-expect-error key has been guarded\n    pluginSyntaxObject[key],\n  ];\n});\n\nconst pluginSyntaxMap = new Map(pluginSyntaxEntries);\n\nexport { proposalPlugins, proposalSyntaxPlugins, pluginSyntaxMap };\n"], "mappings": ";;;;;;AAKA,MAAMA,eAAe,GAAG,IAAIC,GAAG,CAAS,CAAC;AAACC,OAAA,CAAAF,eAAA,GAAAA,eAAA;AAI1C,MAAMG,qBAAqB,GAAG,CAC5B,0BAA0B,EAC1B,0BAA0B,CAClB;AAACD,OAAA,CAAAC,qBAAA,GAAAA,qBAAA;AAGX,MAAMC,kBAAkB,GAAG;EACzB,qCAAqC,EAAE,yBAAyB;EAChE,4BAA4B,EAAE,yBAAyB;EACvD,8BAA8B,EAAE,2BAA2B;EAC3D,wBAAwB,EAAE,qBAAqB;EAC/C,uCAAuC,EAAE,oCAAoC;EAC7E,6BAA6B,EAAE,0BAA0B;EACzD,8BAA8B,EAAE,2BAA2B;EAC3D,kCAAkC,EAAE,+BAA+B;EACnE,6BAA6B,EAAE,0BAA0B;EAEzD,2BAA2B,EAAE,yBAAyB;EACtD,sCAAsC,EAAE,mCAAmC;EAC3E,kCAAkC,EAAE;AACtC,CAAU;AAEV,MAAMC,mBAAmB,GAAGC,MAAM,CAACC,IAAI,CAACH,kBAAkB,CAAC,CAACI,GAAG,CAE7D,UAAUC,GAAG,EAAE;EACf,OAAO,CACLA,GAAG,EAEHL,kBAAkB,CAACK,GAAG,CAAC,CACxB;AACH,CAAC,CAAC;AAEF,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAACN,mBAAmB,CAAC;AAACH,OAAA,CAAAQ,eAAA,GAAAA,eAAA"}