{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_pluginSyntaxNumericSeparator", "remover", "node", "_extra$raw", "extra", "raw", "includes", "replace", "_default", "declare", "api", "assertVersion", "name", "inherits", "syntaxNumericSeparator", "default", "visitor", "NumericLiteral", "BigIntLiteral", "exports"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport syntaxNumericSeparator from \"@babel/plugin-syntax-numeric-separator\";\nimport type { NodePath } from \"@babel/traverse\";\nimport type * as t from \"@babel/types\";\n\n/**\n * Given a bigIntLiteral or NumericLiteral, remove numeric\n * separator `_` from its raw representation\n *\n * @param {NodePath<BigIntLiteral | NumericLiteral>} { node }: A Babel AST node path\n */\nfunction remover({ node }: NodePath<t.BigIntLiteral | t.NumericLiteral>) {\n  const { extra } = node;\n  // @ts-expect-error todo(flow->ts)\n  if (extra?.raw?.includes(\"_\")) {\n    // @ts-expect-error todo(flow->ts)\n    extra.raw = extra.raw.replace(/_/g, \"\");\n  }\n}\n\nexport default declare(api => {\n  api.assertVersion(7);\n\n  return {\n    name: \"transform-numeric-separator\",\n    inherits: syntaxNumericSeparator.default,\n\n    visitor: {\n      NumericLiteral: remover,\n      BigIntLiteral: remover,\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,6BAAA,GAAAD,OAAA;AAUA,SAASE,OAAOA,CAAC;EAAEC;AAAmD,CAAC,EAAE;EAAA,IAAAC,UAAA;EACvE,MAAM;IAAEC;EAAM,CAAC,GAAGF,IAAI;EAEtB,IAAIE,KAAK,aAAAD,UAAA,GAALC,KAAK,CAAEC,GAAG,aAAVF,UAAA,CAAYG,QAAQ,CAAC,GAAG,CAAC,EAAE;IAE7BF,KAAK,CAACC,GAAG,GAAGD,KAAK,CAACC,GAAG,CAACE,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;EACzC;AACF;AAAC,IAAAC,QAAA,GAEc,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAC,CAAC,CAAC;EAEpB,OAAO;IACLC,IAAI,EAAE,6BAA6B;IACnCC,QAAQ,EAAEC,6BAAsB,CAACC,OAAO;IAExCC,OAAO,EAAE;MACPC,cAAc,EAAEhB,OAAO;MACvBiB,aAAa,EAAEjB;IACjB;EACF,CAAC;AACH,CAAC,CAAC;AAAAkB,OAAA,CAAAJ,OAAA,GAAAP,QAAA"}