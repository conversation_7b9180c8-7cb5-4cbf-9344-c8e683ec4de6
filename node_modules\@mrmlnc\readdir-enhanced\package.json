{"_args": [["@mrmlnc/readdir-enhanced@2.2.1", "/var/www/html"]], "_development": true, "_from": "@mrmlnc/readdir-enhanced@2.2.1", "_id": "@mrmlnc/readdir-enhanced@2.2.1", "_inBundle": false, "_integrity": "sha512-bPHp6Ji8b41szTOcaP63VlnbbO5Ny6dwAATtY6JTjh5N2OLrb5Qk/Th5cRkRQhkWCt+EJsYrNB0MiL+Gpn6e3g==", "_location": "/@mrmlnc/readdir-enhanced", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@mrmlnc/readdir-enhanced@2.2.1", "name": "@mrmlnc/readdir-enhanced", "escapedName": "@mrmlnc%2freaddir-enhanced", "scope": "@mrmlnc", "rawSpec": "2.2.1", "saveSpec": null, "fetchSpec": "2.2.1"}, "_requiredBy": ["/fast-glob"], "_resolved": "https://registry.npmjs.org/@mrmlnc/readdir-enhanced/-/readdir-enhanced-2.2.1.tgz", "_spec": "2.2.1", "_where": "/var/www/html", "author": {"name": "<PERSON>", "url": "http://bigstickcarpet.com"}, "bugs": {"url": "https://github.com/bigstickcarpet/readdir-enhanced/issues"}, "dependencies": {"call-me-maybe": "^1.0.1", "glob-to-regexp": "^0.3.0"}, "description": "fs.readdir with sync, async, and streaming APIs + filtering, recursion, absolute paths, etc.", "devDependencies": {"chai": "^4.1.2", "codacy-coverage": "^2.0.3", "coveralls": "^3.0.0", "del": "^3.0.0", "eslint": "^4.15.0", "eslint-config-modular": "^4.1.1", "istanbul": "^0.4.5", "mkdirp": "^0.5.1", "mocha": "^4.1.0", "npm-check": "^5.5.2", "through2": "^2.0.3", "version-bump-prompt": "^4.0.0"}, "engines": {"node": ">=4"}, "files": ["lib", "types.d.ts"], "homepage": "https://github.com/bigstickcarpet/readdir-enhanced", "keywords": ["fs", "readdir", "stream", "event", "recursive", "deep", "filter", "absolute"], "license": "MIT", "main": "lib/index.js", "name": "@mrmlnc/readdir-enhanced", "repository": {"type": "git", "url": "git+https://github.com/bigstickcarpet/readdir-enhanced.git"}, "scripts": {"bump": "bump --prompt --tag --push --all", "cover": "istanbul cover _mocha", "lint": "eslint lib test --fix", "release": "npm run upgrade && npm test && npm run bump && npm publish", "test": "mocha && npm run lint", "upgrade": "npm-check -u"}, "typings": "types.d.ts", "version": "2.2.1"}