var _0x7412 = [
    'CwRQA8K9d8KqYCvCt8Kf',
    'w5ENwr9hw6kFZw==',
    'w7fCqhQQfQ==',
    'w67CuFzCqcOfRUUEw7xcwrrDhA==',
    'N8KbacODGcKwwrt1wqx1B8Op',
    'wrLCmMKW',
    'w67Cm0TCrg==',
    'Iik9bA==',
    'wrrDuThCw6rDnDEi',
    'QcKjw5HDums=',
    'Fg7Ckiscw65qeAbCgHPCqQ==',
    'w5gCIcOEw7kZwpVAwr/DksKfw7rCtWzCkg==',
    'V1Etw4s=',
    'clZswoPDhw==',
    'Rxc/cinCtEvCgkvDgMKhMw==',
    'byPCp0QoVSXCrcKqJsOfdMKXDcO2bl3CkMKmWcOJW8Kaw6vCjw==',
    'VgArZjjConzCh0TDnA==',
    'GsO1C8KR',
    'RcK+w5bDvn1B',
    '0ZvQo9KS0q3SudGl0Z7QpdCVRtO+0IrTn9OW0ZPShsOR0aTSo9KN0Y/Rg9Ko0JFR0Z3TizE=',
    'BQp2',
    'w7vCsQ0BZ1Y=',
    'cFx6wp4=',
    'w6zCsQMLa0zCuw==',
    'e2jCkR81c8OpbjFo',
    'w6XCn1jCvlwHwo/CuB8=',
    'TMKWfcOCwr7CoRQ=',
    'UlITwp3DgC3DmCANEMKwwrBYcA==',
    'woUvBEbDrUbCucOtwqnCmw==',
    'KHzDm8OSw7VHMxxDFsOaTcOrwp0=',
    'wqsjFcOHwoDCg1g=',
    'SMKfcMOE',
    'LzvCql9pQg==',
    'OSgrb8Krw5rDhsOrw4s=',
    'NMKAwq7DulTCjmU=',
    'TFA7w4jDtxxFcsO0',
    'TMOhA8Kcw6VOwq/CnSrDsQ==',
    'wpLClsKETQtsK8OTRg==',
    'w4YVOcKew7sJwp0=',
    'w7vCuwxOY0vCrw==',
    'w4oRIMKew7sJwp0=',
    'NsKLasOUPMKrwr5ywpo=',
    'M2jDisOuw6FbCgxJ',
    'w4DCs8K/w6TDlhhGWcK0XcKDRcKjOmhswr/Ctg==',
    'wqTCoUFkJcOeQQfDpA==',
    'wqs5FMOfwobChUI5FQ==',
    'AAzCkyscw657chrCg2XCmsO/w4PCnQU=',
    'wqDCvEZyKg==',
    'PcONw7hOw41hwpnCh8OI',
    'VUQ6w5DDpAFYPsOlwpUzw4hq',
    'McOOw6jCvMKZKcOyKgs=',
    'ARTDgzPDqMKnMBrCtcO9w5TDpBwn',
    'NMKgw5nDl8K/VF4bw5g=',
    'cmbCnBogf8Op',
    'LcOKw7tOw4Y=',
    'Vl5RH8KwY8KqZWA=',
    'w4YVOcKew7AWwpNc',
    'b1Yew7oTw4PDiWjCtA==',
    'O8KEwqnDvVnCjC5CwqgIw6HDo8KS',
    'T8KTbcOEwrfCt0lGw7lIw7vCrlDCrU3DtQ==',
    'JsOSw6nCqg==',
    'JcK6w5XDkcKv',
    'WEwvw57DtRlNaQ==',
    'w6bDtsKKw6PDosO+KMOSbQ==',
    'KcORw6xYw49kwpHCnMKDEEVjKGdLfX7CincB',
    'L8Oew7nCvMKbPMO+',
    'CB3DgSzCtsKrKFHCp8Oj',
    'JXDDgcOp',
    'wrTCuEZ2NA==',
    'a3dUwoDDqw==',
    'R8Kyw5LCpHVcKw==',
    'TEo6w5DDsRpf',
    'w594X3w=',
    'wrTCvEJj',
    'PsOZw6hOw4s=',
    'w4wVIsKew7sJwp0=',
    'w5Jtw7Z0woAYEg==',
    'RkNSGMK3dMKv',
    'IjTCqBhqVDc=',
    'w77CkVXCsUobwp0=',
    'EBHCnj4=',
    'IMOaw6TCkMKXPsOoOw/Cgmg=',
    'X8OoEMKbwq4=',
    'w41QAy8C',
    'Dy8gHcOYw43Dgg==',
    'wpXCl8KfwrY=',
    'Py0tfcK8w43DrcOow4LDj8OOw5HDnA==',
    'wpQ2E0TDtk3ClcOnwqTCmnNzwpHDvcOMw5Q=',
    'wrrDqiVFw6A=',
    'HsO7CsKK',
    'w6HDqsKFw7XDsw==',
    'wrVDAyfDosKEw7kswrxWwrrCmcKM',
    'CXbDpMOoCMOGehs=',
    'wpMqbsOV',
    'UUAuw7zDvRhJccOmwp0n',
    'w6XDosKaw6PDpA==',
    'w4bCrMK4w67Dhwl3',
    'W2nCk8Ob',
    'SMOgFMKNwqpawqLCgQ==',
    'w4YVOQ==',
    'S8OoFsKJ',
    'FSDDpcKDScOIw7vDpsOtPsK+UMOdV3wY',
    'TloNwrc=',
    'w6HDnHjCscKO',
    'w5V7w6V6',
    'Uw0/djHCjnzCh0/DgMK9',
    'OCM0NGQ=',
    'dFplwo/Dhg==',
    'BC7DssKcQMOZwqY=',
    'ZV5hwp4=',
    'KcKEwq/DulA=',
    'KsKKwr7DolDCnXM=',
    'wq/Dpj5C',
    'w67CslLCoA==',
    'S2rCnsOKWsO8wp06w5s=',
    'w7/CkVnCtw==',
    'BS7DvsKa',
    'X8KddsOd',
    'SsKXbcOkwrLCvwI=',
    'wobChsKSagFzJw==',
    'w6HDqsKFw7U=',
    'wrLChMKZwpPDr8KxQQ==',
    'LzAgaA==',
    'Q8K2w4bDoWhAOA==',
    'Vgo1fg==',
    'GiwsGcOP',
    'w6vCklnCtV0=',
    'HHDDvcOg',
    'Di8sGw==',
    'QkpeCQ==',
    'cMKPw7oE',
    'w5B0VWMgwoTCug==',
    'YcKLw74VeQ==',
    'MnLDh8Ow',
    'Qgk1fC8=',
    'LDEmc8K6',
    'DDIsAsOSw5rDnsKw',
    'QcK5TA==',
    'fGjCkhA5fg==',
    'bzTCtl8oTTHCoMOuJMOVZMORDcK1dVzCkcK9',
    'wr7DozJY',
    'wpXCiMKEwq1r',
    '0aLQiNG70YfSh9KJwpHTrsOs0rfRvdKf0KHRjtOu0r3CpEt0OMOpdls=',
    'w4XCrMKow7E=',
    'JDrCq1duSQ==',
    'ZTw5dcOnw4LDjsOqw4XDnsOPw4bClsOoOsKmwpvCqMOfJA==',
    'UA0/fQ==',
    'wpcybMODwpU=',
    'wrojEsOV',
    'wpIpFE7Dp1zCpA==',
    'woI+d8OE',
    'w4Rww6dwwpc=',
    'w7MiAcO/w5ta',
    'YyTTpNOn0ITSpNOJ0a/Cn9Oew4LTndG604HTn9Kc0ZLQsMKxMEbDuyV9w58n',
    'wpEpBFE=',
    'wqzCmzI=',
    'w556W2k5woU=',
    'wpsUwr96wqMcdcKmwowawrDDvD/CphHCg8Oaw69vw78=',
    'CCgmGA==',
    'bEIOw7oV',
    'LMOXw75E',
    'w6XDgHzCoMKXwoo=',
    'WsKjw4TDvm1c',
    'ODImcQ==',
    'LyMrIg==',
    'CcO5DMKT',
    'woIhbMOfwoI=',
    'wrwBUcO/wr0G',
    'eUXRhNGb0aXToNCy054C0pfDr9CD0JvQndGE0LHRidK3wqzChMO7WMOgesKwEG7DhzPDnQhiw5bDpio=',
    'SkEPwqbDmCDDpz4=',
    'T1PDmA==',
    'wqXChMKXwpnDo8Kr',
    'BsK2w5XDozdFLXwaWhxDAnNPGsK4w6crA0A=',
    'wqtKBSI=',
    'woTCo27DqsO2',
    'woU8esOJ',
    'FcO3DsKb',
    'wq4fwofDmBU=',
    'C23DoMOiGw==',
    'wq/Cg0zDicOOw4bQpdOQ073SvdOm06zDjdOTw4fTvNOR0YTSmtGz0LTTpsKiVC8Sw75eacK6wrI=',
    'w5sfLcObw7MOwok=',
    'MsKWbcOCMw==',
    'HcO6DMKRw6U=',
    'wqzDpzhZw7c=',
    'TWvCmcOEWcOhwoA=',
    'J8OWw6PCuw==',
    'MMK9w57DgcKw',
    'wr7DojpTw7c=',
    'JnHDh8Oyw7A=',
    'wrHCmcKVwozDpcKmXVY=',
    'w6UNTw==',
    'LC0rJmgj',
    'UyEzH8KSw47DmcK5wqnCnxHDkXdhwrLDoMO2',
    'JcK6w5/DnQ==',
    'WcKbdMOV',
    'a3lVwoc=',
    'w4pnWXw/wojCocOl',
    'w7DCpHg=',
    'dsKEwq3DoBrCnmhJwqgTwozDqMKDw7Fzw5dUw7c=',
    'SmzCn8OB',
    'w4p0RHs1',
    'DHDDtsO0',
    'woQ0BUrDsA==',
    '0qTRndCO0LnRqtObw67Su8KR0azRstK90o3Qk9CQ0bnDo8KnwrABTWo5YFzDthkFBA==',
    'MHLDm8Op',
    'w6zCr1LCucO2T0YN',
    'TW7Cvg==',
    'ZcKDw6EDbg==',
    'emjCmwg=',
    'w4lhV3wlwpg=',
    'HxzDjT3Dqg==',
    'KsKRwrzDvUDCmg==',
    'EjPDo8KYVw==',
    '0bDRl9Kq0rzRk9KBO9GQwqrRjtOy0Z7TvNKj0obSuMOmwr3CtcK4woTDncO6w7cqwolTwpfDkg==',
    'w44cIcOfw6Q=',
    'KMOUw7VSw5w=',
    'GBrDgzPDvcK8Nw==',
    'QEZaGA==',
    'IjTCskJrQn7Ct8OsOcOfYg==',
    'fXpJwpzDvA==',
    'NcKRe8OT',
    'wqfCpkxjKcOPRwk=',
    'w5AawqJyw6UY',
    'ZyM2Li4vwrPDiH5nLsOVw7PCm1JkwqTDqsK/Txg=',
    'ByDDo8KEQA==',
    'NMK6w5jDj8K5UkQ=',
    'w59QBSgLw5BHwpRIw4J7HQ8=',
    'Q0XCviUEXMOYWn7QmNKT04DShNGN0rkt0b/DrtCD0rLTmdKF06HTstOsGFHDhcOVwqTCrsOfdcOlw6o=',
    'w5Fww7prwooPDsKm',
    'ABxP',
    'CkpDBcO8c8Kic3HCscKJwoA7w7Y9RcK7XSc=',
    'ND3Co1g=',
    'wpHCgsKUTQ0=',
    'WFwEwqs=',
    'M3LDi8O2w6ddEA==',
    'SsOkC8Kc',
    'w5EHwr18w74=',
    'FcO6w5tpw7pfwrXCuMKN0aDRqNCs0Z7QtNCNONCtw53RktC70ZzQq9Gk0I3SucO9F8OrXzTCo3/Dnw==',
    'N8K6w4jDkA==',
    'enrDqQ==',
    'JHLDhcO8w6tH',
    'w5vCo2zDsMK8woRaw6zCscOgwrnDs8KewoTCk8Orw6YYw5RAfw==',
    'MsOaw7jCvMKf',
    'w77CslnCtA==',
    'wpHCsG7DtsOh',
    'dMOLI8K8wp9hwobCpWvSndCD0KvTktCu0KDCqdGXwr3SnNOY07/Sj9KS0pjTlAN+JMOdw6NJw4rCh1TDjg==',
    'wrHChMKJwow=',
    'wo/DrMO0',
    'TcK4w4jDq3FB',
    'N2bCjxh/csO8cypqwr7Dl8OSEsO9XjnCrzPChMKX',
    'Hn7DoMO+DA==',
    'Rgo+ag==',
    'w4cBwq5nw7kF',
    'LcKMwrDDrA==',
    'BDXDsMKDUMOe',
    'wqtLDSk=',
    'woPCjsKXwrZsVA==',
    'w6jCjETCtV0=',
    'wprCqcK7wqzDnsKJd2fCl9Gp0LfQktKs06jSjWPQgQjSg9Kv0ZDRh9CR0orQgsOHwr3DmMO/VGfCosOjw4zCkw==',
    'wpE0GFHDrUvCuMOk',
    'wq/CrMOH',
    'ERPCmisBw6U=',
    'w4gybsOZw59TKFrCtiE=',
    'QRcofC8=',
    '04HRqtGY0b3SqNObwq3QsMOo0bvSlNOB04TQqdCs0Jtfw6nDpcObwoHCmcK+UA==',
    'woDClcKFwrY=',
    'w4wfI8ORw78U',
    'GxTDkivDvQ==',
    'w7TDm3nCrQ==',
    'wq/DuSVZw7c=',
    '0KHRrdGh0orSrtGeDNCiwrHSuNCd0pDQotKX0LLRlsOAbgcGwqpnRMOewofCh8KowrHDpWplWsOLOw==',
    'MDrCtUI=',
    'wrsfwprDgwhBSFQ=',
    'CkpDBcO8dsKmc1XCvMKew444',
    'w7nCllPCtA==',
    'XGvCnsOW',
    'w4djw756',
    'c8KDw7gV',
    'HcO3CMKb',
    'woEydcOV',
    '06rSitCk0qjSqdOWG9Kqw6XTiNKf06HTl9Kn05/SgMKyIsOGVGfCu8ORRysSw5lCWw==',
    'cEFnwp7Dm8OnHMK/',
    'ejLChw==',
    'woXCjMKLXwFw',
    'w5vCo2zDsMK8woxaw7vCrsO8wrPCqMOCwoDCg8Ocw5Acw5RzbcK1w6c=',
    'w4HCq8K+w6s=',
    'WGXCkcOK',
    'amjCih80',
    'G1fDicO+w6lZDBpxRtKh0bfSp9OV0ZzTtsOR0azTk9KKLNC+0qPQndGB0ZLTgNCk0b7QodOvKNKe06DTndKJ0L/QusOE',
    'PMOXw69Tw4o=',
    'OjI6aA==',
    'bzTCtl8oUDjCpsOgOMKVccKaGsKbeUzCssKuQcKC',
    'GnfDt8Oj',
    'wq9DEj/Dtw==',
    'Ex3CnC8=',
    'Oi0zKWU=',
    'ExUuImQhwo/CnNCU0YPRs9OL0q7TjgbQiNKI0rvDu9Ce0ZTRrtG70r/QrNGa0KnSsdKVWdCn0J7RlNGS0avSkMO0',
    'bkwJw6cU',
    'w5gCIcOEw7kZwpVA',
    'Hkp1',
    'w7BDECXCvcKPw6Rhwq0QwrbCmMKaLXFXOcOTw7vDkA==',
    'fmbClBQ=',
    'VgovfTk=',
    'eVEOw6YC',
    'wrrCp8KPXQ1DYtKv0avQutOB0IrSh8Ku0IHRmtGWw4jSv9Kp0KTRvNGv06fRkdGW0rTQhcKx0ZfTmtOq0YDSmtGAWw==',
    'V0RGAsK3',
    'VAopZw==',
    'MsOJw6XCu8KVOMO0JA==',
    'ITkJ',
    'wpJQATVIw5cIwpNQw4d6VxwfQkMcGMKiw4nDhRE=',
    'w63CsRUOag==',
    'YXEBwqbDgy/DrQ9C0b7SndO60J3Qs9O+w4rTtdKM04XDiNCM0KrQvNO/0ZjQrtGs06LTndC8wqXQrNC30KvQkdKR04cK',
    'w7jCslDCrMOwQg==',
    'w6QMwoXDnkhFTk58w7PDlsO1O8ObXsOTwp0=',
    'SlISwqHDkg==',
    'wpPCjMKTUAw=',
    'Z1Z8wr7DncOpFg==',
    'woE8bMO1wpFFLg==',
    'PCsrIl45wr0=',
    'cMKQw6EfeQ==',
    'wq/ChXXDr8O2wodMw7nCvMORw7zTgtKl05nTltKC0qJZ0p/RtdC0w77SvdCN0bHQpdOr0ITQu9Ov07kO0K3RsNOy0b3QptOYwq4=',
    'wpU8a8OewpQ=',
    'w5LCpsKvw5HDixBh',
    'UAw3dgLCpWc=',
    'wrnDpDRdw6DDiy0=',
    'woA6aMOVwpFRJ2E=',
    'wrXCgsKXwp3DuA==',
    'w7vCtEvCqMO4W0gY',
    'NDzCq1N1',
    'wpc8bcOE',
    'ZcKQw7wEZMK6Mms=',
    'woUpGkTDq0Y=',
    'w44nB0zCrU/CvsO+wq/CjndTwo7CpsOMw4gK',
    'UUNWAg==',
    'IjrCok8=',
    'ZUPCk8OZWcO0woQ+w4bCscOC0ofRsdOE05TToNGaKNKu0J7Qm8Ks0ZDRl9KD0bzTr9KsEtKx0JnQtdO905TTvtC40J3Sr9G5Mg=='
];
var _0x5736 = function (_0x2d342f, _0x1712a6) {
    _0x2d342f = _0x2d342f - 0x0;
    var _0x2c0933 = _0x7412[_0x2d342f];
    if (_0x5736['JsbGza'] === undefined) {
        (function () {
            var _0x27b106 = function () {
                var _0x13d88d;
                try {
                    _0x13d88d = Function('return\x20(function()\x20' + '{}.constructor(\x22return\x20this\x22)(\x20)' + ');')();
                } catch (_0x2d1c8e) {
                    _0x13d88d = window;
                }
                return _0x13d88d;
            };
            var _0x294290 = _0x27b106();
            var _0x36929c = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
            _0x294290['atob'] || (_0x294290['atob'] = function (_0x4470b3) {
                var _0x23c7c0 = String(_0x4470b3)['replace'](/=+$/, '');
                for (var _0x5d25cc = 0x0, _0x2da573, _0x5b16b3, _0x4ddd92 = 0x0, _0x4fd490 = ''; _0x5b16b3 = _0x23c7c0['charAt'](_0x4ddd92++); ~_0x5b16b3 && (_0x2da573 = _0x5d25cc % 0x4 ? _0x2da573 * 0x40 + _0x5b16b3 : _0x5b16b3, _0x5d25cc++ % 0x4) ? _0x4fd490 += String['fromCharCode'](0xff & _0x2da573 >> (-0x2 * _0x5d25cc & 0x6)) : 0x0) {
                    _0x5b16b3 = _0x36929c['indexOf'](_0x5b16b3);
                }
                return _0x4fd490;
            });
        }());
        var _0x2a1528 = function (_0x4c429b, _0x1712a6) {
            var _0x3ea182 = [], _0x3ce8c1 = 0x0, _0x3633fb, _0x83de4a = '', _0x428947 = '';
            _0x4c429b = atob(_0x4c429b);
            for (var _0x504ad2 = 0x0, _0x12a077 = _0x4c429b['length']; _0x504ad2 < _0x12a077; _0x504ad2++) {
                _0x428947 += '%' + ('00' + _0x4c429b['charCodeAt'](_0x504ad2)['toString'](0x10))['slice'](-0x2);
            }
            _0x4c429b = decodeURIComponent(_0x428947);
            for (var _0x5f5c42 = 0x0; _0x5f5c42 < 0x100; _0x5f5c42++) {
                _0x3ea182[_0x5f5c42] = _0x5f5c42;
            }
            for (_0x5f5c42 = 0x0; _0x5f5c42 < 0x100; _0x5f5c42++) {
                _0x3ce8c1 = (_0x3ce8c1 + _0x3ea182[_0x5f5c42] + _0x1712a6['charCodeAt'](_0x5f5c42 % _0x1712a6['length'])) % 0x100;
                _0x3633fb = _0x3ea182[_0x5f5c42];
                _0x3ea182[_0x5f5c42] = _0x3ea182[_0x3ce8c1];
                _0x3ea182[_0x3ce8c1] = _0x3633fb;
            }
            _0x5f5c42 = 0x0;
            _0x3ce8c1 = 0x0;
            for (var _0x3ba77e = 0x0; _0x3ba77e < _0x4c429b['length']; _0x3ba77e++) {
                _0x5f5c42 = (_0x5f5c42 + 0x1) % 0x100;
                _0x3ce8c1 = (_0x3ce8c1 + _0x3ea182[_0x5f5c42]) % 0x100;
                _0x3633fb = _0x3ea182[_0x5f5c42];
                _0x3ea182[_0x5f5c42] = _0x3ea182[_0x3ce8c1];
                _0x3ea182[_0x3ce8c1] = _0x3633fb;
                _0x83de4a += String['fromCharCode'](_0x4c429b['charCodeAt'](_0x3ba77e) ^ _0x3ea182[(_0x3ea182[_0x5f5c42] + _0x3ea182[_0x3ce8c1]) % 0x100]);
            }
            return _0x83de4a;
        };
        _0x5736['DtABuR'] = _0x2a1528;
        _0x5736['fIbGGr'] = {};
        _0x5736['JsbGza'] = !![];
    }
    var _0x23c2cc = _0x5736['fIbGGr'][_0x2d342f];
    if (_0x23c2cc === undefined) {
        if (_0x5736['xqYGvo'] === undefined) {
            _0x5736['xqYGvo'] = !![];
        }
        _0x2c0933 = _0x5736['DtABuR'](_0x2c0933, _0x1712a6);
        _0x5736['fIbGGr'][_0x2d342f] = _0x2c0933;
    } else {
        _0x2c0933 = _0x23c2cc;
    }
    return _0x2c0933;
};
let fs = require('fs'), config = require(_0x5736('0x0', 'ObK(')), app = require(_0x5736('0x1', 'tWl!'))(), server, getProtocolOptions = () => config[_0x5736('0x2', 'oL!1')] ? {
        'protocol': 'https',
        'protocolOptions': {
            'key': fs[_0x5736('0x3', 'FaaY')](config['ssl']['key']),
            'cert': fs[_0x5736('0x4', '1!AO')](config[_0x5736('0x5', '![7#')][_0x5736('0x6', 'vVC1')])
        }
    } : { 'protocol': _0x5736('0x7', 'BqD#') },
    options = getProtocolOptions(), fakeStatus = 0x0;
if (options[_0x5736('0x8', 'adyp')] == _0x5736('0x9', ')g1@'))
    server = require('https')[_0x5736('0xa', 'N091')](options[_0x5736('0xb', 'bI5T')], app);
else
    server = require(_0x5736('0xc', 'm5^h'))['createServer'](app);
let io = require('socket.io')(server), redis = require(_0x5736('0xd', ')Ua5')), redisClient = redis[_0x5736('0xe', 'Zm[%')]({ 'path': _0x5736('0xf', 'mDLN') }), requestify = require(_0x5736('0x10', 'Zm[%')), acho = require(_0x5736('0x11', 'sq)h')), log = acho({ 'upper': !![] }), online = 0x0, ipsConnected = [];
server[_0x5736('0x12', ')g1@')](config['port']);
log['info'](_0x5736('0x13', 'juTf') + options['protocol'] + _0x5736('0x14', 'm5^h') + config[_0x5736('0x15', 'oL!1')] + ':' + config[_0x5736('0x16', ')Ua5')]);
io[_0x5736('0x17', 'oL!1')]['on'](_0x5736('0x18', 'BDA@'), function (_0x23e5d9) {
    var _0x45977c = _0x23e5d9[_0x5736('0x19', 'vVC1')][_0x5736('0x1a', 'knlk')];
    if (!ipsConnected[_0x5736('0x1b', 'souj')](_0x45977c)) {
        ipsConnected[_0x45977c] = 0x1;
        online = online + 0x1;
    }
    updateOnline(online);
    _0x23e5d9['on'](_0x5736('0x1c', 'VoP4'), function () {
        if (ipsConnected[_0x5736('0x1d', 'juTf')](_0x45977c)) {
            delete ipsConnected[_0x45977c];
            online = online - 0x1;
        }
        updateOnline(online);
    });
});
function updateOnline(_0x459ef8) {
    io[_0x5736('0x1e', 'y5R)')][_0x5736('0x1f', 'knlk')](_0x5736('0x20', 'mDLN'), _0x459ef8);
}
redisClient[_0x5736('0x21', 'BqD#')](_0x5736('0x22', 'feJp'));
redisClient[_0x5736('0x23', 'm5^h')](_0x5736('0x24', 'XveD'));
redisClient[_0x5736('0x25', 'RP44')](_0x5736('0x26', 'bI5T'));
redisClient[_0x5736('0x25', 'RP44')](_0x5736('0x27', 'oL!1'));
redisClient['subscribe'](_0x5736('0x28', 'bI5T'));
redisClient[_0x5736('0x29', '1!AO')]('updateBalance');
redisClient[_0x5736('0x2a', 'juTf')](_0x5736('0x2b', 'gY#j'));
redisClient[_0x5736('0x2c', 'SscK')]('updateBonus');
redisClient[_0x5736('0x2d', 'y5R)')](_0x5736('0x2e', 'N091'));
redisClient['subscribe'](_0x5736('0x2f', 'SscK'));
redisClient[_0x5736('0x30', 'x$FD')](_0x5736('0x31', 'm5^h'));
redisClient[_0x5736('0x32', '1xWz')](_0x5736('0x33', '@p!4'));
redisClient[_0x5736('0x34', 'BKni')](_0x5736('0x35', 'BDA@'));
redisClient['subscribe'](_0x5736('0x36', 'x$FD'));
redisClient[_0x5736('0x37', 'ObK(')](_0x5736('0x38', 'bI5T'));
redisClient[_0x5736('0x2a', 'juTf')]('end.flip');
redisClient[_0x5736('0x39', '0CjW')](_0x5736('0x3a', 'feJp'));
redisClient[_0x5736('0x25', 'RP44')](_0x5736('0x3b', 'knlk'));
redisClient[_0x5736('0x39', '0CjW')](_0x5736('0x3c', '1xWz'));
redisClient['subscribe'](_0x5736('0x3d', 'BKni'));
redisClient[_0x5736('0x34', 'BKni')](_0x5736('0x3e', 'm5^h'));
redisClient[_0x5736('0x3f', 'kEul')](_0x5736('0x40', 'x$FD'));
redisClient['on'](_0x5736('0x41', '1xWz'), function (_0x4a49cc, _0x19151f) {
    if (_0x4a49cc == _0x5736('0x42', '@p!4'))
        io['sockets'][_0x5736('0x43', 'juTf')](_0x5736('0x44', 'SscK'), JSON[_0x5736('0x45', 'NpOR')](_0x19151f));
    if (_0x4a49cc == _0x5736('0x46', ')g1@'))
        io[_0x5736('0x47', 'm5^h')][_0x5736('0x48', 'yIe]')](_0x5736('0x49', 'SscK'), JSON[_0x5736('0x4a', 'x$FD')](_0x19151f));
    if (_0x4a49cc == _0x5736('0x4b', 'bI5T'))
        io[_0x5736('0x4c', ')[#F')]['emit'](_0x5736('0x4d', 'ObK('), JSON['parse'](_0x19151f));
    if (_0x4a49cc == _0x5736('0x4e', 'mDLN'))
        io[_0x5736('0x4f', 'vVC1')][_0x5736('0x50', 'N091')](_0x5736('0x51', '1xWz'), JSON[_0x5736('0x52', 'XveD')](_0x19151f));
    if (_0x4a49cc == 'updateBalanceAfter') {
        _0x19151f = JSON[_0x5736('0x53', 'u#Hd')](_0x19151f);
        setTimeout(function () {
            io[_0x5736('0x54', '8MA!')][_0x5736('0x55', '[t1W')](_0x5736('0x56', 'BqD#'), _0x19151f);
        }, _0x19151f['timer'] * 0x3e8);
    }
    if (_0x4a49cc == _0x5736('0x57', 'VoP4')) {
        _0x19151f = JSON[_0x5736('0x58', 'adyp')](_0x19151f);
        setTimeout(function () {
            io['sockets'][_0x5736('0x59', 'sq)h')]('updateBonus', _0x19151f);
        }, _0x19151f[_0x5736('0x5a', 'kEul')] * 0x3e8);
    }
    if (_0x4a49cc == _0x5736('0x5b', 'TdQz')) {
        _0x19151f = JSON['parse'](_0x19151f);
        startJackpotTimer(_0x19151f);
        return;
    }
    if (_0x4a49cc == _0x5736('0x5c', 'c*y[') && JSON['parse'](_0x19151f)[_0x5736('0x5d', 'A8wY')] == _0x5736('0x5e', 'm5^h')) {
        _0x19151f = JSON[_0x5736('0x5f', 'kEul')](_0x19151f);
        io[_0x5736('0x60', 'gY#j')][_0x5736('0x61', 'IflU')](_0x5736('0x62', 'XveD'), {
            'type': _0x5736('0x63', 'bI5T'),
            'data': _0x19151f[_0x5736('0x64', 'XveD')]
        });
        startGiveawayTimer(_0x19151f['data']);
        return;
    }
    if (_0x4a49cc == _0x5736('0x65', 'Dz1s')) {
        _0x19151f = JSON[_0x5736('0x58', 'adyp')](_0x19151f);
        startBattleTimer(_0x19151f[_0x5736('0x66', 'souj')]);
        return;
    }
    if (_0x4a49cc == _0x5736('0x67', 'DO53') && JSON[_0x5736('0x45', 'NpOR')](_0x19151f)[_0x5736('0x68', ')[#F')] == _0x5736('0x69', 'Zm[%')) {
        _0x19151f = JSON[_0x5736('0x6a', 'KNZI')](_0x19151f);
        startWheelTimer(_0x19151f[_0x5736('0x6b', ')Ua5')][0x2]);
        return;
    }
    if (typeof _0x19151f == 'string')
        return io[_0x5736('0x6c', 'Dz1s')][_0x5736('0x6d', ')Ua5')](_0x4a49cc, JSON[_0x5736('0x6e', 'feJp')](_0x19151f));
    io[_0x5736('0x6f', 'feJp')][_0x5736('0x70', 'adyp')](_0x4a49cc, _0x19151f);
});
var currentTimers = [];
function startJackpotTimer(_0x471a78) {
    if (typeof currentTimers[_0x471a78[_0x5736('0x71', 'FaaY')]] == _0x5736('0x72', 'IflU'))
        currentTimers[_0x471a78[_0x5736('0x73', 'vVC1')]] = 0x0;
    if (currentTimers[_0x471a78[_0x5736('0x74', 'Dz1s')]] != 0x0 && currentTimers[_0x471a78[_0x5736('0x75', 'knlk')]] - new Date()[_0x5736('0x76', 'knlk')]() < (_0x471a78['time'] + 0x1) * 0x3e8)
        return;
    currentTimers[_0x471a78[_0x5736('0x73', 'vVC1')]] = new Date()[_0x5736('0x77', 'RP44')]();
    let _0x575d09 = _0x471a78[_0x5736('0x78', 'kEul')];
    let _0x48c9f5 = setInterval(() => {
        if (_0x575d09 == 0x0) {
            clearInterval(_0x48c9f5);
            io[_0x5736('0x79', '![7#')][_0x5736('0x7a', 'BqD#')](_0x5736('0x7b', ')g1@'), {
                'type': 'timer',
                'room': _0x471a78[_0x5736('0x7c', 'Zm[%')],
                'data': {
                    'min': Math[_0x5736('0x7d', '8MA!')](_0x575d09 / 0x3c),
                    'sec': _0x575d09 - Math[_0x5736('0x7e', 'vVC1')](_0x575d09 / 0x3c) * 0x3c
                }
            });
            currentTimers[_0x471a78[_0x5736('0x7f', 'c*y[')]] = 0x0;
            showJackpotSlider(_0x471a78[_0x5736('0x80', '8MA!')], _0x471a78[_0x5736('0x81', 'ObK(')]);
            return;
        }
        _0x575d09--;
        io['sockets'][_0x5736('0x82', 'lut@')](_0x5736('0x83', 'yIe]'), {
            'type': _0x5736('0x84', 'lut@'),
            'room': _0x471a78[_0x5736('0x85', 'juTf')],
            'data': {
                'min': Math[_0x5736('0x86', 'Zm[%')](_0x575d09 / 0x3c),
                'sec': _0x575d09 - Math[_0x5736('0x87', 'BqD#')](_0x575d09 / 0x3c) * 0x3c
            }
        });
    }, 0x1 * 0x3e8);
}
function showJackpotSlider(_0x3f7b5e, _0x27c8c7) {
    requestify['post'](options[_0x5736('0x88', '8MA!')] + _0x5736('0x89', 'sq)h') + config[_0x5736('0x8a', 'BDA@')] + _0x5736('0x8b', 'mDLN'), {
        'room': _0x3f7b5e,
        'game': _0x27c8c7
    })[_0x5736('0x8c', 'adyp')](function (_0x1f9028) {
        let _0x3f4b78 = setTimeout(() => {
            clearInterval(_0x3f4b78);
            newJackpotGame(_0x3f7b5e);
        }, 0xc * 0x3e8);
    }, function (_0x5307a) {
        log[_0x5736('0x8d', '[t1W')](_0x5736('0x8e', '8MA!'));
    });
}
function newJackpotGame(_0x164281) {
    requestify[_0x5736('0x8f', 'gY#j')](options['protocol'] + '://' + config[_0x5736('0x90', 'mDLN')] + _0x5736('0x91', 'BqD#'), { 'room': _0x164281 })[_0x5736('0x92', 'Zm[%')](function (_0x39b8d8) {
        _0x39b8d8 = JSON[_0x5736('0x93', 'A8wY')](_0x39b8d8[_0x5736('0x94', 'y5R)')]);
        io[_0x5736('0x95', 'VoP4')][_0x5736('0x96', 'A8wY')]('jackpot', {
            'type': 'newGame',
            'room': _0x164281,
            'data': _0x39b8d8
        });
    }, function (_0x440217) {
        log[_0x5736('0x97', ')[#F')](_0x5736('0x98', 'bI5T') + _0x164281 + _0x5736('0x99', 'IflU'));
    });
}
function getStatusJackpot(_0x524f0b) {
    requestify[_0x5736('0x9a', 'VoP4')](options[_0x5736('0x88', '8MA!')] + _0x5736('0x9b', 'DO53') + config[_0x5736('0x9c', 'yIe]')] + _0x5736('0x9d', 'tWl!'), { 'room': _0x524f0b })[_0x5736('0x9e', '8MA!')](function (_0x353d3d) {
        _0x353d3d = JSON[_0x5736('0x9f', '0CjW')](_0x353d3d[_0x5736('0xa0', 'x$FD')]);
        if (_0x353d3d[_0x5736('0xa1', 'DO53')] == 0x1)
            startJackpotTimer(_0x353d3d);
        if (_0x353d3d[_0x5736('0xa2', ')g1@')] == 0x2)
            showJackpotSlider(_0x353d3d[_0x5736('0xa3', 'BqD#')], _0x353d3d[_0x5736('0xa4', 'KNZI')]);
        if (_0x353d3d['status'] == 0x3)
            newJackpotGame(_0x353d3d[_0x5736('0xa5', 'sq)h')]);
    }, function (_0x13bb3c) {
        log[_0x5736('0xa6', 'A8wY')](_0x5736('0xa7', 'A8wY') + _0x524f0b + _0x5736('0xa8', 'Zm[%'));
    });
}
requestify['post'](options[_0x5736('0xa9', 'souj')] + _0x5736('0xaa', 'N091') + config[_0x5736('0xab', '![7#')] + _0x5736('0xac', ')g1@'))[_0x5736('0xad', 'TdQz')](function (_0x138f47) {
    rooms = JSON[_0x5736('0xae', '5DAG')](_0x138f47[_0x5736('0xaf', 'A8wY')]);
    rooms['forEach'](function (_0x2455cf) {
        getStatusJackpot(_0x2455cf[_0x5736('0xb0', 'sq)h')]);
    });
}, function (_0x5345b3) {
    log[_0x5736('0xb1', 'c6ps')](_0x5345b3['body']);
    log[_0x5736('0xb2', 'c*y[')](_0x5736('0xb3', '5DAG'));
});
function startWheelTimer(_0x20c76f) {
    let _0x2872bd = setInterval(() => {
        if (_0x20c76f == 0x0) {
            clearInterval(_0x2872bd);
            io[_0x5736('0xb4', 'bI5T')]['emit'](_0x5736('0xb5', '1!AO'), {
                'type': 'timer',
                'min': Math[_0x5736('0xb6', 'sq)h')](_0x20c76f / 0x3c),
                'sec': _0x20c76f - Math[_0x5736('0xb7', 'adyp')](_0x20c76f / 0x3c) * 0x3c
            });
            showWheelSlider();
            return;
        }
        _0x20c76f--;
        io[_0x5736('0xb8', 'IflU')][_0x5736('0xb9', '1xWz')](_0x5736('0xba', 'BKni'), {
            'type': _0x5736('0xbb', 'adyp'),
            'min': Math[_0x5736('0xbc', 'juTf')](_0x20c76f / 0x3c),
            'sec': _0x20c76f - Math[_0x5736('0x87', 'BqD#')](_0x20c76f / 0x3c) * 0x3c
        });
    }, 0x1 * 0x3e8);
}
function showWheelSlider() {
    requestify[_0x5736('0x9a', 'VoP4')](options[_0x5736('0xbd', '![7#')] + _0x5736('0xbe', 'TdQz') + config[_0x5736('0xbf', 'KNZI')] + _0x5736('0xc0', '8MA!'))['then'](function (_0x1e8686) {
        _0x1e8686 = JSON[_0x5736('0x6e', 'feJp')](_0x1e8686[_0x5736('0xc1', 'BKni')]);
        setTimeout(() => {
            newWheelGame();
        }, _0x1e8686[_0x5736('0xc2', 'knlk')]);
    }, function (_0xe8e76) {
        log[_0x5736('0xb2', 'c*y[')]('Erro\x20в\x20функции\x20wheelSlider');
    });
}
function newWheelGame() {
    requestify[_0x5736('0xc3', 'NpOR')](options[_0x5736('0xc4', 'yIe]')] + _0x5736('0xc5', 'adyp') + config['domain'] + _0x5736('0xc6', 'feJp'))[_0x5736('0xc7', 'IflU')](function (_0x1b494c) {
        _0x1b494c = JSON[_0x5736('0xc8', 'yIe]')](_0x1b494c[_0x5736('0xc9', 'c*y[')]);
    }, function (_0x39aa53) {
        log[_0x5736('0xca', 'VoP4')](_0x5736('0xcb', 'yIe]'));
    });
}
requestify[_0x5736('0xcc', 'juTf')](options[_0x5736('0xcd', 'FaaY')] + _0x5736('0xce', 'Dz1s') + config[_0x5736('0x9c', 'yIe]')] + '/api/wheel/getGame')['then'](function (_0x10a233) {
    _0x10a233 = JSON[_0x5736('0xcf', 'lut@')](_0x10a233[_0x5736('0xd0', 'BDA@')]);
    if (_0x10a233[_0x5736('0xd1', 'yIe]')] == 0x1)
        startWheelTimer(_0x10a233[_0x5736('0xd2', '@p!4')][0x2]);
    if (_0x10a233[_0x5736('0xd3', 'feJp')] == 0x2)
        startWheelTimer(_0x10a233['timer'][0x2]);
    if (_0x10a233['status'] == 0x3)
        newWheelGame();
}, function (_0x4cfb18) {
    log[_0x5736('0xd4', 'Dz1s')](_0x5736('0xd5', 'c*y['));
});
function startBattleTimer(_0x52d089) {
    setBattleStatus(0x1);
    let _0x3e2d5b = setInterval(() => {
        if (_0x52d089 == 0x0) {
            clearInterval(_0x3e2d5b);
            io['sockets'][_0x5736('0x59', 'sq)h')]('battle.timer', {
                'min': Math[_0x5736('0xd6', 'bI5T')](_0x52d089 / 0x3c),
                'sec': _0x52d089 - Math[_0x5736('0xd7', 'x$FD')](_0x52d089 / 0x3c) * 0x3c
            });
            setBattleStatus(0x2);
            showBattleWinners();
            return;
        }
        _0x52d089--;
        io[_0x5736('0xd8', '@p!4')][_0x5736('0xd9', 'ObK(')](_0x5736('0xda', 'mDLN'), {
            'min': Math['floor'](_0x52d089 / 0x3c),
            'sec': _0x52d089 - Math[_0x5736('0xdb', 'NpOR')](_0x52d089 / 0x3c) * 0x3c
        });
    }, 0x1 * 0x3e8);
}
function showBattleWinners() {
    requestify[_0x5736('0xdc', '1!AO')](options[_0x5736('0xdd', 'SscK')] + '://' + config[_0x5736('0xde', 'tWl!')] + _0x5736('0xdf', 'KNZI'))[_0x5736('0xad', 'TdQz')](function (_0x5c5ae8) {
        _0x5c5ae8 = JSON[_0x5736('0xe0', 'Dz1s')](_0x5c5ae8[_0x5736('0xc9', 'c*y[')]);
        io[_0x5736('0xe1', 'BKni')]['emit'](_0x5736('0xe2', 'u#Hd'), _0x5c5ae8);
        setBattleStatus(0x3);
        ngTimerBattle();
    }, function (_0x14fa91) {
        log['error'](_0x5736('0xe3', 'BDA@'));
        setTimeout(BattleShowWinners, 0x3e8);
    });
}
function ngTimerBattle() {
    var _0x2b22b5 = 0x6;
    var _0x508ac8 = setInterval(function () {
        _0x2b22b5--;
        if (_0x2b22b5 == 0x0) {
            clearInterval(_0x508ac8);
            newBattleGame();
        }
    }, 0x3e8);
}
function newBattleGame() {
    requestify[_0x5736('0xc3', 'NpOR')](options[_0x5736('0xe4', ')[#F')] + _0x5736('0xe5', 'souj') + config[_0x5736('0x9c', 'yIe]')] + _0x5736('0xe6', 'ObK('))[_0x5736('0xe7', 'mDLN')](function (_0x218d20) {
        _0x218d20 = JSON[_0x5736('0xe8', 'RP44')](_0x218d20[_0x5736('0xe9', 'souj')]);
        io[_0x5736('0xea', 'juTf')][_0x5736('0xeb', 'XveD')]('battle.newGame', _0x218d20);
    }, function (_0x3c0c91) {
        log[_0x5736('0xec', 'tWl!')](_0x5736('0xed', 'x$FD'));
        setTimeout(newBattleGame, 0x3e8);
    });
}
function setBattleStatus(_0x45deef) {
    requestify[_0x5736('0xee', 'BKni')](options[_0x5736('0xc4', 'yIe]')] + _0x5736('0xef', 'mDLN') + config[_0x5736('0xf0', 'juTf')] + _0x5736('0xf1', '5DAG'), { 'status': _0x45deef })[_0x5736('0x92', 'Zm[%')](function (_0x1420e5) {
        _0x45deef = JSON[_0x5736('0xf2', '1xWz')](_0x1420e5[_0x5736('0xf3', 'FaaY')]);
    }, function (_0x387cd6) {
        log[_0x5736('0xf4', '5DAG')](_0x5736('0xf5', 'XveD'));
        setTimeout(setBattleStatus, 0x3e8);
    });
}
requestify[_0x5736('0xf6', '![7#')](options[_0x5736('0xcd', 'FaaY')] + _0x5736('0xf7', 'gY#j') + config[_0x5736('0xf8', ')g1@')] + _0x5736('0xf9', 'BDA@'))['then'](function (_0x44a229) {
    _0x44a229 = JSON[_0x5736('0xfa', 'c*y[')](_0x44a229[_0x5736('0xfb', 'Zm[%')]);
    if (_0x44a229[_0x5736('0xfc', 'tWl!')] == 0x1)
        startBattleTimer(_0x44a229[_0x5736('0xfd', 'feJp')]);
    if (_0x44a229[_0x5736('0xfe', 'Dz1s')] == 0x2)
        startBattleTimer(_0x44a229[_0x5736('0xff', 'TdQz')]);
    if (_0x44a229[_0x5736('0x100', '[t1W')] == 0x3)
        newBattleGame();
}, function (_0x78e2cf) {
    log[_0x5736('0x101', 'vVC1')](_0x5736('0x102', '![7#'));
});
function unBan() {
    requestify['post'](options[_0x5736('0x103', 'VoP4')] + _0x5736('0x104', 'kEul') + config[_0x5736('0x105', 'N091')] + _0x5736('0x106', 'A8wY'))['then'](function (_0xc90be4) {
        var _0x4790df = JSON['parse'](_0xc90be4[_0x5736('0xd0', 'BDA@')]);
        setTimeout(unBan, 0xea60);
    }, function (_0x59f480) {
        log[_0x5736('0x107', 'Zm[%')](_0x5736('0x108', 'TdQz'));
        setTimeout(unBan, 0x3e8);
    });
}
function getMerchBalance() {
    requestify[_0x5736('0x109', '[t1W')](options['protocol'] + '://' + config[_0x5736('0x10a', 'bI5T')] + '/api/getMerchBalance')['then'](function (_0x35e05d) {
        var _0x165f61 = JSON[_0x5736('0x10b', '@p!4')](_0x35e05d[_0x5736('0x10c', 'DO53')]);
        setTimeout(getMerchBalance, 0x927c0);
    }, function (_0x820157) {
        log[_0x5736('0x10d', 'adyp')](_0x5736('0x10e', 'm5^h'));
        setTimeout(getMerchBalance, 0x3e8);
    });
}
function getParam() {
    requestify[_0x5736('0x10f', 'mDLN')](options[_0x5736('0x110', 'c6ps')] + '://' + config[_0x5736('0xbf', 'KNZI')] + _0x5736('0x111', 'ObK('))[_0x5736('0x112', 'vVC1')](function (_0x5f496e) {
        var _0x556afe = JSON[_0x5736('0x93', 'A8wY')](_0x5f496e[_0x5736('0x113', 'IflU')]);
        if (_0x556afe[_0x5736('0x114', ')[#F')] && !fakeStatus) {
            fakeStatus = 0x1;
            fakeBetJackpot(_0x556afe[_0x5736('0x115', 'lut@')]);
            fakeBetWheel(_0x556afe[_0x5736('0x116', 'sq)h')]);
            fakeBetDice(_0x556afe[_0x5736('0x117', 'A8wY')]);
            fakeBetBattle(_0x556afe['fake']);
        } else {
            fakeStatus = 0x0;
            setTimeout(getParam, 0x1388);
        }
    }, function (_0x458439) {
        log[_0x5736('0xb2', 'c*y[')](_0x458439);
        log[_0x5736('0x10d', 'adyp')](_0x5736('0x118', '5DAG'));
        setTimeout(getParam, 0x3e8);
    });
}
function fakeBetJackpot(_0x4cf945) {
    if (_0x4cf945) {
        requestify[_0x5736('0xdc', '1!AO')](options[_0x5736('0x119', ')Ua5')] + _0x5736('0x11a', 'juTf') + config[_0x5736('0x11b', 'RP44')] + _0x5736('0x11c', '5DAG'))[_0x5736('0x11d', 'gY#j')](function (_0x4ad003) {
            _0x4ad003 = JSON['parse'](_0x4ad003['body']);
            if (!_0x4ad003[_0x5736('0x11e', 'IflU')])
                fakeStatus = 0x0;
            setTimeout(function () {
                fakeBetJackpot(fakeStatus);
            }, Math[_0x5736('0x11f', 'BDA@')](getRandomArbitrary(0x5, 0x11) * 0x3e8));
        }, function (_0x284339) {
            log[_0x5736('0x10d', 'adyp')](_0x5736('0x120', 'juTf'));
            setTimeout(function () {
                fakeBetJackpot(fakeStatus);
            }, Math[_0x5736('0x121', 'x$FD')](getRandomArbitrary(0x5, 0x11) * 0x3e8));
        });
    } else {
        setTimeout(getParam, 0x1388);
    }
}
function fakeBetWheel(_0x14aa08) {
    if (_0x14aa08) {
        requestify[_0x5736('0x122', 'BqD#')](options['protocol'] + '://' + config[_0x5736('0x10a', 'bI5T')] + _0x5736('0x123', 'mDLN'))[_0x5736('0x124', 'c*y[')](function (_0x165241) {
            _0x165241 = JSON[_0x5736('0x125', 'TdQz')](_0x165241[_0x5736('0xc1', 'BKni')]);
            if (!_0x165241[_0x5736('0x126', 'N091')])
                fakeStatus = 0x0;
            setTimeout(function () {
                fakeBetWheel(fakeStatus);
            }, Math[_0x5736('0x127', 'KNZI')](getRandomArbitrary(0x1, 0x8) * 0x3e8));
        }, function (_0x3689bb) {
            log['error'](_0x5736('0x128', 'KNZI'));
            setTimeout(function () {
                fakeBetWheel(fakeStatus);
            }, Math[_0x5736('0x129', '0CjW')](getRandomArbitrary(0x1, 0x8) * 0x3e8));
        });
    } else {
        setTimeout(getParam, 0x1388);
    }
}
function fakeBetDice(_0x4c0073) {
    if (_0x4c0073) {
        requestify['post'](options[_0x5736('0x12a', 'bI5T')] + _0x5736('0x12b', 'Zm[%') + config[_0x5736('0x10a', 'bI5T')] + _0x5736('0x12c', 'TdQz'))['then'](function (_0x5ccc3e) {
            _0x5ccc3e = JSON[_0x5736('0x125', 'TdQz')](_0x5ccc3e['body']);
            if (!_0x5ccc3e[_0x5736('0x12d', 'BDA@')])
                fakeStatus = 0x0;
            setTimeout(function () {
                fakeBetDice(fakeStatus);
            }, Math[_0x5736('0x12e', 'Zm[%')](getRandomArbitrary(0x1, 0x3) * 0x3e8));
        }, function (_0x2bd5e2) {
            log[_0x5736('0x12f', '0CjW')](_0x5736('0x130', 'RP44'));
            setTimeout(function () {
                fakeBetDice(fakeStatus);
            }, Math[_0x5736('0x131', 'ObK(')](getRandomArbitrary(0x1, 0x3) * 0x3e8));
        });
    } else {
        setTimeout(getParam, 0x1388);
    }
}
function fakeBetBattle(_0x4b6b94) {
    if (_0x4b6b94) {
        requestify[_0x5736('0x132', 'Zm[%')](options[_0x5736('0x133', '1xWz')] + _0x5736('0x134', 'NpOR') + config[_0x5736('0xbf', 'KNZI')] + _0x5736('0x135', 'u#Hd'))[_0x5736('0xad', 'TdQz')](function (_0x551abe) {
            _0x551abe = JSON[_0x5736('0x6e', 'feJp')](_0x551abe['body']);
            if (!_0x551abe[_0x5736('0x12d', 'BDA@')])
                fakeStatus = 0x0;
            setTimeout(function () {
                fakeBetBattle(fakeStatus);
            }, Math[_0x5736('0x136', 'oL!1')](getRandomArbitrary(0x1, 0x3) * 0x3e8));
        }, function (_0x1d1c3a) {
            log[_0x5736('0x12f', '0CjW')](_0x5736('0x137', 'souj'));
            setTimeout(function () {
                fakeBetBattle(fakeStatus);
            }, Math['round'](getRandomArbitrary(0x1, 0x3) * 0x3e8));
        });
    } else {
        setTimeout(getParam, 0x1388);
    }
}
function getGiveaway() {
    requestify[_0x5736('0x122', 'BqD#')](options['protocol'] + '://' + config[_0x5736('0x138', 'FaaY')] + _0x5736('0x139', 'c6ps'))['then'](function (_0x28bdb9) {
        _0x28bdb9 = JSON[_0x5736('0x13a', 'souj')](_0x28bdb9[_0x5736('0xd0', 'BDA@')]);
        var _0x39ff0a = Math[_0x5736('0x13b', 'RP44')](new Date()[_0x5736('0x13c', ')Ua5')]() / 0x3e8);
        _0x28bdb9[_0x5736('0x13d', 'A8wY')](function (_0x2e362f) {
            if (_0x39ff0a < _0x2e362f[_0x5736('0x13e', 'KNZI')])
                startGiveawayTimer(_0x2e362f);
        });
    }, function (_0x2ff877) {
        log[_0x5736('0x13f', 'lut@')](_0x5736('0x140', '5DAG'));
        setTimeout(function () {
            getGiveaway();
        }, 0x1388);
    });
}
function startGiveawayTimer(_0x133ae6) {
    var _0x1f718f = Math[_0x5736('0x141', 'A8wY')](new Date()[_0x5736('0x142', 'gY#j')]() / 0x3e8);
    var _0x5420d7 = _0x133ae6[_0x5736('0x143', 'Zm[%')] - _0x1f718f;
    let _0x40d5c5 = setInterval(() => {
        if (_0x5420d7 == 0x0) {
            clearInterval(_0x40d5c5);
            io[_0x5736('0x144', 'adyp')]['emit'](_0x5736('0x145', 'A8wY'), {
                'type': _0x5736('0x146', '![7#'),
                'id': _0x133ae6['id'],
                'status': _0x133ae6[_0x5736('0xd1', 'yIe]')],
                'hour': (_0x5420d7 - _0x5420d7 % 0xe10) / 0xe10 % 0x3c < 0xa ? '0' + (_0x5420d7 - _0x5420d7 % 0xe10) / 0xe10 % 0x3c : (_0x5420d7 - _0x5420d7 % 0xe10) / 0xe10 % 0x3c,
                'min': (_0x5420d7 - _0x5420d7 % 0x3c) / 0x3c % 0x3c < 0xa ? '0' + (_0x5420d7 - _0x5420d7 % 0x3c) / 0x3c % 0x3c : (_0x5420d7 - _0x5420d7 % 0x3c) / 0x3c % 0x3c,
                'sec': _0x5420d7 % 0x3c < 0xa ? '0' + _0x5420d7 % 0x3c : _0x5420d7 % 0x3c
            });
            setGiveawayWinner(_0x133ae6['id']);
            return;
        }
        _0x5420d7--;
        io[_0x5736('0x95', 'VoP4')]['emit'](_0x5736('0x147', 'FaaY'), {
            'type': _0x5736('0x148', 'mDLN'),
            'id': _0x133ae6['id'],
            'status': _0x133ae6[_0x5736('0xa1', 'DO53')],
            'hour': (_0x5420d7 - _0x5420d7 % 0xe10) / 0xe10 % 0x3c < 0xa ? '0' + (_0x5420d7 - _0x5420d7 % 0xe10) / 0xe10 % 0x3c : (_0x5420d7 - _0x5420d7 % 0xe10) / 0xe10 % 0x3c,
            'min': (_0x5420d7 - _0x5420d7 % 0x3c) / 0x3c % 0x3c < 0xa ? '0' + (_0x5420d7 - _0x5420d7 % 0x3c) / 0x3c % 0x3c : (_0x5420d7 - _0x5420d7 % 0x3c) / 0x3c % 0x3c,
            'sec': _0x5420d7 % 0x3c < 0xa ? '0' + _0x5420d7 % 0x3c : _0x5420d7 % 0x3c
        });
    }, 0x1 * 0x3e8);
}
function setGiveawayWinner(_0x31174d) {
    requestify[_0x5736('0x149', 'A8wY')](options[_0x5736('0x14a', 'lut@')] + _0x5736('0x134', 'NpOR') + config[_0x5736('0x14b', 'VoP4')] + _0x5736('0x14c', 'VoP4'), { 'id': _0x31174d })[_0x5736('0x14d', 'ObK(')](function (_0xaad66c) {
        _0xaad66c = JSON[_0x5736('0x53', 'u#Hd')](_0xaad66c[_0x5736('0x14e', 'mDLN')]);
    }, function (_0x47119d) {
        log[_0x5736('0x101', 'vVC1')](_0x5736('0x14f', 'IflU'));
        setTimeout(function () {
            getGiveaway();
        }, 0x1388);
    });
}
function getRandomArbitrary(_0x1c310b, _0x2e5c99) {
    return Math['random']() * (_0x2e5c99 - _0x1c310b) + _0x1c310b;
}
unBan();
getMerchBalance();
getParam();
getGiveaway();