{"version": 3, "names": ["_helperSkipTransparentExpressionWrappers", "require", "_core", "matchAffectedArguments", "argumentNodes", "spreadIndex", "findIndex", "node", "t", "isSpreadElement", "length", "shouldTransform", "path", "optionalPath", "chains", "isOptionalMemberExpression", "push", "skipTransparentExprWrappers", "get", "isOptionalCallExpression", "i", "arguments", "optional", "callee"], "sources": ["../src/util.ts"], "sourcesContent": ["import { skipTransparentExprWrappers } from \"@babel/helper-skip-transparent-expression-wrappers\";\nimport type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\n// https://crbug.com/v8/11558\n\n// check if there is a spread element followed by another argument.\n// (...[], 0) or (...[], ...[])\n\nfunction matchAffectedArguments(argumentNodes: t.CallExpression[\"arguments\"]) {\n  const spreadIndex = argumentNodes.findIndex(node => t.isSpreadElement(node));\n  return spreadIndex >= 0 && spreadIndex !== argumentNodes.length - 1;\n}\n\n/**\n * Check whether the optional chain is affected by https://crbug.com/v8/11558.\n * This routine MUST not manipulate NodePath\n *\n * @export\n * @param {(NodePath<t.OptionalMemberExpression | t.OptionalCallExpression>)} path\n * @returns {boolean}\n */\nexport function shouldTransform(\n  path: NodePath<t.OptionalMemberExpression | t.OptionalCallExpression>,\n): boolean {\n  let optionalPath: NodePath<t.Expression> = path;\n  const chains: (t.OptionalCallExpression | t.OptionalMemberExpression)[] = [];\n  for (;;) {\n    if (optionalPath.isOptionalMemberExpression()) {\n      chains.push(optionalPath.node);\n      optionalPath = skipTransparentExprWrappers(optionalPath.get(\"object\"));\n    } else if (optionalPath.isOptionalCallExpression()) {\n      chains.push(optionalPath.node);\n      optionalPath = skipTransparentExprWrappers(optionalPath.get(\"callee\"));\n    } else {\n      break;\n    }\n  }\n  for (let i = 0; i < chains.length; i++) {\n    const node = chains[i];\n    if (\n      t.isOptionalCallExpression(node) &&\n      matchAffectedArguments(node.arguments)\n    ) {\n      // f?.(...[], 0)\n      if (node.optional) {\n        return true;\n      }\n      // o?.m(...[], 0)\n      // when node.optional is false, chains[i + 1] is always well defined\n      const callee = chains[i + 1];\n      if (t.isOptionalMemberExpression(callee, { optional: true })) {\n        return true;\n      }\n    }\n  }\n  return false;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,wCAAA,GAAAC,OAAA;AAEA,IAAAC,KAAA,GAAAD,OAAA;AAMA,SAASE,sBAAsBA,CAACC,aAA4C,EAAE;EAC5E,MAAMC,WAAW,GAAGD,aAAa,CAACE,SAAS,CAACC,IAAI,IAAIC,WAAC,CAACC,eAAe,CAACF,IAAI,CAAC,CAAC;EAC5E,OAAOF,WAAW,IAAI,CAAC,IAAIA,WAAW,KAAKD,aAAa,CAACM,MAAM,GAAG,CAAC;AACrE;AAUO,SAASC,eAAeA,CAC7BC,IAAqE,EAC5D;EACT,IAAIC,YAAoC,GAAGD,IAAI;EAC/C,MAAME,MAAiE,GAAG,EAAE;EAC5E,SAAS;IACP,IAAID,YAAY,CAACE,0BAA0B,CAAC,CAAC,EAAE;MAC7CD,MAAM,CAACE,IAAI,CAACH,YAAY,CAACN,IAAI,CAAC;MAC9BM,YAAY,GAAG,IAAAI,oEAA2B,EAACJ,YAAY,CAACK,GAAG,CAAC,QAAQ,CAAC,CAAC;IACxE,CAAC,MAAM,IAAIL,YAAY,CAACM,wBAAwB,CAAC,CAAC,EAAE;MAClDL,MAAM,CAACE,IAAI,CAACH,YAAY,CAACN,IAAI,CAAC;MAC9BM,YAAY,GAAG,IAAAI,oEAA2B,EAACJ,YAAY,CAACK,GAAG,CAAC,QAAQ,CAAC,CAAC;IACxE,CAAC,MAAM;MACL;IACF;EACF;EACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,MAAM,CAACJ,MAAM,EAAEU,CAAC,EAAE,EAAE;IACtC,MAAMb,IAAI,GAAGO,MAAM,CAACM,CAAC,CAAC;IACtB,IACEZ,WAAC,CAACW,wBAAwB,CAACZ,IAAI,CAAC,IAChCJ,sBAAsB,CAACI,IAAI,CAACc,SAAS,CAAC,EACtC;MAEA,IAAId,IAAI,CAACe,QAAQ,EAAE;QACjB,OAAO,IAAI;MACb;MAGA,MAAMC,MAAM,GAAGT,MAAM,CAACM,CAAC,GAAG,CAAC,CAAC;MAC5B,IAAIZ,WAAC,CAACO,0BAA0B,CAACQ,MAAM,EAAE;QAAED,QAAQ,EAAE;MAAK,CAAC,CAAC,EAAE;QAC5D,OAAO,IAAI;MACb;IACF;EACF;EACA,OAAO,KAAK;AACd"}