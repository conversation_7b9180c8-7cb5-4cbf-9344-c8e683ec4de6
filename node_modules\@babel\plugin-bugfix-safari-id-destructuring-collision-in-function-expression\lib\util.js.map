{"version": 3, "names": ["shouldTransform", "path", "node", "functionId", "id", "name", "paramNameBinding", "scope", "getOwnBinding", "undefined", "kind", "identifier"], "sources": ["../src/util.ts"], "sourcesContent": ["import type { FunctionExpression } from \"@babel/types\";\nimport type { NodePath } from \"@babel/traverse\";\n\n/**\n * Check whether a function expression can be affected by\n * https://bugs.webkit.org/show_bug.cgi?id=220517\n * @param path The function expression NodePath\n * @returns the name of function id if it should be transformed, otherwise returns false\n */\nexport function shouldTransform(\n  path: NodePath<FunctionExpression>,\n): string | false {\n  const { node } = path;\n  const functionId = node.id;\n  if (!functionId) return false;\n\n  const name = functionId.name;\n  // On collision, `getOwnBinding` returns the param binding\n  // with the id binding be registered as constant violation\n  const paramNameBinding = path.scope.getOwnBinding(name);\n  if (paramNameBinding === undefined) {\n    // Case 1: the function id is injected by babel-helper-name-function, which\n    // assigns `NOT_LOCAL_BINDING` to the `functionId` and thus not registering id\n    // in scope tracking\n    // Case 2: the function id is injected by a third party plugin which does not update the\n    // scope info\n    return false;\n  }\n  if (paramNameBinding.kind !== \"param\") {\n    // the function id does not reproduce in params\n    return false;\n  }\n\n  if (paramNameBinding.identifier === paramNameBinding.path.node) {\n    // the param binding is a simple parameter\n    // e.g. (function a(a) {})\n    return false;\n  }\n\n  return name;\n}\n"], "mappings": ";;;;;;AASO,SAASA,eAAeA,CAC7BC,IAAkC,EAClB;EAChB,MAAM;IAAEC;EAAK,CAAC,GAAGD,IAAI;EACrB,MAAME,UAAU,GAAGD,IAAI,CAACE,EAAE;EAC1B,IAAI,CAACD,UAAU,EAAE,OAAO,KAAK;EAE7B,MAAME,IAAI,GAAGF,UAAU,CAACE,IAAI;EAG5B,MAAMC,gBAAgB,GAAGL,IAAI,CAACM,KAAK,CAACC,aAAa,CAACH,IAAI,CAAC;EACvD,IAAIC,gBAAgB,KAAKG,SAAS,EAAE;IAMlC,OAAO,KAAK;EACd;EACA,IAAIH,gBAAgB,CAACI,IAAI,KAAK,OAAO,EAAE;IAErC,OAAO,KAAK;EACd;EAEA,IAAIJ,gBAAgB,CAACK,UAAU,KAAKL,gBAAgB,CAACL,IAAI,CAACC,IAAI,EAAE;IAG9D,OAAO,KAAK;EACd;EAEA,OAAOG,IAAI;AACb"}