const express = require('express');
const path = require('path');
const app = express();
const port = 8000;

// Serve static files from public directory
app.use(express.static('public'));

// Serve uploaded files
app.use('/uploaded_file', express.static('public/uploaded_file'));

// Basic route for testing
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Handle all other routes (for SPA)
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.listen(port, () => {
    console.log(`Server running at http://localhost:${port}`);
    console.log('Note: This is a basic static server. For full functionality, you need PHP and MySQL.');
});
