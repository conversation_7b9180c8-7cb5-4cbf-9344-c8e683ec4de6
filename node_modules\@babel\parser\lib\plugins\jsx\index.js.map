{"version": 3, "names": ["_xhtml", "require", "_types", "_context", "_identifier", "_whitespace", "_parseError", "JsxErrors", "ParseErrorEnum", "AttributeIsEmpty", "MissingClosingTagElement", "openingTagName", "MissingClosingTagFragment", "UnexpectedSequenceExpression", "UnexpectedToken", "unexpected", "HTMLEntity", "UnsupportedJsxValue", "UnterminatedJsxContent", "UnwrappedAdjacentJSXElements", "isFragment", "object", "type", "getQualifiedJSXName", "name", "namespace", "property", "Error", "_default", "superClass", "JSXParserMixin", "jsxReadToken", "out", "chunkStart", "state", "pos", "length", "raise", "at", "startLoc", "ch", "input", "charCodeAt", "start", "canStartJSXElement", "finishToken", "getTokenFromCode", "slice", "jsxReadEntity", "isNewLine", "jsxReadNewLine", "normalizeCRLF", "String", "fromCharCode", "curLine", "lineStart", "jsxReadString", "quote", "Errors", "UnterminatedString", "startPos", "codePointAtPos", "radix", "codePoint", "readInt", "undefined", "fromCodePoint", "count", "semi", "desc", "entity", "XHTMLEntities", "jsxReadWord", "isIdentifierChar", "jsxParseIdentifier", "node", "startNode", "match", "value", "tokenIsKeyword", "tokenLabelName", "next", "finishNode", "jsxParseNamespacedName", "eat", "startNodeAt", "jsxParseElementName", "newNode", "jsxParseAttributeValue", "setContext", "tc", "brace", "jsxParseExpressionContainer", "j_oTag", "expression", "parseExprAtom", "jsxParseEmptyExpression", "lastTokEndLoc", "finishNodeAt", "jsxParseSpreadChild", "parseExpression", "j_expr", "expect", "previousContext", "jsxParseAttribute", "argument", "parseMaybeAssignAllowIn", "jsxParseOpeningElementAt", "jsxParseOpeningElementAfterName", "attributes", "push", "selfClosing", "jsxParseClosingElementAt", "jsxParseElementAt", "children", "openingElement", "closingElement", "contents", "openingFragment", "closingFragment", "jsxParseElement", "newContext", "context", "refExpressionErrors", "parseLiteral", "replaceToken", "skipSpace", "cur<PERSON><PERSON><PERSON><PERSON>", "preserveSpace", "code", "j_cTag", "isIdentifierStart", "updateContext", "prevType", "splice", "pop", "tokenComesBeforeExpression", "exports", "default"], "sources": ["../../../src/plugins/jsx/index.ts"], "sourcesContent": ["import * as charCodes from \"charcodes\";\n\nimport XHTMLEntities from \"./xhtml\";\nimport type Parser from \"../../parser\";\nimport type { ExpressionErrors } from \"../../parser/util\";\nimport {\n  tokenComesBeforeExpression,\n  tokenIsKeyword,\n  tokenLabelName,\n  type TokenType,\n  tt,\n} from \"../../tokenizer/types\";\nimport type { TokContext } from \"../../tokenizer/context\";\nimport { types as tc } from \"../../tokenizer/context\";\nimport type * as N from \"../../types\";\nimport { isIdentifierChar, isIdentifierStart } from \"../../util/identifier\";\nimport type { Position } from \"../../util/location\";\nimport { isNewLine } from \"../../util/whitespace\";\nimport { Errors, ParseErrorEnum } from \"../../parse-error\";\nimport type { Undone } from \"../../parser/node\";\n\n/* eslint sort-keys: \"error\" */\nconst JsxErrors = ParseErrorEnum`jsx`({\n  AttributeIsEmpty:\n    \"JSX attributes must only be assigned a non-empty expression.\",\n  MissingClosingTagElement: ({ openingTagName }: { openingTagName: string }) =>\n    `Expected corresponding JSX closing tag for <${openingTagName}>.`,\n  MissingClosingTagFragment: \"Expected corresponding JSX closing tag for <>.\",\n  UnexpectedSequenceExpression:\n    \"Sequence expressions cannot be directly nested inside JSX. Did you mean to wrap it in parentheses (...)?\",\n  // FIXME: Unify with Errors.UnexpectedToken\n  UnexpectedToken: ({\n    unexpected,\n    HTMLEntity,\n  }: {\n    unexpected: string;\n    HTMLEntity: string;\n  }) =>\n    `Unexpected token \\`${unexpected}\\`. Did you mean \\`${HTMLEntity}\\` or \\`{'${unexpected}'}\\`?`,\n  UnsupportedJsxValue:\n    \"JSX value should be either an expression or a quoted JSX text.\",\n  UnterminatedJsxContent: \"Unterminated JSX contents.\",\n  UnwrappedAdjacentJSXElements:\n    \"Adjacent JSX elements must be wrapped in an enclosing tag. Did you want a JSX fragment <>...</>?\",\n});\n\n/* eslint-disable sort-keys */\n\nfunction isFragment(object?: N.JSXElement | null): boolean {\n  return object\n    ? object.type === \"JSXOpeningFragment\" ||\n        object.type === \"JSXClosingFragment\"\n    : false;\n}\n\n// Transforms JSX element name to string.\n\nfunction getQualifiedJSXName(\n  object: N.JSXIdentifier | N.JSXNamespacedName | N.JSXMemberExpression,\n): string {\n  if (object.type === \"JSXIdentifier\") {\n    return object.name;\n  }\n\n  if (object.type === \"JSXNamespacedName\") {\n    return object.namespace.name + \":\" + object.name.name;\n  }\n\n  if (object.type === \"JSXMemberExpression\") {\n    return (\n      getQualifiedJSXName(object.object) +\n      \".\" +\n      getQualifiedJSXName(object.property)\n    );\n  }\n\n  // istanbul ignore next\n  throw new Error(\"Node had unexpected type: \" + object.type);\n}\n\nexport interface IJSXParserMixin {\n  jsxParseOpeningElementAfterName(\n    node: N.JSXOpeningElement,\n  ): N.JSXOpeningElement;\n}\n\nexport default (superClass: typeof Parser) =>\n  class JSXParserMixin extends superClass implements Parser, IJSXParserMixin {\n    // Reads inline JSX contents token.\n\n    jsxReadToken(): void {\n      let out = \"\";\n      let chunkStart = this.state.pos;\n      for (;;) {\n        if (this.state.pos >= this.length) {\n          throw this.raise(JsxErrors.UnterminatedJsxContent, {\n            at: this.state.startLoc,\n          });\n        }\n\n        const ch = this.input.charCodeAt(this.state.pos);\n\n        switch (ch) {\n          case charCodes.lessThan:\n          case charCodes.leftCurlyBrace:\n            if (this.state.pos === this.state.start) {\n              if (ch === charCodes.lessThan && this.state.canStartJSXElement) {\n                ++this.state.pos;\n                this.finishToken(tt.jsxTagStart);\n              } else {\n                super.getTokenFromCode(ch);\n              }\n              return;\n            }\n            out += this.input.slice(chunkStart, this.state.pos);\n            this.finishToken(tt.jsxText, out);\n            return;\n\n          case charCodes.ampersand:\n            out += this.input.slice(chunkStart, this.state.pos);\n            out += this.jsxReadEntity();\n            chunkStart = this.state.pos;\n            break;\n\n          case charCodes.greaterThan:\n          case charCodes.rightCurlyBrace:\n            if (process.env.BABEL_8_BREAKING) {\n              this.raise(JsxErrors.UnexpectedToken, {\n                at: this.state.curPosition(),\n                unexpected: this.input[this.state.pos],\n                HTMLEntity:\n                  ch === charCodes.rightCurlyBrace ? \"&rbrace;\" : \"&gt;\",\n              });\n            }\n          /* falls through */\n\n          default:\n            if (isNewLine(ch)) {\n              out += this.input.slice(chunkStart, this.state.pos);\n              out += this.jsxReadNewLine(true);\n              chunkStart = this.state.pos;\n            } else {\n              ++this.state.pos;\n            }\n        }\n      }\n    }\n\n    jsxReadNewLine(normalizeCRLF: boolean): string {\n      const ch = this.input.charCodeAt(this.state.pos);\n      let out;\n      ++this.state.pos;\n      if (\n        ch === charCodes.carriageReturn &&\n        this.input.charCodeAt(this.state.pos) === charCodes.lineFeed\n      ) {\n        ++this.state.pos;\n        out = normalizeCRLF ? \"\\n\" : \"\\r\\n\";\n      } else {\n        out = String.fromCharCode(ch);\n      }\n      ++this.state.curLine;\n      this.state.lineStart = this.state.pos;\n\n      return out;\n    }\n\n    jsxReadString(quote: number): void {\n      let out = \"\";\n      let chunkStart = ++this.state.pos;\n      for (;;) {\n        if (this.state.pos >= this.length) {\n          throw this.raise(Errors.UnterminatedString, {\n            at: this.state.startLoc,\n          });\n        }\n\n        const ch = this.input.charCodeAt(this.state.pos);\n        if (ch === quote) break;\n        if (ch === charCodes.ampersand) {\n          out += this.input.slice(chunkStart, this.state.pos);\n          out += this.jsxReadEntity();\n          chunkStart = this.state.pos;\n        } else if (isNewLine(ch)) {\n          out += this.input.slice(chunkStart, this.state.pos);\n          out += this.jsxReadNewLine(false);\n          chunkStart = this.state.pos;\n        } else {\n          ++this.state.pos;\n        }\n      }\n      out += this.input.slice(chunkStart, this.state.pos++);\n      this.finishToken(tt.string, out);\n    }\n\n    jsxReadEntity(): string {\n      const startPos = ++this.state.pos;\n      if (this.codePointAtPos(this.state.pos) === charCodes.numberSign) {\n        ++this.state.pos;\n\n        let radix = 10;\n        if (this.codePointAtPos(this.state.pos) === charCodes.lowercaseX) {\n          radix = 16;\n          ++this.state.pos;\n        }\n\n        const codePoint = this.readInt(\n          radix,\n          /* len */ undefined,\n          /* forceLen */ false,\n          /* allowNumSeparator */ \"bail\",\n        );\n        if (\n          codePoint !== null &&\n          this.codePointAtPos(this.state.pos) === charCodes.semicolon\n        ) {\n          ++this.state.pos;\n          return String.fromCodePoint(codePoint);\n        }\n      } else {\n        let count = 0;\n        let semi = false;\n        while (\n          count++ < 10 &&\n          this.state.pos < this.length &&\n          !(semi = this.codePointAtPos(this.state.pos) == charCodes.semicolon)\n        ) {\n          ++this.state.pos;\n        }\n\n        if (semi) {\n          const desc = this.input.slice(startPos, this.state.pos);\n          const entity = XHTMLEntities[desc];\n          ++this.state.pos;\n\n          if (entity) {\n            return entity;\n          }\n        }\n      }\n\n      // Not a valid entity\n      this.state.pos = startPos;\n      return \"&\";\n    }\n\n    // Read a JSX identifier (valid tag or attribute name).\n    //\n    // Optimized version since JSX identifiers can\"t contain\n    // escape characters and so can be read as single slice.\n    // Also assumes that first character was already checked\n    // by isIdentifierStart in readToken.\n\n    jsxReadWord(): void {\n      let ch;\n      const start = this.state.pos;\n      do {\n        ch = this.input.charCodeAt(++this.state.pos);\n      } while (isIdentifierChar(ch) || ch === charCodes.dash);\n      this.finishToken(tt.jsxName, this.input.slice(start, this.state.pos));\n    }\n\n    // Parse next token as JSX identifier\n\n    jsxParseIdentifier(): N.JSXIdentifier {\n      const node = this.startNode();\n      if (this.match(tt.jsxName)) {\n        node.name = this.state.value;\n      } else if (tokenIsKeyword(this.state.type)) {\n        node.name = tokenLabelName(this.state.type);\n      } else {\n        this.unexpected();\n      }\n      this.next();\n      return this.finishNode(node, \"JSXIdentifier\");\n    }\n\n    // Parse namespaced identifier.\n\n    jsxParseNamespacedName(): N.JSXNamespacedName {\n      const startLoc = this.state.startLoc;\n      const name = this.jsxParseIdentifier();\n      if (!this.eat(tt.colon)) return name;\n\n      const node = this.startNodeAt(startLoc);\n      node.namespace = name;\n      node.name = this.jsxParseIdentifier();\n      return this.finishNode(node, \"JSXNamespacedName\");\n    }\n\n    // Parses element name in any form - namespaced, member\n    // or single identifier.\n\n    jsxParseElementName():\n      | N.JSXIdentifier\n      | N.JSXNamespacedName\n      | N.JSXMemberExpression {\n      const startLoc = this.state.startLoc;\n      let node = this.jsxParseNamespacedName();\n      if (node.type === \"JSXNamespacedName\") {\n        return node;\n      }\n      while (this.eat(tt.dot)) {\n        const newNode = this.startNodeAt(startLoc);\n        newNode.object = node;\n        newNode.property = this.jsxParseIdentifier();\n        node = this.finishNode(newNode, \"JSXMemberExpression\");\n      }\n      return node;\n    }\n\n    // Parses any type of JSX attribute value.\n\n    jsxParseAttributeValue(): N.Expression {\n      let node;\n      switch (this.state.type) {\n        case tt.braceL:\n          node = this.startNode();\n          this.setContext(tc.brace);\n          this.next();\n          node = this.jsxParseExpressionContainer(node, tc.j_oTag);\n          if (node.expression.type === \"JSXEmptyExpression\") {\n            this.raise(JsxErrors.AttributeIsEmpty, { at: node });\n          }\n          return node;\n\n        case tt.jsxTagStart:\n        case tt.string:\n          return this.parseExprAtom();\n\n        default:\n          throw this.raise(JsxErrors.UnsupportedJsxValue, {\n            at: this.state.startLoc,\n          });\n      }\n    }\n\n    // JSXEmptyExpression is unique type since it doesn't actually parse anything,\n    // and so it should start at the end of last read token (left brace) and finish\n    // at the beginning of the next one (right brace).\n\n    jsxParseEmptyExpression(): N.JSXEmptyExpression {\n      const node = this.startNodeAt(this.state.lastTokEndLoc);\n      return this.finishNodeAt(node, \"JSXEmptyExpression\", this.state.startLoc);\n    }\n\n    // Parse JSX spread child\n\n    jsxParseSpreadChild(node: Undone<N.JSXSpreadChild>): N.JSXSpreadChild {\n      this.next(); // ellipsis\n      node.expression = this.parseExpression();\n      this.setContext(tc.j_expr);\n      this.state.canStartJSXElement = true;\n      this.expect(tt.braceR);\n\n      return this.finishNode(node, \"JSXSpreadChild\");\n    }\n\n    // Parses JSX expression enclosed into curly brackets.\n\n    jsxParseExpressionContainer(\n      node: Undone<N.JSXExpressionContainer>,\n      previousContext: TokContext,\n    ): N.JSXExpressionContainer {\n      if (this.match(tt.braceR)) {\n        node.expression = this.jsxParseEmptyExpression();\n      } else {\n        const expression = this.parseExpression();\n\n        if (process.env.BABEL_8_BREAKING) {\n          if (\n            expression.type === \"SequenceExpression\" &&\n            !expression.extra?.parenthesized\n          ) {\n            this.raise(JsxErrors.UnexpectedSequenceExpression, {\n              at: expression.expressions[1],\n            });\n          }\n        }\n\n        node.expression = expression;\n      }\n      this.setContext(previousContext);\n      this.state.canStartJSXElement = true;\n      this.expect(tt.braceR);\n\n      return this.finishNode(node, \"JSXExpressionContainer\");\n    }\n\n    // Parses following JSX attribute name-value pair.\n\n    jsxParseAttribute(): N.JSXAttribute {\n      const node = this.startNode();\n      if (this.match(tt.braceL)) {\n        this.setContext(tc.brace);\n        this.next();\n        this.expect(tt.ellipsis);\n        node.argument = this.parseMaybeAssignAllowIn();\n        this.setContext(tc.j_oTag);\n        this.state.canStartJSXElement = true;\n        this.expect(tt.braceR);\n        return this.finishNode(node, \"JSXSpreadAttribute\");\n      }\n      node.name = this.jsxParseNamespacedName();\n      node.value = this.eat(tt.eq) ? this.jsxParseAttributeValue() : null;\n      return this.finishNode(node, \"JSXAttribute\");\n    }\n\n    // Parses JSX opening tag starting after \"<\".\n\n    jsxParseOpeningElementAt(startLoc: Position): N.JSXOpeningElement {\n      const node = this.startNodeAt<N.JSXOpeningElement | N.JSXOpeningFragment>(\n        startLoc,\n      );\n      if (this.eat(tt.jsxTagEnd)) {\n        // @ts-expect-error migrate to Babel types\n        return this.finishNode(node, \"JSXOpeningFragment\");\n      }\n      node.name = this.jsxParseElementName();\n      return this.jsxParseOpeningElementAfterName(\n        node as Undone<N.JSXOpeningElement>,\n      );\n    }\n\n    jsxParseOpeningElementAfterName(\n      node: Undone<N.JSXOpeningElement>,\n    ): N.JSXOpeningElement {\n      const attributes: N.JSXAttribute[] = [];\n      while (!this.match(tt.slash) && !this.match(tt.jsxTagEnd)) {\n        attributes.push(this.jsxParseAttribute());\n      }\n      node.attributes = attributes;\n      node.selfClosing = this.eat(tt.slash);\n      this.expect(tt.jsxTagEnd);\n      return this.finishNode(node, \"JSXOpeningElement\");\n    }\n\n    // Parses JSX closing tag starting after \"</\".\n\n    jsxParseClosingElementAt(startLoc: Position): N.JSXClosingElement {\n      const node = this.startNodeAt(startLoc);\n      if (this.eat(tt.jsxTagEnd)) {\n        return this.finishNode(node, \"JSXClosingFragment\");\n      }\n      node.name = this.jsxParseElementName();\n      this.expect(tt.jsxTagEnd);\n      return this.finishNode(node, \"JSXClosingElement\");\n    }\n\n    // Parses entire JSX element, including it\"s opening tag\n    // (starting after \"<\"), attributes, contents and closing tag.\n\n    jsxParseElementAt(startLoc: Position): N.JSXElement {\n      const node = this.startNodeAt(startLoc);\n      const children = [];\n      const openingElement = this.jsxParseOpeningElementAt(startLoc);\n      let closingElement = null;\n\n      if (!openingElement.selfClosing) {\n        contents: for (;;) {\n          switch (this.state.type) {\n            case tt.jsxTagStart:\n              startLoc = this.state.startLoc;\n              this.next();\n              if (this.eat(tt.slash)) {\n                closingElement = this.jsxParseClosingElementAt(startLoc);\n                break contents;\n              }\n              children.push(this.jsxParseElementAt(startLoc));\n              break;\n\n            case tt.jsxText:\n              children.push(this.parseExprAtom());\n              break;\n\n            case tt.braceL: {\n              const node = this.startNode<\n                N.JSXSpreadChild | N.JSXExpressionContainer\n              >();\n              this.setContext(tc.brace);\n              this.next();\n              if (this.match(tt.ellipsis)) {\n                children.push(this.jsxParseSpreadChild(node));\n              } else {\n                children.push(\n                  this.jsxParseExpressionContainer(node, tc.j_expr),\n                );\n              }\n\n              break;\n            }\n            // istanbul ignore next - should never happen\n            default:\n              this.unexpected();\n          }\n        }\n\n        if (\n          isFragment(openingElement) &&\n          !isFragment(closingElement) &&\n          closingElement !== null\n        ) {\n          this.raise(JsxErrors.MissingClosingTagFragment, {\n            at: closingElement,\n          });\n        } else if (!isFragment(openingElement) && isFragment(closingElement)) {\n          this.raise(JsxErrors.MissingClosingTagElement, {\n            at: closingElement,\n            openingTagName: getQualifiedJSXName(openingElement.name),\n          });\n        } else if (!isFragment(openingElement) && !isFragment(closingElement)) {\n          if (\n            getQualifiedJSXName(closingElement.name) !==\n            getQualifiedJSXName(openingElement.name)\n          ) {\n            this.raise(JsxErrors.MissingClosingTagElement, {\n              at: closingElement,\n              openingTagName: getQualifiedJSXName(openingElement.name),\n            });\n          }\n        }\n      }\n\n      if (isFragment(openingElement)) {\n        node.openingFragment = openingElement;\n        node.closingFragment = closingElement;\n      } else {\n        node.openingElement = openingElement;\n        node.closingElement = closingElement;\n      }\n      node.children = children;\n      if (this.match(tt.lt)) {\n        throw this.raise(JsxErrors.UnwrappedAdjacentJSXElements, {\n          at: this.state.startLoc,\n        });\n      }\n\n      return isFragment(openingElement)\n        ? this.finishNode(node, \"JSXFragment\")\n        : this.finishNode(node, \"JSXElement\");\n    }\n\n    // Parses entire JSX element from current position.\n\n    jsxParseElement(): N.JSXElement {\n      const startLoc = this.state.startLoc;\n      this.next();\n      return this.jsxParseElementAt(startLoc);\n    }\n\n    setContext(newContext: TokContext) {\n      const { context } = this.state;\n      context[context.length - 1] = newContext;\n    }\n\n    // ==================================\n    // Overrides\n    // ==================================\n\n    parseExprAtom(refExpressionErrors?: ExpressionErrors | null): N.Expression {\n      if (this.match(tt.jsxText)) {\n        return this.parseLiteral(this.state.value, \"JSXText\");\n      } else if (this.match(tt.jsxTagStart)) {\n        return this.jsxParseElement();\n      } else if (\n        this.match(tt.lt) &&\n        this.input.charCodeAt(this.state.pos) !== charCodes.exclamationMark\n      ) {\n        // In case we encounter an lt token here it will always be the start of\n        // jsx as the lt sign is not allowed in places that expect an expression\n        this.replaceToken(tt.jsxTagStart);\n        return this.jsxParseElement();\n      } else {\n        return super.parseExprAtom(refExpressionErrors);\n      }\n    }\n\n    skipSpace() {\n      const curContext = this.curContext();\n      if (!curContext.preserveSpace) super.skipSpace();\n    }\n\n    getTokenFromCode(code: number): void {\n      const context = this.curContext();\n\n      if (context === tc.j_expr) {\n        this.jsxReadToken();\n        return;\n      }\n\n      if (context === tc.j_oTag || context === tc.j_cTag) {\n        if (isIdentifierStart(code)) {\n          this.jsxReadWord();\n          return;\n        }\n\n        if (code === charCodes.greaterThan) {\n          ++this.state.pos;\n          this.finishToken(tt.jsxTagEnd);\n          return;\n        }\n\n        if (\n          (code === charCodes.quotationMark || code === charCodes.apostrophe) &&\n          context === tc.j_oTag\n        ) {\n          this.jsxReadString(code);\n          return;\n        }\n      }\n\n      if (\n        code === charCodes.lessThan &&\n        this.state.canStartJSXElement &&\n        this.input.charCodeAt(this.state.pos + 1) !== charCodes.exclamationMark\n      ) {\n        ++this.state.pos;\n        this.finishToken(tt.jsxTagStart);\n        return;\n      }\n\n      super.getTokenFromCode(code);\n    }\n\n    updateContext(prevType: TokenType): void {\n      const { context, type } = this.state;\n      if (type === tt.slash && prevType === tt.jsxTagStart) {\n        // do not consider JSX expr -> JSX open tag -> ... anymore\n        // reconsider as closing tag context\n        context.splice(-2, 2, tc.j_cTag);\n        this.state.canStartJSXElement = false;\n      } else if (type === tt.jsxTagStart) {\n        // start opening tag context\n        context.push(tc.j_oTag);\n      } else if (type === tt.jsxTagEnd) {\n        const out = context[context.length - 1];\n        if ((out === tc.j_oTag && prevType === tt.slash) || out === tc.j_cTag) {\n          context.pop();\n          this.state.canStartJSXElement =\n            context[context.length - 1] === tc.j_expr;\n        } else {\n          this.setContext(tc.j_expr);\n          this.state.canStartJSXElement = true;\n        }\n      } else {\n        this.state.canStartJSXElement = tokenComesBeforeExpression(type);\n      }\n    }\n  };\n"], "mappings": ";;;;;;AAEA,IAAAA,MAAA,GAAAC,OAAA;AAGA,IAAAC,MAAA,GAAAD,OAAA;AAQA,IAAAE,QAAA,GAAAF,OAAA;AAEA,IAAAG,WAAA,GAAAH,OAAA;AAEA,IAAAI,WAAA,GAAAJ,OAAA;AACA,IAAAK,WAAA,GAAAL,OAAA;AAIA,MAAMM,SAAS,GAAG,IAAAC,0BAAc,CAAC,KAAI,CAAC;EACpCC,gBAAgB,EACd,8DAA8D;EAChEC,wBAAwB,EAAEA,CAAC;IAAEC;EAA2C,CAAC,KACtE,+CAA8CA,cAAe,IAAG;EACnEC,yBAAyB,EAAE,gDAAgD;EAC3EC,4BAA4B,EAC1B,0GAA0G;EAE5GC,eAAe,EAAEA,CAAC;IAChBC,UAAU;IACVC;EAIF,CAAC,KACE,sBAAqBD,UAAW,sBAAqBC,UAAW,aAAYD,UAAW,OAAM;EAChGE,mBAAmB,EACjB,gEAAgE;EAClEC,sBAAsB,EAAE,4BAA4B;EACpDC,4BAA4B,EAC1B;AACJ,CAAC,CAAC;AAIF,SAASC,UAAUA,CAACC,MAA4B,EAAW;EACzD,OAAOA,MAAM,GACTA,MAAM,CAACC,IAAI,KAAK,oBAAoB,IAClCD,MAAM,CAACC,IAAI,KAAK,oBAAoB,GACtC,KAAK;AACX;AAIA,SAASC,mBAAmBA,CAC1BF,MAAqE,EAC7D;EACR,IAAIA,MAAM,CAACC,IAAI,KAAK,eAAe,EAAE;IACnC,OAAOD,MAAM,CAACG,IAAI;EACpB;EAEA,IAAIH,MAAM,CAACC,IAAI,KAAK,mBAAmB,EAAE;IACvC,OAAOD,MAAM,CAACI,SAAS,CAACD,IAAI,GAAG,GAAG,GAAGH,MAAM,CAACG,IAAI,CAACA,IAAI;EACvD;EAEA,IAAIH,MAAM,CAACC,IAAI,KAAK,qBAAqB,EAAE;IACzC,OACEC,mBAAmB,CAACF,MAAM,CAACA,MAAM,CAAC,GAClC,GAAG,GACHE,mBAAmB,CAACF,MAAM,CAACK,QAAQ,CAAC;EAExC;EAGA,MAAM,IAAIC,KAAK,CAAC,4BAA4B,GAAGN,MAAM,CAACC,IAAI,CAAC;AAC7D;AAAC,IAAAM,QAAA,GAQeC,UAAyB,IACvC,MAAMC,cAAc,SAASD,UAAU,CAAoC;EAGzEE,YAAYA,CAAA,EAAS;IACnB,IAAIC,GAAG,GAAG,EAAE;IACZ,IAAIC,UAAU,GAAG,IAAI,CAACC,KAAK,CAACC,GAAG;IAC/B,SAAS;MACP,IAAI,IAAI,CAACD,KAAK,CAACC,GAAG,IAAI,IAAI,CAACC,MAAM,EAAE;QACjC,MAAM,IAAI,CAACC,KAAK,CAAC9B,SAAS,CAACW,sBAAsB,EAAE;UACjDoB,EAAE,EAAE,IAAI,CAACJ,KAAK,CAACK;QACjB,CAAC,CAAC;MACJ;MAEA,MAAMC,EAAE,GAAG,IAAI,CAACC,KAAK,CAACC,UAAU,CAAC,IAAI,CAACR,KAAK,CAACC,GAAG,CAAC;MAEhD,QAAQK,EAAE;QACR;QACA;UACE,IAAI,IAAI,CAACN,KAAK,CAACC,GAAG,KAAK,IAAI,CAACD,KAAK,CAACS,KAAK,EAAE;YACvC,IAAIH,EAAE,OAAuB,IAAI,IAAI,CAACN,KAAK,CAACU,kBAAkB,EAAE;cAC9D,EAAE,IAAI,CAACV,KAAK,CAACC,GAAG;cAChB,IAAI,CAACU,WAAW,IAAe,CAAC;YAClC,CAAC,MAAM;cACL,KAAK,CAACC,gBAAgB,CAACN,EAAE,CAAC;YAC5B;YACA;UACF;UACAR,GAAG,IAAI,IAAI,CAACS,KAAK,CAACM,KAAK,CAACd,UAAU,EAAE,IAAI,CAACC,KAAK,CAACC,GAAG,CAAC;UACnD,IAAI,CAACU,WAAW,MAAab,GAAG,CAAC;UACjC;QAEF;UACEA,GAAG,IAAI,IAAI,CAACS,KAAK,CAACM,KAAK,CAACd,UAAU,EAAE,IAAI,CAACC,KAAK,CAACC,GAAG,CAAC;UACnDH,GAAG,IAAI,IAAI,CAACgB,aAAa,CAAC,CAAC;UAC3Bf,UAAU,GAAG,IAAI,CAACC,KAAK,CAACC,GAAG;UAC3B;QAEF;QACA;UAAA;QAWA;UACE,IAAI,IAAAc,qBAAS,EAACT,EAAE,CAAC,EAAE;YACjBR,GAAG,IAAI,IAAI,CAACS,KAAK,CAACM,KAAK,CAACd,UAAU,EAAE,IAAI,CAACC,KAAK,CAACC,GAAG,CAAC;YACnDH,GAAG,IAAI,IAAI,CAACkB,cAAc,CAAC,IAAI,CAAC;YAChCjB,UAAU,GAAG,IAAI,CAACC,KAAK,CAACC,GAAG;UAC7B,CAAC,MAAM;YACL,EAAE,IAAI,CAACD,KAAK,CAACC,GAAG;UAClB;MACJ;IACF;EACF;EAEAe,cAAcA,CAACC,aAAsB,EAAU;IAC7C,MAAMX,EAAE,GAAG,IAAI,CAACC,KAAK,CAACC,UAAU,CAAC,IAAI,CAACR,KAAK,CAACC,GAAG,CAAC;IAChD,IAAIH,GAAG;IACP,EAAE,IAAI,CAACE,KAAK,CAACC,GAAG;IAChB,IACEK,EAAE,OAA6B,IAC/B,IAAI,CAACC,KAAK,CAACC,UAAU,CAAC,IAAI,CAACR,KAAK,CAACC,GAAG,CAAC,OAAuB,EAC5D;MACA,EAAE,IAAI,CAACD,KAAK,CAACC,GAAG;MAChBH,GAAG,GAAGmB,aAAa,GAAG,IAAI,GAAG,MAAM;IACrC,CAAC,MAAM;MACLnB,GAAG,GAAGoB,MAAM,CAACC,YAAY,CAACb,EAAE,CAAC;IAC/B;IACA,EAAE,IAAI,CAACN,KAAK,CAACoB,OAAO;IACpB,IAAI,CAACpB,KAAK,CAACqB,SAAS,GAAG,IAAI,CAACrB,KAAK,CAACC,GAAG;IAErC,OAAOH,GAAG;EACZ;EAEAwB,aAAaA,CAACC,KAAa,EAAQ;IACjC,IAAIzB,GAAG,GAAG,EAAE;IACZ,IAAIC,UAAU,GAAG,EAAE,IAAI,CAACC,KAAK,CAACC,GAAG;IACjC,SAAS;MACP,IAAI,IAAI,CAACD,KAAK,CAACC,GAAG,IAAI,IAAI,CAACC,MAAM,EAAE;QACjC,MAAM,IAAI,CAACC,KAAK,CAACqB,kBAAM,CAACC,kBAAkB,EAAE;UAC1CrB,EAAE,EAAE,IAAI,CAACJ,KAAK,CAACK;QACjB,CAAC,CAAC;MACJ;MAEA,MAAMC,EAAE,GAAG,IAAI,CAACC,KAAK,CAACC,UAAU,CAAC,IAAI,CAACR,KAAK,CAACC,GAAG,CAAC;MAChD,IAAIK,EAAE,KAAKiB,KAAK,EAAE;MAClB,IAAIjB,EAAE,OAAwB,EAAE;QAC9BR,GAAG,IAAI,IAAI,CAACS,KAAK,CAACM,KAAK,CAACd,UAAU,EAAE,IAAI,CAACC,KAAK,CAACC,GAAG,CAAC;QACnDH,GAAG,IAAI,IAAI,CAACgB,aAAa,CAAC,CAAC;QAC3Bf,UAAU,GAAG,IAAI,CAACC,KAAK,CAACC,GAAG;MAC7B,CAAC,MAAM,IAAI,IAAAc,qBAAS,EAACT,EAAE,CAAC,EAAE;QACxBR,GAAG,IAAI,IAAI,CAACS,KAAK,CAACM,KAAK,CAACd,UAAU,EAAE,IAAI,CAACC,KAAK,CAACC,GAAG,CAAC;QACnDH,GAAG,IAAI,IAAI,CAACkB,cAAc,CAAC,KAAK,CAAC;QACjCjB,UAAU,GAAG,IAAI,CAACC,KAAK,CAACC,GAAG;MAC7B,CAAC,MAAM;QACL,EAAE,IAAI,CAACD,KAAK,CAACC,GAAG;MAClB;IACF;IACAH,GAAG,IAAI,IAAI,CAACS,KAAK,CAACM,KAAK,CAACd,UAAU,EAAE,IAAI,CAACC,KAAK,CAACC,GAAG,EAAE,CAAC;IACrD,IAAI,CAACU,WAAW,MAAYb,GAAG,CAAC;EAClC;EAEAgB,aAAaA,CAAA,EAAW;IACtB,MAAMY,QAAQ,GAAG,EAAE,IAAI,CAAC1B,KAAK,CAACC,GAAG;IACjC,IAAI,IAAI,CAAC0B,cAAc,CAAC,IAAI,CAAC3B,KAAK,CAACC,GAAG,CAAC,OAAyB,EAAE;MAChE,EAAE,IAAI,CAACD,KAAK,CAACC,GAAG;MAEhB,IAAI2B,KAAK,GAAG,EAAE;MACd,IAAI,IAAI,CAACD,cAAc,CAAC,IAAI,CAAC3B,KAAK,CAACC,GAAG,CAAC,QAAyB,EAAE;QAChE2B,KAAK,GAAG,EAAE;QACV,EAAE,IAAI,CAAC5B,KAAK,CAACC,GAAG;MAClB;MAEA,MAAM4B,SAAS,GAAG,IAAI,CAACC,OAAO,CAC5BF,KAAK,EACKG,SAAS,EACJ,KAAK,EACI,MAC1B,CAAC;MACD,IACEF,SAAS,KAAK,IAAI,IAClB,IAAI,CAACF,cAAc,CAAC,IAAI,CAAC3B,KAAK,CAACC,GAAG,CAAC,OAAwB,EAC3D;QACA,EAAE,IAAI,CAACD,KAAK,CAACC,GAAG;QAChB,OAAOiB,MAAM,CAACc,aAAa,CAACH,SAAS,CAAC;MACxC;IACF,CAAC,MAAM;MACL,IAAII,KAAK,GAAG,CAAC;MACb,IAAIC,IAAI,GAAG,KAAK;MAChB,OACED,KAAK,EAAE,GAAG,EAAE,IACZ,IAAI,CAACjC,KAAK,CAACC,GAAG,GAAG,IAAI,CAACC,MAAM,IAC5B,EAAEgC,IAAI,GAAG,IAAI,CAACP,cAAc,CAAC,IAAI,CAAC3B,KAAK,CAACC,GAAG,CAAC,MAAuB,CAAC,EACpE;QACA,EAAE,IAAI,CAACD,KAAK,CAACC,GAAG;MAClB;MAEA,IAAIiC,IAAI,EAAE;QACR,MAAMC,IAAI,GAAG,IAAI,CAAC5B,KAAK,CAACM,KAAK,CAACa,QAAQ,EAAE,IAAI,CAAC1B,KAAK,CAACC,GAAG,CAAC;QACvD,MAAMmC,MAAM,GAAGC,cAAa,CAACF,IAAI,CAAC;QAClC,EAAE,IAAI,CAACnC,KAAK,CAACC,GAAG;QAEhB,IAAImC,MAAM,EAAE;UACV,OAAOA,MAAM;QACf;MACF;IACF;IAGA,IAAI,CAACpC,KAAK,CAACC,GAAG,GAAGyB,QAAQ;IACzB,OAAO,GAAG;EACZ;EASAY,WAAWA,CAAA,EAAS;IAClB,IAAIhC,EAAE;IACN,MAAMG,KAAK,GAAG,IAAI,CAACT,KAAK,CAACC,GAAG;IAC5B,GAAG;MACDK,EAAE,GAAG,IAAI,CAACC,KAAK,CAACC,UAAU,CAAC,EAAE,IAAI,CAACR,KAAK,CAACC,GAAG,CAAC;IAC9C,CAAC,QAAQ,IAAAsC,4BAAgB,EAACjC,EAAE,CAAC,IAAIA,EAAE,OAAmB;IACtD,IAAI,CAACK,WAAW,MAAa,IAAI,CAACJ,KAAK,CAACM,KAAK,CAACJ,KAAK,EAAE,IAAI,CAACT,KAAK,CAACC,GAAG,CAAC,CAAC;EACvE;EAIAuC,kBAAkBA,CAAA,EAAoB;IACpC,MAAMC,IAAI,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IAC7B,IAAI,IAAI,CAACC,KAAK,IAAW,CAAC,EAAE;MAC1BF,IAAI,CAACnD,IAAI,GAAG,IAAI,CAACU,KAAK,CAAC4C,KAAK;IAC9B,CAAC,MAAM,IAAI,IAAAC,qBAAc,EAAC,IAAI,CAAC7C,KAAK,CAACZ,IAAI,CAAC,EAAE;MAC1CqD,IAAI,CAACnD,IAAI,GAAG,IAAAwD,qBAAc,EAAC,IAAI,CAAC9C,KAAK,CAACZ,IAAI,CAAC;IAC7C,CAAC,MAAM;MACL,IAAI,CAACP,UAAU,CAAC,CAAC;IACnB;IACA,IAAI,CAACkE,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAACC,UAAU,CAACP,IAAI,EAAE,eAAe,CAAC;EAC/C;EAIAQ,sBAAsBA,CAAA,EAAwB;IAC5C,MAAM5C,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACK,QAAQ;IACpC,MAAMf,IAAI,GAAG,IAAI,CAACkD,kBAAkB,CAAC,CAAC;IACtC,IAAI,CAAC,IAAI,CAACU,GAAG,GAAS,CAAC,EAAE,OAAO5D,IAAI;IAEpC,MAAMmD,IAAI,GAAG,IAAI,CAACU,WAAW,CAAC9C,QAAQ,CAAC;IACvCoC,IAAI,CAAClD,SAAS,GAAGD,IAAI;IACrBmD,IAAI,CAACnD,IAAI,GAAG,IAAI,CAACkD,kBAAkB,CAAC,CAAC;IACrC,OAAO,IAAI,CAACQ,UAAU,CAACP,IAAI,EAAE,mBAAmB,CAAC;EACnD;EAKAW,mBAAmBA,CAAA,EAGO;IACxB,MAAM/C,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACK,QAAQ;IACpC,IAAIoC,IAAI,GAAG,IAAI,CAACQ,sBAAsB,CAAC,CAAC;IACxC,IAAIR,IAAI,CAACrD,IAAI,KAAK,mBAAmB,EAAE;MACrC,OAAOqD,IAAI;IACb;IACA,OAAO,IAAI,CAACS,GAAG,GAAO,CAAC,EAAE;MACvB,MAAMG,OAAO,GAAG,IAAI,CAACF,WAAW,CAAC9C,QAAQ,CAAC;MAC1CgD,OAAO,CAAClE,MAAM,GAAGsD,IAAI;MACrBY,OAAO,CAAC7D,QAAQ,GAAG,IAAI,CAACgD,kBAAkB,CAAC,CAAC;MAC5CC,IAAI,GAAG,IAAI,CAACO,UAAU,CAACK,OAAO,EAAE,qBAAqB,CAAC;IACxD;IACA,OAAOZ,IAAI;EACb;EAIAa,sBAAsBA,CAAA,EAAiB;IACrC,IAAIb,IAAI;IACR,QAAQ,IAAI,CAACzC,KAAK,CAACZ,IAAI;MACrB;QACEqD,IAAI,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;QACvB,IAAI,CAACa,UAAU,CAACC,cAAE,CAACC,KAAK,CAAC;QACzB,IAAI,CAACV,IAAI,CAAC,CAAC;QACXN,IAAI,GAAG,IAAI,CAACiB,2BAA2B,CAACjB,IAAI,EAAEe,cAAE,CAACG,MAAM,CAAC;QACxD,IAAIlB,IAAI,CAACmB,UAAU,CAACxE,IAAI,KAAK,oBAAoB,EAAE;UACjD,IAAI,CAACe,KAAK,CAAC9B,SAAS,CAACE,gBAAgB,EAAE;YAAE6B,EAAE,EAAEqC;UAAK,CAAC,CAAC;QACtD;QACA,OAAOA,IAAI;MAEb;MACA;QACE,OAAO,IAAI,CAACoB,aAAa,CAAC,CAAC;MAE7B;QACE,MAAM,IAAI,CAAC1D,KAAK,CAAC9B,SAAS,CAACU,mBAAmB,EAAE;UAC9CqB,EAAE,EAAE,IAAI,CAACJ,KAAK,CAACK;QACjB,CAAC,CAAC;IACN;EACF;EAMAyD,uBAAuBA,CAAA,EAAyB;IAC9C,MAAMrB,IAAI,GAAG,IAAI,CAACU,WAAW,CAAC,IAAI,CAACnD,KAAK,CAAC+D,aAAa,CAAC;IACvD,OAAO,IAAI,CAACC,YAAY,CAACvB,IAAI,EAAE,oBAAoB,EAAE,IAAI,CAACzC,KAAK,CAACK,QAAQ,CAAC;EAC3E;EAIA4D,mBAAmBA,CAACxB,IAA8B,EAAoB;IACpE,IAAI,CAACM,IAAI,CAAC,CAAC;IACXN,IAAI,CAACmB,UAAU,GAAG,IAAI,CAACM,eAAe,CAAC,CAAC;IACxC,IAAI,CAACX,UAAU,CAACC,cAAE,CAACW,MAAM,CAAC;IAC1B,IAAI,CAACnE,KAAK,CAACU,kBAAkB,GAAG,IAAI;IACpC,IAAI,CAAC0D,MAAM,EAAU,CAAC;IAEtB,OAAO,IAAI,CAACpB,UAAU,CAACP,IAAI,EAAE,gBAAgB,CAAC;EAChD;EAIAiB,2BAA2BA,CACzBjB,IAAsC,EACtC4B,eAA2B,EACD;IAC1B,IAAI,IAAI,CAAC1B,KAAK,EAAU,CAAC,EAAE;MACzBF,IAAI,CAACmB,UAAU,GAAG,IAAI,CAACE,uBAAuB,CAAC,CAAC;IAClD,CAAC,MAAM;MACL,MAAMF,UAAU,GAAG,IAAI,CAACM,eAAe,CAAC,CAAC;MAAC;MAa1CzB,IAAI,CAACmB,UAAU,GAAGA,UAAU;IAC9B;IACA,IAAI,CAACL,UAAU,CAACc,eAAe,CAAC;IAChC,IAAI,CAACrE,KAAK,CAACU,kBAAkB,GAAG,IAAI;IACpC,IAAI,CAAC0D,MAAM,EAAU,CAAC;IAEtB,OAAO,IAAI,CAACpB,UAAU,CAACP,IAAI,EAAE,wBAAwB,CAAC;EACxD;EAIA6B,iBAAiBA,CAAA,EAAmB;IAClC,MAAM7B,IAAI,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IAC7B,IAAI,IAAI,CAACC,KAAK,EAAU,CAAC,EAAE;MACzB,IAAI,CAACY,UAAU,CAACC,cAAE,CAACC,KAAK,CAAC;MACzB,IAAI,CAACV,IAAI,CAAC,CAAC;MACX,IAAI,CAACqB,MAAM,GAAY,CAAC;MACxB3B,IAAI,CAAC8B,QAAQ,GAAG,IAAI,CAACC,uBAAuB,CAAC,CAAC;MAC9C,IAAI,CAACjB,UAAU,CAACC,cAAE,CAACG,MAAM,CAAC;MAC1B,IAAI,CAAC3D,KAAK,CAACU,kBAAkB,GAAG,IAAI;MACpC,IAAI,CAAC0D,MAAM,EAAU,CAAC;MACtB,OAAO,IAAI,CAACpB,UAAU,CAACP,IAAI,EAAE,oBAAoB,CAAC;IACpD;IACAA,IAAI,CAACnD,IAAI,GAAG,IAAI,CAAC2D,sBAAsB,CAAC,CAAC;IACzCR,IAAI,CAACG,KAAK,GAAG,IAAI,CAACM,GAAG,GAAM,CAAC,GAAG,IAAI,CAACI,sBAAsB,CAAC,CAAC,GAAG,IAAI;IACnE,OAAO,IAAI,CAACN,UAAU,CAACP,IAAI,EAAE,cAAc,CAAC;EAC9C;EAIAgC,wBAAwBA,CAACpE,QAAkB,EAAuB;IAChE,MAAMoC,IAAI,GAAG,IAAI,CAACU,WAAW,CAC3B9C,QACF,CAAC;IACD,IAAI,IAAI,CAAC6C,GAAG,IAAa,CAAC,EAAE;MAE1B,OAAO,IAAI,CAACF,UAAU,CAACP,IAAI,EAAE,oBAAoB,CAAC;IACpD;IACAA,IAAI,CAACnD,IAAI,GAAG,IAAI,CAAC8D,mBAAmB,CAAC,CAAC;IACtC,OAAO,IAAI,CAACsB,+BAA+B,CACzCjC,IACF,CAAC;EACH;EAEAiC,+BAA+BA,CAC7BjC,IAAiC,EACZ;IACrB,MAAMkC,UAA4B,GAAG,EAAE;IACvC,OAAO,CAAC,IAAI,CAAChC,KAAK,GAAS,CAAC,IAAI,CAAC,IAAI,CAACA,KAAK,IAAa,CAAC,EAAE;MACzDgC,UAAU,CAACC,IAAI,CAAC,IAAI,CAACN,iBAAiB,CAAC,CAAC,CAAC;IAC3C;IACA7B,IAAI,CAACkC,UAAU,GAAGA,UAAU;IAC5BlC,IAAI,CAACoC,WAAW,GAAG,IAAI,CAAC3B,GAAG,GAAS,CAAC;IACrC,IAAI,CAACkB,MAAM,IAAa,CAAC;IACzB,OAAO,IAAI,CAACpB,UAAU,CAACP,IAAI,EAAE,mBAAmB,CAAC;EACnD;EAIAqC,wBAAwBA,CAACzE,QAAkB,EAAuB;IAChE,MAAMoC,IAAI,GAAG,IAAI,CAACU,WAAW,CAAC9C,QAAQ,CAAC;IACvC,IAAI,IAAI,CAAC6C,GAAG,IAAa,CAAC,EAAE;MAC1B,OAAO,IAAI,CAACF,UAAU,CAACP,IAAI,EAAE,oBAAoB,CAAC;IACpD;IACAA,IAAI,CAACnD,IAAI,GAAG,IAAI,CAAC8D,mBAAmB,CAAC,CAAC;IACtC,IAAI,CAACgB,MAAM,IAAa,CAAC;IACzB,OAAO,IAAI,CAACpB,UAAU,CAACP,IAAI,EAAE,mBAAmB,CAAC;EACnD;EAKAsC,iBAAiBA,CAAC1E,QAAkB,EAAgB;IAClD,MAAMoC,IAAI,GAAG,IAAI,CAACU,WAAW,CAAC9C,QAAQ,CAAC;IACvC,MAAM2E,QAAQ,GAAG,EAAE;IACnB,MAAMC,cAAc,GAAG,IAAI,CAACR,wBAAwB,CAACpE,QAAQ,CAAC;IAC9D,IAAI6E,cAAc,GAAG,IAAI;IAEzB,IAAI,CAACD,cAAc,CAACJ,WAAW,EAAE;MAC/BM,QAAQ,EAAE,SAAS;QACjB,QAAQ,IAAI,CAACnF,KAAK,CAACZ,IAAI;UACrB;YACEiB,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACK,QAAQ;YAC9B,IAAI,CAAC0C,IAAI,CAAC,CAAC;YACX,IAAI,IAAI,CAACG,GAAG,GAAS,CAAC,EAAE;cACtBgC,cAAc,GAAG,IAAI,CAACJ,wBAAwB,CAACzE,QAAQ,CAAC;cACxD,MAAM8E,QAAQ;YAChB;YACAH,QAAQ,CAACJ,IAAI,CAAC,IAAI,CAACG,iBAAiB,CAAC1E,QAAQ,CAAC,CAAC;YAC/C;UAEF;YACE2E,QAAQ,CAACJ,IAAI,CAAC,IAAI,CAACf,aAAa,CAAC,CAAC,CAAC;YACnC;UAEF;YAAgB;cACd,MAAMpB,IAAI,GAAG,IAAI,CAACC,SAAS,CAEzB,CAAC;cACH,IAAI,CAACa,UAAU,CAACC,cAAE,CAACC,KAAK,CAAC;cACzB,IAAI,CAACV,IAAI,CAAC,CAAC;cACX,IAAI,IAAI,CAACJ,KAAK,GAAY,CAAC,EAAE;gBAC3BqC,QAAQ,CAACJ,IAAI,CAAC,IAAI,CAACX,mBAAmB,CAACxB,IAAI,CAAC,CAAC;cAC/C,CAAC,MAAM;gBACLuC,QAAQ,CAACJ,IAAI,CACX,IAAI,CAAClB,2BAA2B,CAACjB,IAAI,EAAEe,cAAE,CAACW,MAAM,CAClD,CAAC;cACH;cAEA;YACF;UAEA;YACE,IAAI,CAACtF,UAAU,CAAC,CAAC;QACrB;MACF;MAEA,IACEK,UAAU,CAAC+F,cAAc,CAAC,IAC1B,CAAC/F,UAAU,CAACgG,cAAc,CAAC,IAC3BA,cAAc,KAAK,IAAI,EACvB;QACA,IAAI,CAAC/E,KAAK,CAAC9B,SAAS,CAACK,yBAAyB,EAAE;UAC9C0B,EAAE,EAAE8E;QACN,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,CAAChG,UAAU,CAAC+F,cAAc,CAAC,IAAI/F,UAAU,CAACgG,cAAc,CAAC,EAAE;QACpE,IAAI,CAAC/E,KAAK,CAAC9B,SAAS,CAACG,wBAAwB,EAAE;UAC7C4B,EAAE,EAAE8E,cAAc;UAClBzG,cAAc,EAAEY,mBAAmB,CAAC4F,cAAc,CAAC3F,IAAI;QACzD,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,CAACJ,UAAU,CAAC+F,cAAc,CAAC,IAAI,CAAC/F,UAAU,CAACgG,cAAc,CAAC,EAAE;QACrE,IACE7F,mBAAmB,CAAC6F,cAAc,CAAC5F,IAAI,CAAC,KACxCD,mBAAmB,CAAC4F,cAAc,CAAC3F,IAAI,CAAC,EACxC;UACA,IAAI,CAACa,KAAK,CAAC9B,SAAS,CAACG,wBAAwB,EAAE;YAC7C4B,EAAE,EAAE8E,cAAc;YAClBzG,cAAc,EAAEY,mBAAmB,CAAC4F,cAAc,CAAC3F,IAAI;UACzD,CAAC,CAAC;QACJ;MACF;IACF;IAEA,IAAIJ,UAAU,CAAC+F,cAAc,CAAC,EAAE;MAC9BxC,IAAI,CAAC2C,eAAe,GAAGH,cAAc;MACrCxC,IAAI,CAAC4C,eAAe,GAAGH,cAAc;IACvC,CAAC,MAAM;MACLzC,IAAI,CAACwC,cAAc,GAAGA,cAAc;MACpCxC,IAAI,CAACyC,cAAc,GAAGA,cAAc;IACtC;IACAzC,IAAI,CAACuC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,IAAI,CAACrC,KAAK,GAAM,CAAC,EAAE;MACrB,MAAM,IAAI,CAACxC,KAAK,CAAC9B,SAAS,CAACY,4BAA4B,EAAE;QACvDmB,EAAE,EAAE,IAAI,CAACJ,KAAK,CAACK;MACjB,CAAC,CAAC;IACJ;IAEA,OAAOnB,UAAU,CAAC+F,cAAc,CAAC,GAC7B,IAAI,CAACjC,UAAU,CAACP,IAAI,EAAE,aAAa,CAAC,GACpC,IAAI,CAACO,UAAU,CAACP,IAAI,EAAE,YAAY,CAAC;EACzC;EAIA6C,eAAeA,CAAA,EAAiB;IAC9B,MAAMjF,QAAQ,GAAG,IAAI,CAACL,KAAK,CAACK,QAAQ;IACpC,IAAI,CAAC0C,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAACgC,iBAAiB,CAAC1E,QAAQ,CAAC;EACzC;EAEAkD,UAAUA,CAACgC,UAAsB,EAAE;IACjC,MAAM;MAAEC;IAAQ,CAAC,GAAG,IAAI,CAACxF,KAAK;IAC9BwF,OAAO,CAACA,OAAO,CAACtF,MAAM,GAAG,CAAC,CAAC,GAAGqF,UAAU;EAC1C;EAMA1B,aAAaA,CAAC4B,mBAA6C,EAAgB;IACzE,IAAI,IAAI,CAAC9C,KAAK,IAAW,CAAC,EAAE;MAC1B,OAAO,IAAI,CAAC+C,YAAY,CAAC,IAAI,CAAC1F,KAAK,CAAC4C,KAAK,EAAE,SAAS,CAAC;IACvD,CAAC,MAAM,IAAI,IAAI,CAACD,KAAK,IAAe,CAAC,EAAE;MACrC,OAAO,IAAI,CAAC2C,eAAe,CAAC,CAAC;IAC/B,CAAC,MAAM,IACL,IAAI,CAAC3C,KAAK,GAAM,CAAC,IACjB,IAAI,CAACpC,KAAK,CAACC,UAAU,CAAC,IAAI,CAACR,KAAK,CAACC,GAAG,CAAC,OAA8B,EACnE;MAGA,IAAI,CAAC0F,YAAY,IAAe,CAAC;MACjC,OAAO,IAAI,CAACL,eAAe,CAAC,CAAC;IAC/B,CAAC,MAAM;MACL,OAAO,KAAK,CAACzB,aAAa,CAAC4B,mBAAmB,CAAC;IACjD;EACF;EAEAG,SAASA,CAAA,EAAG;IACV,MAAMC,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC,CAAC;IACpC,IAAI,CAACA,UAAU,CAACC,aAAa,EAAE,KAAK,CAACF,SAAS,CAAC,CAAC;EAClD;EAEAhF,gBAAgBA,CAACmF,IAAY,EAAQ;IACnC,MAAMP,OAAO,GAAG,IAAI,CAACK,UAAU,CAAC,CAAC;IAEjC,IAAIL,OAAO,KAAKhC,cAAE,CAACW,MAAM,EAAE;MACzB,IAAI,CAACtE,YAAY,CAAC,CAAC;MACnB;IACF;IAEA,IAAI2F,OAAO,KAAKhC,cAAE,CAACG,MAAM,IAAI6B,OAAO,KAAKhC,cAAE,CAACwC,MAAM,EAAE;MAClD,IAAI,IAAAC,6BAAiB,EAACF,IAAI,CAAC,EAAE;QAC3B,IAAI,CAACzD,WAAW,CAAC,CAAC;QAClB;MACF;MAEA,IAAIyD,IAAI,OAA0B,EAAE;QAClC,EAAE,IAAI,CAAC/F,KAAK,CAACC,GAAG;QAChB,IAAI,CAACU,WAAW,IAAa,CAAC;QAC9B;MACF;MAEA,IACE,CAACoF,IAAI,OAA4B,IAAIA,IAAI,OAAyB,KAClEP,OAAO,KAAKhC,cAAE,CAACG,MAAM,EACrB;QACA,IAAI,CAACrC,aAAa,CAACyE,IAAI,CAAC;QACxB;MACF;IACF;IAEA,IACEA,IAAI,OAAuB,IAC3B,IAAI,CAAC/F,KAAK,CAACU,kBAAkB,IAC7B,IAAI,CAACH,KAAK,CAACC,UAAU,CAAC,IAAI,CAACR,KAAK,CAACC,GAAG,GAAG,CAAC,CAAC,OAA8B,EACvE;MACA,EAAE,IAAI,CAACD,KAAK,CAACC,GAAG;MAChB,IAAI,CAACU,WAAW,IAAe,CAAC;MAChC;IACF;IAEA,KAAK,CAACC,gBAAgB,CAACmF,IAAI,CAAC;EAC9B;EAEAG,aAAaA,CAACC,QAAmB,EAAQ;IACvC,MAAM;MAAEX,OAAO;MAAEpG;IAAK,CAAC,GAAG,IAAI,CAACY,KAAK;IACpC,IAAIZ,IAAI,OAAa,IAAI+G,QAAQ,QAAmB,EAAE;MAGpDX,OAAO,CAACY,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE5C,cAAE,CAACwC,MAAM,CAAC;MAChC,IAAI,CAAChG,KAAK,CAACU,kBAAkB,GAAG,KAAK;IACvC,CAAC,MAAM,IAAItB,IAAI,QAAmB,EAAE;MAElCoG,OAAO,CAACZ,IAAI,CAACpB,cAAE,CAACG,MAAM,CAAC;IACzB,CAAC,MAAM,IAAIvE,IAAI,QAAiB,EAAE;MAChC,MAAMU,GAAG,GAAG0F,OAAO,CAACA,OAAO,CAACtF,MAAM,GAAG,CAAC,CAAC;MACvC,IAAKJ,GAAG,KAAK0D,cAAE,CAACG,MAAM,IAAIwC,QAAQ,OAAa,IAAKrG,GAAG,KAAK0D,cAAE,CAACwC,MAAM,EAAE;QACrER,OAAO,CAACa,GAAG,CAAC,CAAC;QACb,IAAI,CAACrG,KAAK,CAACU,kBAAkB,GAC3B8E,OAAO,CAACA,OAAO,CAACtF,MAAM,GAAG,CAAC,CAAC,KAAKsD,cAAE,CAACW,MAAM;MAC7C,CAAC,MAAM;QACL,IAAI,CAACZ,UAAU,CAACC,cAAE,CAACW,MAAM,CAAC;QAC1B,IAAI,CAACnE,KAAK,CAACU,kBAAkB,GAAG,IAAI;MACtC;IACF,CAAC,MAAM;MACL,IAAI,CAACV,KAAK,CAACU,kBAAkB,GAAG,IAAA4F,iCAA0B,EAAClH,IAAI,CAAC;IAClE;EACF;AACF,CAAC;AAAAmH,OAAA,CAAAC,OAAA,GAAA9G,QAAA"}