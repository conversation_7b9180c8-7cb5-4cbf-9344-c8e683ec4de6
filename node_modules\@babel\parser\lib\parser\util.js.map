{"version": 3, "names": ["_types", "require", "_tokenizer", "_whitespace", "_identifier", "_classScope", "_expressionScope", "_scopeflags", "_productionParameter", "_parseError", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tokenizer", "addExtra", "node", "key", "value", "enumerable", "extra", "Object", "defineProperty", "isContextual", "token", "state", "type", "containsEsc", "isUnparsedContextual", "nameStart", "name", "nameEnd", "length", "input", "slice", "nextCh", "charCodeAt", "isIdentifierChar", "isLookaheadContextual", "next", "nextTokenStart", "eatContextual", "expectContextual", "toParseError", "raise", "at", "startLoc", "unexpected", "canInsertSemicolon", "match", "hasPrecedingLineBreak", "lineBreak", "test", "lastTokEndLoc", "index", "start", "hasFollowingLineBreak", "skipWhiteSpaceToLineBreak", "lastIndex", "end", "isLineTerminator", "eat", "semicolon", "allow<PERSON>i", "Errors", "MissingSemicolon", "expect", "loc", "try<PERSON><PERSON><PERSON>", "fn", "oldState", "clone", "abortSignal", "errors", "failState", "tokensLength", "error", "thrown", "aborted", "SyntaxError", "checkExpressionErrors", "refExpressionErrors", "andThrow", "shorthandAssignLoc", "doubleProtoLoc", "privateKeyLoc", "optionalParametersLoc", "hasErrors", "InvalidCoverInitializedName", "DuplicateProto", "UnexpectedPrivateField", "isLiteralPropertyName", "tokenIsLiteralPropertyName", "isPrivateName", "getPrivateNameSV", "id", "hasPropertyAsPrivateName", "property", "isObjectProperty", "isObjectMethod", "initializeScopes", "inModule", "options", "sourceType", "<PERSON><PERSON><PERSON><PERSON>", "labels", "oldExportedIdentifiers", "exportedIdentifiers", "Set", "oldInModule", "oldScope", "scope", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getScopeHandler", "oldProdParam", "prodParam", "ProductionParameterHandler", "oldClassScope", "classScope", "ClassScopeHandler", "oldExpressionScope", "expressionScope", "ExpressionScopeHandler", "enterInitialScopes", "paramFlags", "PARAM", "PARAM_AWAIT", "enter", "SCOPE_PROGRAM", "checkDestructuringPrivate", "expectPlugin", "exports", "default", "ExpressionErrors", "constructor"], "sources": ["../../src/parser/util.ts"], "sourcesContent": ["import type { Position } from \"../util/location\";\nimport {\n  tokenIsLiteralPropertyName,\n  tt,\n  type TokenType,\n} from \"../tokenizer/types\";\nimport Tokenizer from \"../tokenizer\";\nimport type State from \"../tokenizer/state\";\nimport type { EstreePropertyDefinition, Node, ObjectProperty } from \"../types\";\nimport { lineBreak, skipWhiteSpaceToLineBreak } from \"../util/whitespace\";\nimport { isIdentifierChar } from \"../util/identifier\";\nimport ClassScopeHandler from \"../util/class-scope\";\nimport ExpressionScopeHandler from \"../util/expression-scope\";\nimport { SCOPE_PROGRAM } from \"../util/scopeflags\";\nimport ProductionParameterHandler, {\n  PARAM_AWAIT,\n  PARAM,\n} from \"../util/production-parameter\";\nimport {\n  Errors,\n  type ParseError,\n  type ParseErrorConstructor,\n} from \"../parse-error\";\nimport type Parser from \".\";\n\nimport type ScopeHandler from \"../util/scope\";\n\ntype TryParse<Node, Error, Thrown, Aborted, FailState> = {\n  node: Node;\n  error: Error;\n  thrown: Thrown;\n  aborted: Aborted;\n  failState: FailState;\n};\n\n// ## Parser utilities\n\nexport default abstract class UtilParser extends Tokenizer {\n  // Forward-declaration: defined in parser/index.js\n  abstract getScopeHandler(): { new (...args: any): ScopeHandler };\n\n  addExtra(\n    node: Partial<Node>,\n    key: string,\n    value: any,\n    enumerable: boolean = true,\n  ): void {\n    if (!node) return;\n\n    const extra = (node.extra = node.extra || {});\n    if (enumerable) {\n      extra[key] = value;\n    } else {\n      Object.defineProperty(extra, key, { enumerable, value });\n    }\n  }\n\n  // Tests whether parsed token is a contextual keyword.\n\n  isContextual(token: TokenType): boolean {\n    return this.state.type === token && !this.state.containsEsc;\n  }\n\n  isUnparsedContextual(nameStart: number, name: string): boolean {\n    const nameEnd = nameStart + name.length;\n    if (this.input.slice(nameStart, nameEnd) === name) {\n      const nextCh = this.input.charCodeAt(nameEnd);\n      return !(\n        isIdentifierChar(nextCh) ||\n        // check if `nextCh is between 0xd800 - 0xdbff,\n        // if `nextCh` is NaN, `NaN & 0xfc00` is 0, the function\n        // returns true\n        (nextCh & 0xfc00) === 0xd800\n      );\n    }\n    return false;\n  }\n\n  isLookaheadContextual(name: string): boolean {\n    const next = this.nextTokenStart();\n    return this.isUnparsedContextual(next, name);\n  }\n\n  // Consumes contextual keyword if possible.\n\n  eatContextual(token: TokenType): boolean {\n    if (this.isContextual(token)) {\n      this.next();\n      return true;\n    }\n    return false;\n  }\n\n  // Asserts that following token is given contextual keyword.\n\n  expectContextual(\n    token: TokenType,\n    toParseError?: ParseErrorConstructor<any>,\n  ): void {\n    if (!this.eatContextual(token)) {\n      if (toParseError != null) {\n        throw this.raise(toParseError, { at: this.state.startLoc });\n      }\n      this.unexpected(null, token);\n    }\n  }\n\n  // Test whether a semicolon can be inserted at the current position.\n\n  canInsertSemicolon(): boolean {\n    return (\n      this.match(tt.eof) ||\n      this.match(tt.braceR) ||\n      this.hasPrecedingLineBreak()\n    );\n  }\n\n  hasPrecedingLineBreak(): boolean {\n    return lineBreak.test(\n      this.input.slice(this.state.lastTokEndLoc.index, this.state.start),\n    );\n  }\n\n  hasFollowingLineBreak(): boolean {\n    skipWhiteSpaceToLineBreak.lastIndex = this.state.end;\n    return skipWhiteSpaceToLineBreak.test(this.input);\n  }\n\n  isLineTerminator(): boolean {\n    return this.eat(tt.semi) || this.canInsertSemicolon();\n  }\n\n  // Consume a semicolon, or, failing that, see if we are allowed to\n  // pretend that there is a semicolon at this position.\n\n  semicolon(allowAsi: boolean = true): void {\n    if (allowAsi ? this.isLineTerminator() : this.eat(tt.semi)) return;\n    this.raise(Errors.MissingSemicolon, { at: this.state.lastTokEndLoc });\n  }\n\n  // Expect a token of a given type. If found, consume it, otherwise,\n  // raise an unexpected token error at given pos.\n\n  expect(type: TokenType, loc?: Position | null): void {\n    this.eat(type) || this.unexpected(loc, type);\n  }\n\n  // tryParse will clone parser state.\n  // It is expensive and should be used with cautions\n  tryParse<T extends Node | ReadonlyArray<Node>>(\n    fn: (abort: (node?: T) => never) => T,\n    oldState: State = this.state.clone(),\n  ):\n    | TryParse<T, null, false, false, null>\n    | TryParse<T | null, ParseError<any>, boolean, false, State>\n    | TryParse<T | null, null, false, true, State> {\n    const abortSignal: {\n      node: T | null;\n    } = { node: null };\n    try {\n      const node = fn((node = null) => {\n        abortSignal.node = node;\n        throw abortSignal;\n      });\n      if (this.state.errors.length > oldState.errors.length) {\n        const failState = this.state;\n        this.state = oldState;\n        // tokensLength should be preserved during error recovery mode\n        // since the parser does not halt and will instead parse the\n        // remaining tokens\n        this.state.tokensLength = failState.tokensLength;\n        return {\n          node,\n          error: failState.errors[oldState.errors.length],\n          thrown: false,\n          aborted: false,\n          failState,\n        };\n      }\n\n      return {\n        node,\n        error: null,\n        thrown: false,\n        aborted: false,\n        failState: null,\n      };\n    } catch (error) {\n      const failState = this.state;\n      this.state = oldState;\n      if (error instanceof SyntaxError) {\n        // @ts-expect-error casting general syntax error to parse error\n        return { node: null, error, thrown: true, aborted: false, failState };\n      }\n      if (error === abortSignal) {\n        return {\n          node: abortSignal.node,\n          error: null,\n          thrown: false,\n          aborted: true,\n          failState,\n        };\n      }\n\n      throw error;\n    }\n  }\n\n  checkExpressionErrors(\n    refExpressionErrors: ExpressionErrors | undefined | null,\n    andThrow: boolean,\n  ) {\n    if (!refExpressionErrors) return false;\n    const {\n      shorthandAssignLoc,\n      doubleProtoLoc,\n      privateKeyLoc,\n      optionalParametersLoc,\n    } = refExpressionErrors;\n\n    const hasErrors =\n      !!shorthandAssignLoc ||\n      !!doubleProtoLoc ||\n      !!optionalParametersLoc ||\n      !!privateKeyLoc;\n\n    if (!andThrow) {\n      return hasErrors;\n    }\n\n    if (shorthandAssignLoc != null) {\n      this.raise(Errors.InvalidCoverInitializedName, {\n        at: shorthandAssignLoc,\n      });\n    }\n\n    if (doubleProtoLoc != null) {\n      this.raise(Errors.DuplicateProto, { at: doubleProtoLoc });\n    }\n\n    if (privateKeyLoc != null) {\n      this.raise(Errors.UnexpectedPrivateField, { at: privateKeyLoc });\n    }\n\n    if (optionalParametersLoc != null) {\n      this.unexpected(optionalParametersLoc);\n    }\n  }\n\n  /**\n   * Test if current token is a literal property name\n   * https://tc39.es/ecma262/#prod-LiteralPropertyName\n   * LiteralPropertyName:\n   *   IdentifierName\n   *   StringLiteral\n   *   NumericLiteral\n   *   BigIntLiteral\n   */\n  isLiteralPropertyName(): boolean {\n    return tokenIsLiteralPropertyName(this.state.type);\n  }\n\n  /**\n   * Test if given node is a PrivateName\n   * will be overridden in ESTree plugin\n   */\n  isPrivateName(node: Node): boolean {\n    return node.type === \"PrivateName\";\n  }\n\n  /**\n   * Return the string value of a given private name\n   * WITHOUT `#`\n   * @see {@link https://tc39.es/ecma262/#sec-static-semantics-stringvalue}\n   */\n  getPrivateNameSV(node: Node): string {\n    return node.id.name;\n  }\n\n  /**\n   * Return whether the given node is a member/optional chain that\n   * contains a private name as its property\n   * It is overridden in ESTree plugin\n   */\n  hasPropertyAsPrivateName(node: Node): boolean {\n    return (\n      (node.type === \"MemberExpression\" ||\n        node.type === \"OptionalMemberExpression\") &&\n      this.isPrivateName(node.property)\n    );\n  }\n\n  isObjectProperty(\n    node: Node,\n  ): node is ObjectProperty | EstreePropertyDefinition {\n    return node.type === \"ObjectProperty\";\n  }\n\n  isObjectMethod(node: Node): boolean {\n    return node.type === \"ObjectMethod\";\n  }\n\n  initializeScopes(\n    this: Parser,\n    inModule: boolean = this.options.sourceType === \"module\",\n  ): () => void {\n    // Initialize state\n    const oldLabels = this.state.labels;\n    this.state.labels = [];\n\n    const oldExportedIdentifiers = this.exportedIdentifiers;\n    this.exportedIdentifiers = new Set();\n\n    // initialize scopes\n    const oldInModule = this.inModule;\n    this.inModule = inModule;\n\n    const oldScope = this.scope;\n    const ScopeHandler = this.getScopeHandler();\n    this.scope = new ScopeHandler(this, inModule);\n\n    const oldProdParam = this.prodParam;\n    this.prodParam = new ProductionParameterHandler();\n\n    const oldClassScope = this.classScope;\n    this.classScope = new ClassScopeHandler(this);\n\n    const oldExpressionScope = this.expressionScope;\n    this.expressionScope = new ExpressionScopeHandler(this);\n\n    return () => {\n      // Revert state\n      this.state.labels = oldLabels;\n      this.exportedIdentifiers = oldExportedIdentifiers;\n\n      // Revert scopes\n      this.inModule = oldInModule;\n      this.scope = oldScope;\n      this.prodParam = oldProdParam;\n      this.classScope = oldClassScope;\n      this.expressionScope = oldExpressionScope;\n    };\n  }\n\n  enterInitialScopes() {\n    let paramFlags = PARAM;\n    if (this.inModule) {\n      paramFlags |= PARAM_AWAIT;\n    }\n    this.scope.enter(SCOPE_PROGRAM);\n    this.prodParam.enter(paramFlags);\n  }\n\n  checkDestructuringPrivate(refExpressionErrors: ExpressionErrors) {\n    const { privateKeyLoc } = refExpressionErrors;\n    if (privateKeyLoc !== null) {\n      this.expectPlugin(\"destructuringPrivate\", privateKeyLoc);\n    }\n  }\n}\n\n/**\n * The ExpressionErrors is a context struct used to track ambiguous patterns\n * When we are sure the parsed pattern is a RHS, which means it is not a pattern,\n * we will throw on this position on invalid assign syntax, otherwise it will be reset to -1\n *\n * Types of ExpressionErrors:\n *\n * - **shorthandAssignLoc**: track initializer `=` position\n * - **doubleProtoLoc**: track the duplicate `__proto__` key position\n * - **privateKey**: track private key `#p` position\n * - **optionalParametersLoc**: track the optional parameter (`?`).\n * It's only used by typescript and flow plugins\n */\nexport class ExpressionErrors {\n  shorthandAssignLoc: Position | undefined | null = null;\n  doubleProtoLoc: Position | undefined | null = null;\n  privateKeyLoc: Position | undefined | null = null;\n  optionalParametersLoc: Position | undefined | null = null;\n}\n"], "mappings": ";;;;;;AACA,IAAAA,MAAA,GAAAC,OAAA;AAKA,IAAAC,UAAA,GAAAD,OAAA;AAGA,IAAAE,WAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAH,OAAA;AACA,IAAAI,WAAA,GAAAJ,OAAA;AACA,IAAAK,gBAAA,GAAAL,OAAA;AACA,IAAAM,WAAA,GAAAN,OAAA;AACA,IAAAO,oBAAA,GAAAP,OAAA;AAIA,IAAAQ,WAAA,GAAAR,OAAA;AAmBe,MAAeS,UAAU,SAASC,kBAAS,CAAC;EAIzDC,QAAQA,CACNC,IAAmB,EACnBC,GAAW,EACXC,KAAU,EACVC,UAAmB,GAAG,IAAI,EACpB;IACN,IAAI,CAACH,IAAI,EAAE;IAEX,MAAMI,KAAK,GAAIJ,IAAI,CAACI,KAAK,GAAGJ,IAAI,CAACI,KAAK,IAAI,CAAC,CAAE;IAC7C,IAAID,UAAU,EAAE;MACdC,KAAK,CAACH,GAAG,CAAC,GAAGC,KAAK;IACpB,CAAC,MAAM;MACLG,MAAM,CAACC,cAAc,CAACF,KAAK,EAAEH,GAAG,EAAE;QAAEE,UAAU;QAAED;MAAM,CAAC,CAAC;IAC1D;EACF;EAIAK,YAAYA,CAACC,KAAgB,EAAW;IACtC,OAAO,IAAI,CAACC,KAAK,CAACC,IAAI,KAAKF,KAAK,IAAI,CAAC,IAAI,CAACC,KAAK,CAACE,WAAW;EAC7D;EAEAC,oBAAoBA,CAACC,SAAiB,EAAEC,IAAY,EAAW;IAC7D,MAAMC,OAAO,GAAGF,SAAS,GAAGC,IAAI,CAACE,MAAM;IACvC,IAAI,IAAI,CAACC,KAAK,CAACC,KAAK,CAACL,SAAS,EAAEE,OAAO,CAAC,KAAKD,IAAI,EAAE;MACjD,MAAMK,MAAM,GAAG,IAAI,CAACF,KAAK,CAACG,UAAU,CAACL,OAAO,CAAC;MAC7C,OAAO,EACL,IAAAM,4BAAgB,EAACF,MAAM,CAAC,IAIxB,CAACA,MAAM,GAAG,MAAM,MAAM,MAAM,CAC7B;IACH;IACA,OAAO,KAAK;EACd;EAEAG,qBAAqBA,CAACR,IAAY,EAAW;IAC3C,MAAMS,IAAI,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IAClC,OAAO,IAAI,CAACZ,oBAAoB,CAACW,IAAI,EAAET,IAAI,CAAC;EAC9C;EAIAW,aAAaA,CAACjB,KAAgB,EAAW;IACvC,IAAI,IAAI,CAACD,YAAY,CAACC,KAAK,CAAC,EAAE;MAC5B,IAAI,CAACe,IAAI,CAAC,CAAC;MACX,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAIAG,gBAAgBA,CACdlB,KAAgB,EAChBmB,YAAyC,EACnC;IACN,IAAI,CAAC,IAAI,CAACF,aAAa,CAACjB,KAAK,CAAC,EAAE;MAC9B,IAAImB,YAAY,IAAI,IAAI,EAAE;QACxB,MAAM,IAAI,CAACC,KAAK,CAACD,YAAY,EAAE;UAAEE,EAAE,EAAE,IAAI,CAACpB,KAAK,CAACqB;QAAS,CAAC,CAAC;MAC7D;MACA,IAAI,CAACC,UAAU,CAAC,IAAI,EAAEvB,KAAK,CAAC;IAC9B;EACF;EAIAwB,kBAAkBA,CAAA,EAAY;IAC5B,OACE,IAAI,CAACC,KAAK,IAAO,CAAC,IAClB,IAAI,CAACA,KAAK,EAAU,CAAC,IACrB,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAEhC;EAEAA,qBAAqBA,CAAA,EAAY;IAC/B,OAAOC,qBAAS,CAACC,IAAI,CACnB,IAAI,CAACnB,KAAK,CAACC,KAAK,CAAC,IAAI,CAACT,KAAK,CAAC4B,aAAa,CAACC,KAAK,EAAE,IAAI,CAAC7B,KAAK,CAAC8B,KAAK,CACnE,CAAC;EACH;EAEAC,qBAAqBA,CAAA,EAAY;IAC/BC,qCAAyB,CAACC,SAAS,GAAG,IAAI,CAACjC,KAAK,CAACkC,GAAG;IACpD,OAAOF,qCAAyB,CAACL,IAAI,CAAC,IAAI,CAACnB,KAAK,CAAC;EACnD;EAEA2B,gBAAgBA,CAAA,EAAY;IAC1B,OAAO,IAAI,CAACC,GAAG,GAAQ,CAAC,IAAI,IAAI,CAACb,kBAAkB,CAAC,CAAC;EACvD;EAKAc,SAASA,CAACC,QAAiB,GAAG,IAAI,EAAQ;IACxC,IAAIA,QAAQ,GAAG,IAAI,CAACH,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAACC,GAAG,GAAQ,CAAC,EAAE;IAC5D,IAAI,CAACjB,KAAK,CAACoB,kBAAM,CAACC,gBAAgB,EAAE;MAAEpB,EAAE,EAAE,IAAI,CAACpB,KAAK,CAAC4B;IAAc,CAAC,CAAC;EACvE;EAKAa,MAAMA,CAACxC,IAAe,EAAEyC,GAAqB,EAAQ;IACnD,IAAI,CAACN,GAAG,CAACnC,IAAI,CAAC,IAAI,IAAI,CAACqB,UAAU,CAACoB,GAAG,EAAEzC,IAAI,CAAC;EAC9C;EAIA0C,QAAQA,CACNC,EAAqC,EACrCC,QAAe,GAAG,IAAI,CAAC7C,KAAK,CAAC8C,KAAK,CAAC,CAAC,EAIW;IAC/C,MAAMC,WAEL,GAAG;MAAExD,IAAI,EAAE;IAAK,CAAC;IAClB,IAAI;MACF,MAAMA,IAAI,GAAGqD,EAAE,CAAC,CAACrD,IAAI,GAAG,IAAI,KAAK;QAC/BwD,WAAW,CAACxD,IAAI,GAAGA,IAAI;QACvB,MAAMwD,WAAW;MACnB,CAAC,CAAC;MACF,IAAI,IAAI,CAAC/C,KAAK,CAACgD,MAAM,CAACzC,MAAM,GAAGsC,QAAQ,CAACG,MAAM,CAACzC,MAAM,EAAE;QACrD,MAAM0C,SAAS,GAAG,IAAI,CAACjD,KAAK;QAC5B,IAAI,CAACA,KAAK,GAAG6C,QAAQ;QAIrB,IAAI,CAAC7C,KAAK,CAACkD,YAAY,GAAGD,SAAS,CAACC,YAAY;QAChD,OAAO;UACL3D,IAAI;UACJ4D,KAAK,EAAEF,SAAS,CAACD,MAAM,CAACH,QAAQ,CAACG,MAAM,CAACzC,MAAM,CAAC;UAC/C6C,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE,KAAK;UACdJ;QACF,CAAC;MACH;MAEA,OAAO;QACL1D,IAAI;QACJ4D,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,KAAK;QACdJ,SAAS,EAAE;MACb,CAAC;IACH,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,MAAMF,SAAS,GAAG,IAAI,CAACjD,KAAK;MAC5B,IAAI,CAACA,KAAK,GAAG6C,QAAQ;MACrB,IAAIM,KAAK,YAAYG,WAAW,EAAE;QAEhC,OAAO;UAAE/D,IAAI,EAAE,IAAI;UAAE4D,KAAK;UAAEC,MAAM,EAAE,IAAI;UAAEC,OAAO,EAAE,KAAK;UAAEJ;QAAU,CAAC;MACvE;MACA,IAAIE,KAAK,KAAKJ,WAAW,EAAE;QACzB,OAAO;UACLxD,IAAI,EAAEwD,WAAW,CAACxD,IAAI;UACtB4D,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE,IAAI;UACbJ;QACF,CAAC;MACH;MAEA,MAAME,KAAK;IACb;EACF;EAEAI,qBAAqBA,CACnBC,mBAAwD,EACxDC,QAAiB,EACjB;IACA,IAAI,CAACD,mBAAmB,EAAE,OAAO,KAAK;IACtC,MAAM;MACJE,kBAAkB;MAClBC,cAAc;MACdC,aAAa;MACbC;IACF,CAAC,GAAGL,mBAAmB;IAEvB,MAAMM,SAAS,GACb,CAAC,CAACJ,kBAAkB,IACpB,CAAC,CAACC,cAAc,IAChB,CAAC,CAACE,qBAAqB,IACvB,CAAC,CAACD,aAAa;IAEjB,IAAI,CAACH,QAAQ,EAAE;MACb,OAAOK,SAAS;IAClB;IAEA,IAAIJ,kBAAkB,IAAI,IAAI,EAAE;MAC9B,IAAI,CAACvC,KAAK,CAACoB,kBAAM,CAACwB,2BAA2B,EAAE;QAC7C3C,EAAE,EAAEsC;MACN,CAAC,CAAC;IACJ;IAEA,IAAIC,cAAc,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACxC,KAAK,CAACoB,kBAAM,CAACyB,cAAc,EAAE;QAAE5C,EAAE,EAAEuC;MAAe,CAAC,CAAC;IAC3D;IAEA,IAAIC,aAAa,IAAI,IAAI,EAAE;MACzB,IAAI,CAACzC,KAAK,CAACoB,kBAAM,CAAC0B,sBAAsB,EAAE;QAAE7C,EAAE,EAAEwC;MAAc,CAAC,CAAC;IAClE;IAEA,IAAIC,qBAAqB,IAAI,IAAI,EAAE;MACjC,IAAI,CAACvC,UAAU,CAACuC,qBAAqB,CAAC;IACxC;EACF;EAWAK,qBAAqBA,CAAA,EAAY;IAC/B,OAAO,IAAAC,iCAA0B,EAAC,IAAI,CAACnE,KAAK,CAACC,IAAI,CAAC;EACpD;EAMAmE,aAAaA,CAAC7E,IAAU,EAAW;IACjC,OAAOA,IAAI,CAACU,IAAI,KAAK,aAAa;EACpC;EAOAoE,gBAAgBA,CAAC9E,IAAU,EAAU;IACnC,OAAOA,IAAI,CAAC+E,EAAE,CAACjE,IAAI;EACrB;EAOAkE,wBAAwBA,CAAChF,IAAU,EAAW;IAC5C,OACE,CAACA,IAAI,CAACU,IAAI,KAAK,kBAAkB,IAC/BV,IAAI,CAACU,IAAI,KAAK,0BAA0B,KAC1C,IAAI,CAACmE,aAAa,CAAC7E,IAAI,CAACiF,QAAQ,CAAC;EAErC;EAEAC,gBAAgBA,CACdlF,IAAU,EACyC;IACnD,OAAOA,IAAI,CAACU,IAAI,KAAK,gBAAgB;EACvC;EAEAyE,cAAcA,CAACnF,IAAU,EAAW;IAClC,OAAOA,IAAI,CAACU,IAAI,KAAK,cAAc;EACrC;EAEA0E,gBAAgBA,CAEdC,QAAiB,GAAG,IAAI,CAACC,OAAO,CAACC,UAAU,KAAK,QAAQ,EAC5C;IAEZ,MAAMC,SAAS,GAAG,IAAI,CAAC/E,KAAK,CAACgF,MAAM;IACnC,IAAI,CAAChF,KAAK,CAACgF,MAAM,GAAG,EAAE;IAEtB,MAAMC,sBAAsB,GAAG,IAAI,CAACC,mBAAmB;IACvD,IAAI,CAACA,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAGpC,MAAMC,WAAW,GAAG,IAAI,CAACR,QAAQ;IACjC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IAExB,MAAMS,QAAQ,GAAG,IAAI,CAACC,KAAK;IAC3B,MAAMC,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IAC3C,IAAI,CAACF,KAAK,GAAG,IAAIC,YAAY,CAAC,IAAI,EAAEX,QAAQ,CAAC;IAE7C,MAAMa,YAAY,GAAG,IAAI,CAACC,SAAS;IACnC,IAAI,CAACA,SAAS,GAAG,IAAIC,4BAA0B,CAAC,CAAC;IAEjD,MAAMC,aAAa,GAAG,IAAI,CAACC,UAAU;IACrC,IAAI,CAACA,UAAU,GAAG,IAAIC,mBAAiB,CAAC,IAAI,CAAC;IAE7C,MAAMC,kBAAkB,GAAG,IAAI,CAACC,eAAe;IAC/C,IAAI,CAACA,eAAe,GAAG,IAAIC,wBAAsB,CAAC,IAAI,CAAC;IAEvD,OAAO,MAAM;MAEX,IAAI,CAACjG,KAAK,CAACgF,MAAM,GAAGD,SAAS;MAC7B,IAAI,CAACG,mBAAmB,GAAGD,sBAAsB;MAGjD,IAAI,CAACL,QAAQ,GAAGQ,WAAW;MAC3B,IAAI,CAACE,KAAK,GAAGD,QAAQ;MACrB,IAAI,CAACK,SAAS,GAAGD,YAAY;MAC7B,IAAI,CAACI,UAAU,GAAGD,aAAa;MAC/B,IAAI,CAACI,eAAe,GAAGD,kBAAkB;IAC3C,CAAC;EACH;EAEAG,kBAAkBA,CAAA,EAAG;IACnB,IAAIC,UAAU,GAAGC,0BAAK;IACtB,IAAI,IAAI,CAACxB,QAAQ,EAAE;MACjBuB,UAAU,IAAIE,gCAAW;IAC3B;IACA,IAAI,CAACf,KAAK,CAACgB,KAAK,CAACC,yBAAa,CAAC;IAC/B,IAAI,CAACb,SAAS,CAACY,KAAK,CAACH,UAAU,CAAC;EAClC;EAEAK,yBAAyBA,CAAChD,mBAAqC,EAAE;IAC/D,MAAM;MAAEI;IAAc,CAAC,GAAGJ,mBAAmB;IAC7C,IAAII,aAAa,KAAK,IAAI,EAAE;MAC1B,IAAI,CAAC6C,YAAY,CAAC,sBAAsB,EAAE7C,aAAa,CAAC;IAC1D;EACF;AACF;AAAC8C,OAAA,CAAAC,OAAA,GAAAvH,UAAA;AAeM,MAAMwH,gBAAgB,CAAC;EAAAC,YAAA;IAAA,KAC5BnD,kBAAkB,GAAgC,IAAI;IAAA,KACtDC,cAAc,GAAgC,IAAI;IAAA,KAClDC,aAAa,GAAgC,IAAI;IAAA,KACjDC,qBAAqB,GAAgC,IAAI;EAAA;AAC3D;AAAC6C,OAAA,CAAAE,gBAAA,GAAAA,gBAAA"}