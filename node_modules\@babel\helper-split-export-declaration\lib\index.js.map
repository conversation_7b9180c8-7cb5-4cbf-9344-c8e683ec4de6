{"version": 3, "names": ["_t", "require", "cloneNode", "exportNamedDeclaration", "exportSpecifier", "identifier", "variableDeclaration", "variableDeclarator", "splitExportDeclaration", "exportDeclaration", "isExportDeclaration", "isExportAllDeclaration", "Error", "isExportDefaultDeclaration", "declaration", "get", "standaloneDeclaration", "isFunctionDeclaration", "isClassDeclaration", "scope", "isScope", "parent", "id", "node", "needBindingRegistration", "generateUidIdentifier", "isFunctionExpression", "isClassExpression", "updatedDeclaration", "updatedExportDeclaration", "insertAfter", "replaceWith", "registerDeclaration", "length", "bindingIdentifiers", "getOuterBindingIdentifiers", "specifiers", "Object", "keys", "map", "name", "aliasDeclar"], "sources": ["../src/index.ts"], "sourcesContent": ["import {\n  cloneNode,\n  exportNamedDeclaration,\n  exportSpecifier,\n  identifier,\n  variableDeclaration,\n  variableDeclarator,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type { NodePath } from \"@babel/traverse\";\n\nexport default function splitExportDeclaration(\n  exportDeclaration: NodePath<\n    t.ExportDefaultDeclaration | t.ExportNamedDeclaration\n  >,\n) {\n  if (\n    !exportDeclaration.isExportDeclaration() ||\n    exportDeclaration.isExportAllDeclaration()\n  ) {\n    throw new Error(\"Only default and named export declarations can be split.\");\n  }\n\n  // build specifiers that point back to this export declaration\n\n  if (exportDeclaration.isExportDefaultDeclaration()) {\n    const declaration = exportDeclaration.get(\"declaration\");\n    const standaloneDeclaration =\n      declaration.isFunctionDeclaration() || declaration.isClassDeclaration();\n\n    const scope = declaration.isScope()\n      ? declaration.scope.parent\n      : declaration.scope;\n\n    // @ts-expect-error id is not defined in expressions other than function/class\n    let id = declaration.node.id;\n    let needBindingRegistration = false;\n\n    if (!id) {\n      needBindingRegistration = true;\n\n      id = scope.generateUidIdentifier(\"default\");\n\n      if (\n        standaloneDeclaration ||\n        declaration.isFunctionExpression() ||\n        declaration.isClassExpression()\n      ) {\n        declaration.node.id = cloneNode(id);\n      }\n    }\n\n    const updatedDeclaration = standaloneDeclaration\n      ? declaration.node\n      : variableDeclaration(\"var\", [\n          variableDeclarator(\n            cloneNode(id),\n            // @ts-expect-error When `standaloneDeclaration` is false, declaration must not be a Function/ClassDeclaration\n            declaration.node,\n          ),\n        ]);\n\n    const updatedExportDeclaration = exportNamedDeclaration(null, [\n      exportSpecifier(cloneNode(id), identifier(\"default\")),\n    ]);\n\n    exportDeclaration.insertAfter(updatedExportDeclaration);\n    exportDeclaration.replaceWith(updatedDeclaration);\n\n    if (needBindingRegistration) {\n      scope.registerDeclaration(exportDeclaration);\n    }\n\n    return exportDeclaration;\n  } else if (\n    // @ts-expect-error TS can not narrow down to NodePath<t.ExportNamedDeclaration>\n    exportDeclaration.get(\"specifiers\").length > 0\n  ) {\n    throw new Error(\"It doesn't make sense to split exported specifiers.\");\n  }\n\n  const declaration = exportDeclaration.get(\"declaration\");\n  const bindingIdentifiers = declaration.getOuterBindingIdentifiers();\n\n  const specifiers = Object.keys(bindingIdentifiers).map(name => {\n    return exportSpecifier(identifier(name), identifier(name));\n  });\n\n  const aliasDeclar = exportNamedDeclaration(null, specifiers);\n\n  exportDeclaration.insertAfter(aliasDeclar);\n  exportDeclaration.replaceWith(declaration.node);\n  return exportDeclaration;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAOsB;EANpBC,SAAS;EACTC,sBAAsB;EACtBC,eAAe;EACfC,UAAU;EACVC,mBAAmB;EACnBC;AAAkB,IAAAP,EAAA;AAKL,SAASQ,sBAAsBA,CAC5CC,iBAEC,EACD;EACA,IACE,CAACA,iBAAiB,CAACC,mBAAmB,CAAC,CAAC,IACxCD,iBAAiB,CAACE,sBAAsB,CAAC,CAAC,EAC1C;IACA,MAAM,IAAIC,KAAK,CAAC,0DAA0D,CAAC;EAC7E;EAIA,IAAIH,iBAAiB,CAACI,0BAA0B,CAAC,CAAC,EAAE;IAClD,MAAMC,WAAW,GAAGL,iBAAiB,CAACM,GAAG,CAAC,aAAa,CAAC;IACxD,MAAMC,qBAAqB,GACzBF,WAAW,CAACG,qBAAqB,CAAC,CAAC,IAAIH,WAAW,CAACI,kBAAkB,CAAC,CAAC;IAEzE,MAAMC,KAAK,GAAGL,WAAW,CAACM,OAAO,CAAC,CAAC,GAC/BN,WAAW,CAACK,KAAK,CAACE,MAAM,GACxBP,WAAW,CAACK,KAAK;IAGrB,IAAIG,EAAE,GAAGR,WAAW,CAACS,IAAI,CAACD,EAAE;IAC5B,IAAIE,uBAAuB,GAAG,KAAK;IAEnC,IAAI,CAACF,EAAE,EAAE;MACPE,uBAAuB,GAAG,IAAI;MAE9BF,EAAE,GAAGH,KAAK,CAACM,qBAAqB,CAAC,SAAS,CAAC;MAE3C,IACET,qBAAqB,IACrBF,WAAW,CAACY,oBAAoB,CAAC,CAAC,IAClCZ,WAAW,CAACa,iBAAiB,CAAC,CAAC,EAC/B;QACAb,WAAW,CAACS,IAAI,CAACD,EAAE,GAAGpB,SAAS,CAACoB,EAAE,CAAC;MACrC;IACF;IAEA,MAAMM,kBAAkB,GAAGZ,qBAAqB,GAC5CF,WAAW,CAACS,IAAI,GAChBjB,mBAAmB,CAAC,KAAK,EAAE,CACzBC,kBAAkB,CAChBL,SAAS,CAACoB,EAAE,CAAC,EAEbR,WAAW,CAACS,IACd,CAAC,CACF,CAAC;IAEN,MAAMM,wBAAwB,GAAG1B,sBAAsB,CAAC,IAAI,EAAE,CAC5DC,eAAe,CAACF,SAAS,CAACoB,EAAE,CAAC,EAAEjB,UAAU,CAAC,SAAS,CAAC,CAAC,CACtD,CAAC;IAEFI,iBAAiB,CAACqB,WAAW,CAACD,wBAAwB,CAAC;IACvDpB,iBAAiB,CAACsB,WAAW,CAACH,kBAAkB,CAAC;IAEjD,IAAIJ,uBAAuB,EAAE;MAC3BL,KAAK,CAACa,mBAAmB,CAACvB,iBAAiB,CAAC;IAC9C;IAEA,OAAOA,iBAAiB;EAC1B,CAAC,MAAM,IAELA,iBAAiB,CAACM,GAAG,CAAC,YAAY,CAAC,CAACkB,MAAM,GAAG,CAAC,EAC9C;IACA,MAAM,IAAIrB,KAAK,CAAC,qDAAqD,CAAC;EACxE;EAEA,MAAME,WAAW,GAAGL,iBAAiB,CAACM,GAAG,CAAC,aAAa,CAAC;EACxD,MAAMmB,kBAAkB,GAAGpB,WAAW,CAACqB,0BAA0B,CAAC,CAAC;EAEnE,MAAMC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACJ,kBAAkB,CAAC,CAACK,GAAG,CAACC,IAAI,IAAI;IAC7D,OAAOpC,eAAe,CAACC,UAAU,CAACmC,IAAI,CAAC,EAAEnC,UAAU,CAACmC,IAAI,CAAC,CAAC;EAC5D,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAGtC,sBAAsB,CAAC,IAAI,EAAEiC,UAAU,CAAC;EAE5D3B,iBAAiB,CAACqB,WAAW,CAACW,WAAW,CAAC;EAC1ChC,iBAAiB,CAACsB,WAAW,CAACjB,WAAW,CAACS,IAAI,CAAC;EAC/C,OAAOd,iBAAiB;AAC1B"}