{"version": 3, "names": ["visitors", "require", "exports", "_t", "cache", "_traverseNode", "_path", "_scope", "_hub", "VISITOR_KEYS", "removeProperties", "traverseFast", "traverse", "parent", "opts", "scope", "state", "parentPath", "noScope", "type", "Error", "explode", "traverseNode", "_default", "default", "verify", "cheap", "node", "enter", "path", "<PERSON><PERSON><PERSON><PERSON>", "clearNode", "delete", "tree", "hasDenylistedType", "has", "stop", "hasType", "denylistTypes", "includes", "denylist"], "sources": ["../src/index.ts"], "sourcesContent": ["import * as visitors from \"./visitors\";\nimport {\n  VISITOR_KEYS,\n  removeProperties,\n  type RemovePropertiesOptions,\n  traverseFast,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport * as cache from \"./cache\";\nimport type NodePath from \"./path\";\nimport type { default as Scope, Binding } from \"./scope\";\nimport type { ExplodedVisitor, Visitor } from \"./types\";\nimport { traverseNode } from \"./traverse-node\";\n\nexport type { ExplodedVisitor, Visitor, Binding };\nexport { default as NodePath } from \"./path\";\nexport { default as Scope } from \"./scope\";\nexport { default as Hub } from \"./hub\";\nexport type { HubInterface } from \"./hub\";\n\nexport { visitors };\n\nexport type TraverseOptions<S = t.Node> = {\n  scope?: Scope;\n  noScope?: boolean;\n  denylist?: string[];\n  shouldSkip?: (node: NodePath) => boolean;\n} & Visitor<S>;\n\nexport type ExplodedTraverseOptions<S = t.Node> = TraverseOptions<S> &\n  ExplodedVisitor<S>;\n\nfunction traverse<S>(\n  parent: t.Node,\n  opts: TraverseOptions<S>,\n  scope: Scope | undefined,\n  state: S,\n  parentPath?: NodePath,\n): void;\n\nfunction traverse(\n  parent: t.Node,\n  opts: TraverseOptions,\n  scope?: Scope,\n  state?: any,\n  parentPath?: NodePath,\n): void;\n\nfunction traverse<Options extends TraverseOptions>(\n  parent: t.Node,\n  // @ts-expect-error provide {} as default value for Options\n  opts: Options = {},\n  scope?: Scope,\n  state?: any,\n  parentPath?: NodePath,\n) {\n  if (!parent) return;\n\n  if (!opts.noScope && !scope) {\n    if (parent.type !== \"Program\" && parent.type !== \"File\") {\n      throw new Error(\n        \"You must pass a scope and parentPath unless traversing a Program/File. \" +\n          `Instead of that you tried to traverse a ${parent.type} node without ` +\n          \"passing scope and parentPath.\",\n      );\n    }\n  }\n\n  if (!VISITOR_KEYS[parent.type]) {\n    return;\n  }\n\n  visitors.explode(opts as Visitor);\n\n  traverseNode(parent, opts as ExplodedVisitor, scope, state, parentPath);\n}\n\nexport default traverse;\n\ntraverse.visitors = visitors;\ntraverse.verify = visitors.verify;\ntraverse.explode = visitors.explode;\n\ntraverse.cheap = function (node: t.Node, enter: (node: t.Node) => void) {\n  traverseFast(node, enter);\n  return;\n};\n\ntraverse.node = function (\n  node: t.Node,\n  opts: ExplodedTraverseOptions,\n  scope?: Scope,\n  state?: any,\n  path?: NodePath,\n  skipKeys?: Record<string, boolean>,\n) {\n  traverseNode(node, opts, scope, state, path, skipKeys);\n  // traverse.node always returns undefined\n};\n\ntraverse.clearNode = function (node: t.Node, opts?: RemovePropertiesOptions) {\n  removeProperties(node, opts);\n\n  cache.path.delete(node);\n};\n\ntraverse.removeProperties = function (\n  tree: t.Node,\n  opts?: RemovePropertiesOptions,\n) {\n  traverseFast(tree, traverse.clearNode, opts);\n  return tree;\n};\n\ntype HasDenylistedTypeState = {\n  has: boolean;\n  type: t.Node[\"type\"];\n};\nfunction hasDenylistedType(path: NodePath, state: HasDenylistedTypeState) {\n  if (path.node.type === state.type) {\n    state.has = true;\n    path.stop();\n  }\n}\n\ntraverse.hasType = function (\n  tree: t.Node,\n  type: t.Node[\"type\"],\n  denylistTypes?: Array<string>,\n): boolean {\n  // the node we're searching in is denylisted\n  if (denylistTypes?.includes(tree.type)) return false;\n\n  // the type we're looking for is the same as the passed node\n  if (tree.type === type) return true;\n\n  const state: HasDenylistedTypeState = {\n    has: false,\n    type: type,\n  };\n\n  traverse(\n    tree,\n    {\n      noScope: true,\n      denylist: denylistTypes,\n      enter: hasDenylistedType,\n    },\n    null,\n    state,\n  );\n\n  return state.has;\n};\n\ntraverse.cache = cache;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAAuCC,OAAA,CAAAF,QAAA,GAAAA,QAAA;AACvC,IAAAG,EAAA,GAAAF,OAAA;AAOA,IAAAG,KAAA,GAAAH,OAAA;AAIA,IAAAI,aAAA,GAAAJ,OAAA;AAGA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAN,OAAA;AACA,IAAAO,IAAA,GAAAP,OAAA;AAAuC;EAfrCQ,YAAY;EACZC,gBAAgB;EAEhBC;AAAY,IAAAR,EAAA;AA2Cd,SAASS,QAAQA,CACfC,MAAc,EAEdC,IAAa,GAAG,CAAC,CAAC,EAClBC,KAAa,EACbC,KAAW,EACXC,UAAqB,EACrB;EACA,IAAI,CAACJ,MAAM,EAAE;EAEb,IAAI,CAACC,IAAI,CAACI,OAAO,IAAI,CAACH,KAAK,EAAE;IAC3B,IAAIF,MAAM,CAACM,IAAI,KAAK,SAAS,IAAIN,MAAM,CAACM,IAAI,KAAK,MAAM,EAAE;MACvD,MAAM,IAAIC,KAAK,CACb,yEAAyE,GACtE,2CAA0CP,MAAM,CAACM,IAAK,gBAAe,GACtE,+BACJ,CAAC;IACH;EACF;EAEA,IAAI,CAACV,YAAY,CAACI,MAAM,CAACM,IAAI,CAAC,EAAE;IAC9B;EACF;EAEAnB,QAAQ,CAACqB,OAAO,CAACP,IAAe,CAAC;EAEjC,IAAAQ,0BAAY,EAACT,MAAM,EAAEC,IAAI,EAAqBC,KAAK,EAAEC,KAAK,EAAEC,UAAU,CAAC;AACzE;AAAC,IAAAM,QAAA,GAEcX,QAAQ;AAAAV,OAAA,CAAAsB,OAAA,GAAAD,QAAA;AAEvBX,QAAQ,CAACZ,QAAQ,GAAGA,QAAQ;AAC5BY,QAAQ,CAACa,MAAM,GAAGzB,QAAQ,CAACyB,MAAM;AACjCb,QAAQ,CAACS,OAAO,GAAGrB,QAAQ,CAACqB,OAAO;AAEnCT,QAAQ,CAACc,KAAK,GAAG,UAAUC,IAAY,EAAEC,KAA6B,EAAE;EACtEjB,YAAY,CAACgB,IAAI,EAAEC,KAAK,CAAC;EACzB;AACF,CAAC;AAEDhB,QAAQ,CAACe,IAAI,GAAG,UACdA,IAAY,EACZb,IAA6B,EAC7BC,KAAa,EACbC,KAAW,EACXa,IAAe,EACfC,QAAkC,EAClC;EACA,IAAAR,0BAAY,EAACK,IAAI,EAAEb,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEa,IAAI,EAAEC,QAAQ,CAAC;AAExD,CAAC;AAEDlB,QAAQ,CAACmB,SAAS,GAAG,UAAUJ,IAAY,EAAEb,IAA8B,EAAE;EAC3EJ,gBAAgB,CAACiB,IAAI,EAAEb,IAAI,CAAC;EAE5BV,KAAK,CAACyB,IAAI,CAACG,MAAM,CAACL,IAAI,CAAC;AACzB,CAAC;AAEDf,QAAQ,CAACF,gBAAgB,GAAG,UAC1BuB,IAAY,EACZnB,IAA8B,EAC9B;EACAH,YAAY,CAACsB,IAAI,EAAErB,QAAQ,CAACmB,SAAS,EAAEjB,IAAI,CAAC;EAC5C,OAAOmB,IAAI;AACb,CAAC;AAMD,SAASC,iBAAiBA,CAACL,IAAc,EAAEb,KAA6B,EAAE;EACxE,IAAIa,IAAI,CAACF,IAAI,CAACR,IAAI,KAAKH,KAAK,CAACG,IAAI,EAAE;IACjCH,KAAK,CAACmB,GAAG,GAAG,IAAI;IAChBN,IAAI,CAACO,IAAI,CAAC,CAAC;EACb;AACF;AAEAxB,QAAQ,CAACyB,OAAO,GAAG,UACjBJ,IAAY,EACZd,IAAoB,EACpBmB,aAA6B,EACpB;EAET,IAAIA,aAAa,YAAbA,aAAa,CAAEC,QAAQ,CAACN,IAAI,CAACd,IAAI,CAAC,EAAE,OAAO,KAAK;EAGpD,IAAIc,IAAI,CAACd,IAAI,KAAKA,IAAI,EAAE,OAAO,IAAI;EAEnC,MAAMH,KAA6B,GAAG;IACpCmB,GAAG,EAAE,KAAK;IACVhB,IAAI,EAAEA;EACR,CAAC;EAEDP,QAAQ,CACNqB,IAAI,EACJ;IACEf,OAAO,EAAE,IAAI;IACbsB,QAAQ,EAAEF,aAAa;IACvBV,KAAK,EAAEM;EACT,CAAC,EACD,IAAI,EACJlB,KACF,CAAC;EAED,OAAOA,KAAK,CAACmB,GAAG;AAClB,CAAC;AAEDvB,QAAQ,CAACR,KAAK,GAAGA,KAAK"}