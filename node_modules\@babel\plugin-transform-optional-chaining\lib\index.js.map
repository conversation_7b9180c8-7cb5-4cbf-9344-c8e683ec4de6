{"version": 3, "file": "index.js", "sources": ["../src/util.ts", "../src/transform.ts", "../src/index.ts"], "sourcesContent": ["import type { NodePath } from \"@babel/traverse\";\nimport { isTransparentExprWrapper } from \"@babel/helper-skip-transparent-expression-wrappers\";\n/**\n * Test if a NodePath will be cast to boolean when evaluated.\n * It respects transparent expression wrappers defined in\n * \"@babel/helper-skip-transparent-expression-wrappers\"\n *\n * @example\n * // returns true\n * const nodePathADotB = NodePath(\"if (a.b) {}\").get(\"test\"); // a.b\n * willPathCastToBoolean(nodePathADotB)\n * @example\n * // returns false\n * willPathCastToBoolean(NodePath(\"a.b\"))\n * @param {NodePath} path\n * @returns {boolean}\n */\nexport function willPathCastToBoolean(path: NodePath): boolean {\n  const maybeWrapped = findOutermostTransparentParent(path);\n  const { node, parentPath } = maybeWrapped;\n  if (parentPath.isLogicalExpression()) {\n    const { operator, right } = parentPath.node;\n    if (\n      operator === \"&&\" ||\n      operator === \"||\" ||\n      (operator === \"??\" && node === right)\n    ) {\n      return willPathCastToBoolean(parentPath);\n    }\n  }\n  if (parentPath.isSequenceExpression()) {\n    const { expressions } = parentPath.node;\n    if (expressions[expressions.length - 1] === node) {\n      return willPathCastToBoolean(parentPath);\n    } else {\n      // if it is in the middle of a sequence expression, we don't\n      // care the return value so just cast to boolean for smaller\n      // output\n      return true;\n    }\n  }\n  return (\n    parentPath.isConditional({ test: node }) ||\n    parentPath.isUnaryExpression({ operator: \"!\" }) ||\n    parentPath.isLoop({ test: node })\n  );\n}\n\n/**\n * Return the outermost transparent expression wrapper of a given path,\n * otherwise returns path itself.\n * @example\n * const nodePathADotB = NodePath(\"(a.b as any)\").get(\"expression\"); // a.b\n * // returns NodePath(\"(a.b as any)\")\n * findOutermostTransparentParent(nodePathADotB);\n * @param {NodePath} path\n * @returns {NodePath}\n */\nexport function findOutermostTransparentParent(path: NodePath): NodePath {\n  let maybeWrapped = path;\n  path.findParent(p => {\n    if (!isTransparentExprWrapper(p.node)) return true;\n    maybeWrapped = p;\n  });\n  return maybeWrapped;\n}\n", "import { types as t, template } from \"@babel/core\";\nimport type { NodePath } from \"@babel/traverse\";\nimport {\n  skipTransparentExprWrapperNodes,\n  skipTransparentExprWrappers,\n} from \"@babel/helper-skip-transparent-expression-wrappers\";\nimport { willPathCastToBoolean, findOutermostTransparentParent } from \"./util\";\n\nconst { ast } = template.expression;\n\nfunction isSimpleMemberExpression(\n  expression: t.Expression | t.Super,\n): expression is t.Identifier | t.Super | t.MemberExpression {\n  expression = skipTransparentExprWrapperNodes(expression);\n  return (\n    t.isIdentifier(expression) ||\n    t.isSuper(expression) ||\n    (t.isMemberExpression(expression) &&\n      !expression.computed &&\n      isSimpleMemberExpression(expression.object))\n  );\n}\n\n/**\n * Test if a given optional chain `path` needs to be memoized\n * @param {NodePath} path\n * @returns {boolean}\n */\nfunction needsMemoize(\n  path: NodePath<t.OptionalCallExpression | t.OptionalMemberExpression>,\n) {\n  let optionalPath: NodePath = path;\n  const { scope } = path;\n  while (\n    optionalPath.isOptionalMemberExpression() ||\n    optionalPath.isOptionalCallExpression()\n  ) {\n    const { node } = optionalPath;\n    const childPath = skipTransparentExprWrappers(\n      // @ts-expect-error isOptionalMemberExpression does not work with NodePath union\n      optionalPath.isOptionalMemberExpression()\n        ? optionalPath.get(\"object\")\n        : optionalPath.get(\"callee\"),\n    );\n    if (node.optional) {\n      return !scope.isStatic(childPath.node);\n    }\n\n    optionalPath = childPath;\n  }\n}\n\nexport function transform(\n  path: NodePath<t.OptionalCallExpression | t.OptionalMemberExpression>,\n  {\n    pureGetters,\n    noDocumentAll,\n  }: { pureGetters: boolean; noDocumentAll: boolean },\n) {\n  const { scope } = path;\n  // maybeWrapped points to the outermost transparent expression wrapper\n  // or the path itself\n  const maybeWrapped = findOutermostTransparentParent(path);\n  const { parentPath } = maybeWrapped;\n  const willReplacementCastToBoolean = willPathCastToBoolean(maybeWrapped);\n  let isDeleteOperation = false;\n  const parentIsCall =\n    parentPath.isCallExpression({ callee: maybeWrapped.node }) &&\n    // note that the first condition must implies that `path.optional` is `true`,\n    // otherwise the parentPath should be an OptionalCallExpression\n    path.isOptionalMemberExpression();\n\n  const optionals = [];\n\n  let optionalPath = path;\n  // Replace `function (a, x = a.b?.c) {}` to `function (a, x = (() => a.b?.c)() ){}`\n  // so the temporary variable can be injected in correct scope\n  if (scope.path.isPattern() && needsMemoize(optionalPath)) {\n    path.replaceWith(template.ast`(() => ${path.node})()` as t.Statement);\n    // The injected optional chain will be queued and eventually transformed when visited\n    return;\n  }\n  while (\n    optionalPath.isOptionalMemberExpression() ||\n    optionalPath.isOptionalCallExpression()\n  ) {\n    const { node } = optionalPath;\n    if (node.optional) {\n      optionals.push(node);\n    }\n    // @ts-expect-error isOptionalMemberExpression does not work with NodePath union\n    if (optionalPath.isOptionalMemberExpression()) {\n      // @ts-expect-error todo(flow->ts) avoid changing more type\n      optionalPath.node.type = \"MemberExpression\";\n      // @ts-expect-error todo(flow->ts)\n      optionalPath = skipTransparentExprWrappers(optionalPath.get(\"object\"));\n    } else if (optionalPath.isOptionalCallExpression()) {\n      // @ts-expect-error todo(flow->ts) avoid changing more type\n      optionalPath.node.type = \"CallExpression\";\n      // @ts-expect-error todo(flow->ts)\n      optionalPath = skipTransparentExprWrappers(optionalPath.get(\"callee\"));\n    }\n  }\n\n  // todo: Improve replacementPath typings\n  let replacementPath: NodePath<any> = path;\n  if (parentPath.isUnaryExpression({ operator: \"delete\" })) {\n    replacementPath = parentPath;\n    isDeleteOperation = true;\n  }\n  for (let i = optionals.length - 1; i >= 0; i--) {\n    const node = optionals[i] as unknown as\n      | t.MemberExpression\n      | t.CallExpression;\n\n    const isCall = t.isCallExpression(node);\n\n    const chainWithTypes = isCall\n      ? // V8 intrinsics must not be an optional call\n        (node.callee as t.Expression)\n      : node.object;\n    const chain = skipTransparentExprWrapperNodes(chainWithTypes);\n\n    let ref;\n    let check;\n    if (isCall && t.isIdentifier(chain, { name: \"eval\" })) {\n      check = ref = chain;\n      // `eval?.()` is an indirect eval call transformed to `(0,eval)()`\n      node.callee = t.sequenceExpression([t.numericLiteral(0), ref]);\n    } else if (pureGetters && isCall && isSimpleMemberExpression(chain)) {\n      // If we assume getters are pure (avoiding a Function#call) and we are at the call,\n      // we can avoid a needless memoize. We only do this if the callee is a simple member\n      // expression, to avoid multiple calls to nested call expressions.\n      check = ref = node.callee;\n    } else {\n      ref = scope.maybeGenerateMemoised(chain);\n      if (ref) {\n        check = t.assignmentExpression(\n          \"=\",\n          t.cloneNode(ref),\n          // Here `chainWithTypes` MUST NOT be cloned because it could be\n          // updated when generating the memoised context of a call\n          // expression. It must be an Expression when `ref` is an identifier\n          // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n          chainWithTypes as t.Expression,\n        );\n\n        isCall ? (node.callee = ref) : (node.object = ref);\n      } else {\n        check = ref = chainWithTypes;\n      }\n    }\n\n    // Ensure call expressions have the proper `this`\n    // `foo.bar()` has context `foo`.\n    if (isCall && t.isMemberExpression(chain)) {\n      if (pureGetters && isSimpleMemberExpression(chain)) {\n        // To avoid a Function#call, we can instead re-grab the property from the context object.\n        // `a.?b.?()` translates roughly to `_a.b != null && _a.b()`\n        node.callee = chainWithTypes;\n      } else {\n        // Otherwise, we need to memoize the context object, and change the call into a Function#call.\n        // `a.?b.?()` translates roughly to `(_b = _a.b) != null && _b.call(_a)`\n        const { object } = chain;\n        let context: t.Expression;\n        if (t.isSuper(object)) {\n          context = t.thisExpression();\n        } else {\n          const memoized = scope.maybeGenerateMemoised(object);\n          if (memoized) {\n            context = memoized;\n            chain.object = t.assignmentExpression(\"=\", memoized, object);\n          } else {\n            context = object;\n          }\n        }\n\n        node.arguments.unshift(t.cloneNode(context));\n        // @ts-expect-error node.callee can not be an V8IntrinsicIdentifier: V8 intrinsic is disallowed in optional chain\n        node.callee = t.memberExpression(node.callee, t.identifier(\"call\"));\n      }\n    }\n    let replacement = replacementPath.node;\n    // Ensure (a?.b)() has proper `this`\n    // The `parentIsCall` is constant within loop, we should check i === 0\n    // to ensure that it is only applied to the first optional chain element\n    // i.e. `?.b` in `(a?.b.c)()`\n    if (i === 0 && parentIsCall) {\n      // `(a?.b)()` to `(a == null ? undefined : a.b.bind(a))()`\n      // object must not be Super as super?.foo is invalid\n      const object = skipTransparentExprWrapperNodes(\n        replacement.object,\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n      ) as any as t.Expression;\n      let baseRef;\n      if (!pureGetters || !isSimpleMemberExpression(object)) {\n        // memoize the context object when getters are not always pure\n        // or the object is not a simple member expression\n        // `(a?.b.c)()` to `(a == null ? undefined : (_a$b = a.b).c.bind(_a$b))()`\n        baseRef = scope.maybeGenerateMemoised(object);\n        if (baseRef) {\n          replacement.object = t.assignmentExpression(\"=\", baseRef, object);\n        }\n      }\n      replacement = t.callExpression(\n        t.memberExpression(replacement, t.identifier(\"bind\")),\n        [t.cloneNode(baseRef ?? object)],\n      );\n    }\n\n    if (willReplacementCastToBoolean) {\n      // `if (a?.b) {}` transformed to `if (a != null && a.b) {}`\n      // we don't need to return `void 0` because the returned value will\n      // eventually cast to boolean.\n      const nonNullishCheck = noDocumentAll\n        ? ast`${t.cloneNode(check)} != null`\n        : ast`\n            ${t.cloneNode(check)} !== null && ${t.cloneNode(ref)} !== void 0`;\n      replacementPath.replaceWith(\n        t.logicalExpression(\"&&\", nonNullishCheck, replacement),\n      );\n      replacementPath = skipTransparentExprWrappers(\n        // @ts-expect-error todo(flow->ts)\n        replacementPath.get(\"right\"),\n      );\n    } else {\n      const nullishCheck = noDocumentAll\n        ? ast`${t.cloneNode(check)} == null`\n        : ast`\n            ${t.cloneNode(check)} === null || ${t.cloneNode(ref)} === void 0`;\n\n      const returnValue = isDeleteOperation ? ast`true` : ast`void 0`;\n      replacementPath.replaceWith(\n        t.conditionalExpression(nullishCheck, returnValue, replacement),\n      );\n      replacementPath = skipTransparentExprWrappers(\n        // @ts-expect-error todo(flow->ts)\n        replacementPath.get(\"alternate\"),\n      );\n    }\n  }\n}\n", "import { declare } from \"@babel/helper-plugin-utils\";\nimport syntaxOptionalChaining from \"@babel/plugin-syntax-optional-chaining\";\nimport { transform } from \"./transform\";\nimport type { NodePath } from \"@babel/traverse\";\nimport type * as t from \"@babel/types\";\n\nexport interface Options {\n  loose?: boolean;\n}\nexport default declare((api, options: Options) => {\n  api.assertVersion(7);\n\n  const { loose = false } = options;\n  const noDocumentAll = api.assumption(\"noDocumentAll\") ?? loose;\n  const pureGetters = api.assumption(\"pureGetters\") ?? loose;\n\n  return {\n    name: \"transform-optional-chaining\",\n    inherits: syntaxOptionalChaining.default,\n\n    visitor: {\n      \"OptionalCallExpression|OptionalMemberExpression\"(\n        path: NodePath<t.OptionalCallExpression | t.OptionalMemberExpression>,\n      ) {\n        transform(path, { noDocumentAll, pureGetters });\n      },\n    },\n  };\n});\n\nexport { transform };\n"], "names": ["willPathCastToBoolean", "path", "maybeWrapped", "findOutermostTransparentParent", "node", "parentPath", "isLogicalExpression", "operator", "right", "isSequenceExpression", "expressions", "length", "isConditional", "test", "isUnaryExpression", "isLoop", "findParent", "p", "isTransparentExprWrapper", "ast", "template", "expression", "isSimpleMemberExpression", "skipTransparentExprWrapperNodes", "t", "isIdentifier", "is<PERSON><PERSON><PERSON>", "isMemberExpression", "computed", "object", "needsMemoize", "optionalPath", "scope", "isOptionalMemberExpression", "isOptionalCallExpression", "child<PERSON><PERSON>", "skipTransparentExprWrappers", "get", "optional", "isStatic", "transform", "pureGetters", "noDocumentAll", "willReplacementCastToBoolean", "isDeleteOperation", "parentIsCall", "isCallExpression", "callee", "optionals", "isPattern", "replaceWith", "push", "type", "<PERSON><PERSON><PERSON>", "i", "isCall", "chainWithTypes", "chain", "ref", "check", "name", "sequenceExpression", "numericLiteral", "maybeGenerateMemoised", "assignmentExpression", "cloneNode", "context", "thisExpression", "memoized", "arguments", "unshift", "memberExpression", "identifier", "replacement", "_baseRef", "baseRef", "callExpression", "nonNullish<PERSON><PERSON>ck", "logicalExpression", "nullish<PERSON><PERSON><PERSON>", "returnValue", "conditionalExpression", "declare", "api", "options", "_api$assumption", "_api$assumption2", "assertVersion", "loose", "assumption", "inherits", "syntaxOptionalChaining", "default", "visitor", "OptionalCallExpression|OptionalMemberExpression"], "mappings": ";;;;;;;;;AAiBO,SAASA,qBAAqBA,CAACC,IAAc,EAAW;AAC7D,EAAA,MAAMC,YAAY,GAAGC,8BAA8B,CAACF,IAAI,CAAC,CAAA;EACzD,MAAM;IAAEG,IAAI;AAAEC,IAAAA,UAAAA;AAAW,GAAC,GAAGH,YAAY,CAAA;AACzC,EAAA,IAAIG,UAAU,CAACC,mBAAmB,EAAE,EAAE;IACpC,MAAM;MAAEC,QAAQ;AAAEC,MAAAA,KAAAA;KAAO,GAAGH,UAAU,CAACD,IAAI,CAAA;AAC3C,IAAA,IACEG,QAAQ,KAAK,IAAI,IACjBA,QAAQ,KAAK,IAAI,IAChBA,QAAQ,KAAK,IAAI,IAAIH,IAAI,KAAKI,KAAM,EACrC;MACA,OAAOR,qBAAqB,CAACK,UAAU,CAAC,CAAA;AAC1C,KAAA;AACF,GAAA;AACA,EAAA,IAAIA,UAAU,CAACI,oBAAoB,EAAE,EAAE;IACrC,MAAM;AAAEC,MAAAA,WAAAA;KAAa,GAAGL,UAAU,CAACD,IAAI,CAAA;IACvC,IAAIM,WAAW,CAACA,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC,KAAKP,IAAI,EAAE;MAChD,OAAOJ,qBAAqB,CAACK,UAAU,CAAC,CAAA;AAC1C,KAAC,MAAM;AAIL,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AACF,GAAA;EACA,OACEA,UAAU,CAACO,aAAa,CAAC;AAAEC,IAAAA,IAAI,EAAET,IAAAA;AAAK,GAAC,CAAC,IACxCC,UAAU,CAACS,iBAAiB,CAAC;AAAEP,IAAAA,QAAQ,EAAE,GAAA;AAAI,GAAC,CAAC,IAC/CF,UAAU,CAACU,MAAM,CAAC;AAAEF,IAAAA,IAAI,EAAET,IAAAA;AAAK,GAAC,CAAC,CAAA;AAErC,CAAA;AAYO,SAASD,8BAA8BA,CAACF,IAAc,EAAY;EACvE,IAAIC,YAAY,GAAGD,IAAI,CAAA;AACvBA,EAAAA,IAAI,CAACe,UAAU,CAACC,CAAC,IAAI;IACnB,IAAI,CAACC,gEAAwB,CAACD,CAAC,CAACb,IAAI,CAAC,EAAE,OAAO,IAAI,CAAA;AAClDF,IAAAA,YAAY,GAAGe,CAAC,CAAA;AAClB,GAAC,CAAC,CAAA;AACF,EAAA,OAAOf,YAAY,CAAA;AACrB;;ACzDA,MAAM;AAAEiB,EAAAA,GAAAA;AAAI,CAAC,GAAGC,aAAQ,CAACC,UAAU,CAAA;AAEnC,SAASC,wBAAwBA,CAC/BD,UAAkC,EACyB;AAC3DA,EAAAA,UAAU,GAAGE,uEAA+B,CAACF,UAAU,CAAC,CAAA;AACxD,EAAA,OACEG,UAAC,CAACC,YAAY,CAACJ,UAAU,CAAC,IAC1BG,UAAC,CAACE,OAAO,CAACL,UAAU,CAAC,IACpBG,UAAC,CAACG,kBAAkB,CAACN,UAAU,CAAC,IAC/B,CAACA,UAAU,CAACO,QAAQ,IACpBN,wBAAwB,CAACD,UAAU,CAACQ,MAAM,CAAE,CAAA;AAElD,CAAA;AAOA,SAASC,YAAYA,CACnB7B,IAAqE,EACrE;EACA,IAAI8B,YAAsB,GAAG9B,IAAI,CAAA;EACjC,MAAM;AAAE+B,IAAAA,KAAAA;AAAM,GAAC,GAAG/B,IAAI,CAAA;EACtB,OACE8B,YAAY,CAACE,0BAA0B,EAAE,IACzCF,YAAY,CAACG,wBAAwB,EAAE,EACvC;IACA,MAAM;AAAE9B,MAAAA,IAAAA;AAAK,KAAC,GAAG2B,YAAY,CAAA;IAC7B,MAAMI,SAAS,GAAGC,mEAA2B,CAE3CL,YAAY,CAACE,0BAA0B,EAAE,GACrCF,YAAY,CAACM,GAAG,CAAC,QAAQ,CAAC,GAC1BN,YAAY,CAACM,GAAG,CAAC,QAAQ,CAC/B,CAAC,CAAA;IACD,IAAIjC,IAAI,CAACkC,QAAQ,EAAE;MACjB,OAAO,CAACN,KAAK,CAACO,QAAQ,CAACJ,SAAS,CAAC/B,IAAI,CAAC,CAAA;AACxC,KAAA;AAEA2B,IAAAA,YAAY,GAAGI,SAAS,CAAA;AAC1B,GAAA;AACF,CAAA;AAEO,SAASK,SAASA,CACvBvC,IAAqE,EACrE;EACEwC,WAAW;AACXC,EAAAA,aAAAA;AACgD,CAAC,EACnD;EACA,MAAM;AAAEV,IAAAA,KAAAA;AAAM,GAAC,GAAG/B,IAAI,CAAA;AAGtB,EAAA,MAAMC,YAAY,GAAGC,8BAA8B,CAACF,IAAI,CAAC,CAAA;EACzD,MAAM;AAAEI,IAAAA,UAAAA;AAAW,GAAC,GAAGH,YAAY,CAAA;AACnC,EAAA,MAAMyC,4BAA4B,GAAG3C,qBAAqB,CAACE,YAAY,CAAC,CAAA;EACxE,IAAI0C,iBAAiB,GAAG,KAAK,CAAA;AAC7B,EAAA,MAAMC,YAAY,GAChBxC,UAAU,CAACyC,gBAAgB,CAAC;IAAEC,MAAM,EAAE7C,YAAY,CAACE,IAAAA;AAAK,GAAC,CAAC,IAG1DH,IAAI,CAACgC,0BAA0B,EAAE,CAAA;EAEnC,MAAMe,SAAS,GAAG,EAAE,CAAA;EAEpB,IAAIjB,YAAY,GAAG9B,IAAI,CAAA;AAGvB,EAAA,IAAI+B,KAAK,CAAC/B,IAAI,CAACgD,SAAS,EAAE,IAAInB,YAAY,CAACC,YAAY,CAAC,EAAE;IACxD9B,IAAI,CAACiD,WAAW,CAAC9B,aAAQ,CAACD,GAAI,CAAA,OAAA,EAASlB,IAAI,CAACG,IAAK,CAAA,GAAA,CAAmB,CAAC,CAAA;AAErE,IAAA,OAAA;AACF,GAAA;EACA,OACE2B,YAAY,CAACE,0BAA0B,EAAE,IACzCF,YAAY,CAACG,wBAAwB,EAAE,EACvC;IACA,MAAM;AAAE9B,MAAAA,IAAAA;AAAK,KAAC,GAAG2B,YAAY,CAAA;IAC7B,IAAI3B,IAAI,CAACkC,QAAQ,EAAE;AACjBU,MAAAA,SAAS,CAACG,IAAI,CAAC/C,IAAI,CAAC,CAAA;AACtB,KAAA;AAEA,IAAA,IAAI2B,YAAY,CAACE,0BAA0B,EAAE,EAAE;AAE7CF,MAAAA,YAAY,CAAC3B,IAAI,CAACgD,IAAI,GAAG,kBAAkB,CAAA;MAE3CrB,YAAY,GAAGK,mEAA2B,CAACL,YAAY,CAACM,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAA;AACxE,KAAC,MAAM,IAAIN,YAAY,CAACG,wBAAwB,EAAE,EAAE;AAElDH,MAAAA,YAAY,CAAC3B,IAAI,CAACgD,IAAI,GAAG,gBAAgB,CAAA;MAEzCrB,YAAY,GAAGK,mEAA2B,CAACL,YAAY,CAACM,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAA;AACxE,KAAA;AACF,GAAA;EAGA,IAAIgB,eAA8B,GAAGpD,IAAI,CAAA;EACzC,IAAII,UAAU,CAACS,iBAAiB,CAAC;AAAEP,IAAAA,QAAQ,EAAE,QAAA;AAAS,GAAC,CAAC,EAAE;AACxD8C,IAAAA,eAAe,GAAGhD,UAAU,CAAA;AAC5BuC,IAAAA,iBAAiB,GAAG,IAAI,CAAA;AAC1B,GAAA;AACA,EAAA,KAAK,IAAIU,CAAC,GAAGN,SAAS,CAACrC,MAAM,GAAG,CAAC,EAAE2C,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;AAC9C,IAAA,MAAMlD,IAAI,GAAG4C,SAAS,CAACM,CAAC,CAEJ,CAAA;AAEpB,IAAA,MAAMC,MAAM,GAAG/B,UAAC,CAACsB,gBAAgB,CAAC1C,IAAI,CAAC,CAAA;IAEvC,MAAMoD,cAAc,GAAGD,MAAM,GAExBnD,IAAI,CAAC2C,MAAM,GACZ3C,IAAI,CAACyB,MAAM,CAAA;AACf,IAAA,MAAM4B,KAAK,GAAGlC,uEAA+B,CAACiC,cAAc,CAAC,CAAA;AAE7D,IAAA,IAAIE,GAAG,CAAA;AACP,IAAA,IAAIC,KAAK,CAAA;AACT,IAAA,IAAIJ,MAAM,IAAI/B,UAAC,CAACC,YAAY,CAACgC,KAAK,EAAE;AAAEG,MAAAA,IAAI,EAAE,MAAA;AAAO,KAAC,CAAC,EAAE;MACrDD,KAAK,GAAGD,GAAG,GAAGD,KAAK,CAAA;AAEnBrD,MAAAA,IAAI,CAAC2C,MAAM,GAAGvB,UAAC,CAACqC,kBAAkB,CAAC,CAACrC,UAAC,CAACsC,cAAc,CAAC,CAAC,CAAC,EAAEJ,GAAG,CAAC,CAAC,CAAA;KAC/D,MAAM,IAAIjB,WAAW,IAAIc,MAAM,IAAIjC,wBAAwB,CAACmC,KAAK,CAAC,EAAE;AAInEE,MAAAA,KAAK,GAAGD,GAAG,GAAGtD,IAAI,CAAC2C,MAAM,CAAA;AAC3B,KAAC,MAAM;AACLW,MAAAA,GAAG,GAAG1B,KAAK,CAAC+B,qBAAqB,CAACN,KAAK,CAAC,CAAA;AACxC,MAAA,IAAIC,GAAG,EAAE;AACPC,QAAAA,KAAK,GAAGnC,UAAC,CAACwC,oBAAoB,CAC5B,GAAG,EACHxC,UAAC,CAACyC,SAAS,CAACP,GAAG,CAAC,EAKhBF,cACF,CAAC,CAAA;QAEDD,MAAM,GAAInD,IAAI,CAAC2C,MAAM,GAAGW,GAAG,GAAKtD,IAAI,CAACyB,MAAM,GAAG6B,GAAI,CAAA;AACpD,OAAC,MAAM;QACLC,KAAK,GAAGD,GAAG,GAAGF,cAAc,CAAA;AAC9B,OAAA;AACF,KAAA;IAIA,IAAID,MAAM,IAAI/B,UAAC,CAACG,kBAAkB,CAAC8B,KAAK,CAAC,EAAE;AACzC,MAAA,IAAIhB,WAAW,IAAInB,wBAAwB,CAACmC,KAAK,CAAC,EAAE;QAGlDrD,IAAI,CAAC2C,MAAM,GAAGS,cAAc,CAAA;AAC9B,OAAC,MAAM;QAGL,MAAM;AAAE3B,UAAAA,MAAAA;AAAO,SAAC,GAAG4B,KAAK,CAAA;AACxB,QAAA,IAAIS,OAAqB,CAAA;AACzB,QAAA,IAAI1C,UAAC,CAACE,OAAO,CAACG,MAAM,CAAC,EAAE;AACrBqC,UAAAA,OAAO,GAAG1C,UAAC,CAAC2C,cAAc,EAAE,CAAA;AAC9B,SAAC,MAAM;AACL,UAAA,MAAMC,QAAQ,GAAGpC,KAAK,CAAC+B,qBAAqB,CAAClC,MAAM,CAAC,CAAA;AACpD,UAAA,IAAIuC,QAAQ,EAAE;AACZF,YAAAA,OAAO,GAAGE,QAAQ,CAAA;AAClBX,YAAAA,KAAK,CAAC5B,MAAM,GAAGL,UAAC,CAACwC,oBAAoB,CAAC,GAAG,EAAEI,QAAQ,EAAEvC,MAAM,CAAC,CAAA;AAC9D,WAAC,MAAM;AACLqC,YAAAA,OAAO,GAAGrC,MAAM,CAAA;AAClB,WAAA;AACF,SAAA;QAEAzB,IAAI,CAACiE,SAAS,CAACC,OAAO,CAAC9C,UAAC,CAACyC,SAAS,CAACC,OAAO,CAAC,CAAC,CAAA;AAE5C9D,QAAAA,IAAI,CAAC2C,MAAM,GAAGvB,UAAC,CAAC+C,gBAAgB,CAACnE,IAAI,CAAC2C,MAAM,EAAEvB,UAAC,CAACgD,UAAU,CAAC,MAAM,CAAC,CAAC,CAAA;AACrE,OAAA;AACF,KAAA;AACA,IAAA,IAAIC,WAAW,GAAGpB,eAAe,CAACjD,IAAI,CAAA;AAKtC,IAAA,IAAIkD,CAAC,KAAK,CAAC,IAAIT,YAAY,EAAE;AAAA,MAAA,IAAA6B,QAAA,CAAA;AAG3B,MAAA,MAAM7C,MAAM,GAAGN,uEAA+B,CAC5CkD,WAAW,CAAC5C,MAEd,CAAwB,CAAA;AACxB,MAAA,IAAI8C,OAAO,CAAA;MACX,IAAI,CAAClC,WAAW,IAAI,CAACnB,wBAAwB,CAACO,MAAM,CAAC,EAAE;AAIrD8C,QAAAA,OAAO,GAAG3C,KAAK,CAAC+B,qBAAqB,CAAClC,MAAM,CAAC,CAAA;AAC7C,QAAA,IAAI8C,OAAO,EAAE;AACXF,UAAAA,WAAW,CAAC5C,MAAM,GAAGL,UAAC,CAACwC,oBAAoB,CAAC,GAAG,EAAEW,OAAO,EAAE9C,MAAM,CAAC,CAAA;AACnE,SAAA;AACF,OAAA;AACA4C,MAAAA,WAAW,GAAGjD,UAAC,CAACoD,cAAc,CAC5BpD,UAAC,CAAC+C,gBAAgB,CAACE,WAAW,EAAEjD,UAAC,CAACgD,UAAU,CAAC,MAAM,CAAC,CAAC,EACrD,CAAChD,UAAC,CAACyC,SAAS,CAAAS,CAAAA,QAAA,GAACC,OAAO,YAAAD,QAAA,GAAI7C,MAAM,CAAC,CACjC,CAAC,CAAA;AACH,KAAA;AAEA,IAAA,IAAIc,4BAA4B,EAAE;AAIhC,MAAA,MAAMkC,eAAe,GAAGnC,aAAa,GACjCvB,GAAI,CAAEK,EAAAA,UAAC,CAACyC,SAAS,CAACN,KAAK,CAAE,CAAA,QAAA,CAAS,GAClCxC,GAAI,CAAA;AACd,YAAA,EAAcK,UAAC,CAACyC,SAAS,CAACN,KAAK,CAAE,CAAenC,aAAAA,EAAAA,UAAC,CAACyC,SAAS,CAACP,GAAG,CAAE,CAAY,WAAA,CAAA,CAAA;AACvEL,MAAAA,eAAe,CAACH,WAAW,CACzB1B,UAAC,CAACsD,iBAAiB,CAAC,IAAI,EAAED,eAAe,EAAEJ,WAAW,CACxD,CAAC,CAAA;MACDpB,eAAe,GAAGjB,mEAA2B,CAE3CiB,eAAe,CAAChB,GAAG,CAAC,OAAO,CAC7B,CAAC,CAAA;AACH,KAAC,MAAM;AACL,MAAA,MAAM0C,YAAY,GAAGrC,aAAa,GAC9BvB,GAAI,CAAEK,EAAAA,UAAC,CAACyC,SAAS,CAACN,KAAK,CAAE,CAAA,QAAA,CAAS,GAClCxC,GAAI,CAAA;AACd,YAAA,EAAcK,UAAC,CAACyC,SAAS,CAACN,KAAK,CAAE,CAAenC,aAAAA,EAAAA,UAAC,CAACyC,SAAS,CAACP,GAAG,CAAE,CAAY,WAAA,CAAA,CAAA;MAEvE,MAAMsB,WAAW,GAAGpC,iBAAiB,GAAGzB,GAAI,CAAK,IAAA,CAAA,GAAGA,GAAI,CAAO,MAAA,CAAA,CAAA;AAC/DkC,MAAAA,eAAe,CAACH,WAAW,CACzB1B,UAAC,CAACyD,qBAAqB,CAACF,YAAY,EAAEC,WAAW,EAAEP,WAAW,CAChE,CAAC,CAAA;MACDpB,eAAe,GAAGjB,mEAA2B,CAE3CiB,eAAe,CAAChB,GAAG,CAAC,WAAW,CACjC,CAAC,CAAA;AACH,KAAA;AACF,GAAA;AACF;;ACxOA,YAAe6C,yBAAO,CAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,eAAA,EAAAC,gBAAA,CAAA;AAChDH,EAAAA,GAAG,CAACI,aAAa,CAAC,CAAC,CAAC,CAAA;EAEpB,MAAM;AAAEC,IAAAA,KAAK,GAAG,KAAA;AAAM,GAAC,GAAGJ,OAAO,CAAA;AACjC,EAAA,MAAM1C,aAAa,GAAA,CAAA2C,eAAA,GAAGF,GAAG,CAACM,UAAU,CAAC,eAAe,CAAC,KAAAJ,IAAAA,GAAAA,eAAA,GAAIG,KAAK,CAAA;AAC9D,EAAA,MAAM/C,WAAW,GAAA,CAAA6C,gBAAA,GAAGH,GAAG,CAACM,UAAU,CAAC,aAAa,CAAC,KAAAH,IAAAA,GAAAA,gBAAA,GAAIE,KAAK,CAAA;EAE1D,OAAO;AACL5B,IAAAA,IAAI,EAAE,6BAA6B;IACnC8B,QAAQ,EAAEC,sBAAsB,CAACC,OAAO;AAExCC,IAAAA,OAAO,EAAE;MACP,iDAAiDC,CAC/C7F,IAAqE,EACrE;QACAuC,SAAS,CAACvC,IAAI,EAAE;UAAEyC,aAAa;AAAED,UAAAA,WAAAA;AAAY,SAAC,CAAC,CAAA;AACjD,OAAA;AACF,KAAA;GACD,CAAA;AACH,CAAC,CAAC;;;;;"}