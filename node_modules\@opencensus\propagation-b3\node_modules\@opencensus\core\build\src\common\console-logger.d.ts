import * as types from './types';
/**
 * This class implements a console logger.
 */
export declare class ConsoleLogger implements types.Logger {
    private logger;
    static LEVELS: string[];
    level: string;
    /**
     * Constructs a new ConsoleLogger instance
     * @param options A logger configuration object.
     */
    constructor(options?: types.LoggerOptions | string | number);
    /**
     * Logger error function.
     * @param message menssage erro to log in console
     * @param args arguments to log in console
     */
    error(message: any, ...args: any[]): void;
    /**
     * Logger warning function.
     * @param message menssage warning to log in console
     * @param args arguments to log in console
     */
    warn(message: any, ...args: any[]): void;
    /**
     * Logger info function.
     * @param message menssage info to log in console
     * @param args arguments to log in console
     */
    info(message: any, ...args: any[]): void;
    /**
     * Logger debug function.
     * @param message menssage debug to log in console
     * @param args arguments to log in console
     */
    debug(message: any, ...args: any[]): void;
    /**
     * Logger silly function.
     * @param message menssage silly to log in console
     * @param args arguments to log in console
     */
    silly(message: any, ...args: any[]): void;
}
/**
 * Function logger exported to others classes. Inspired by:
 * https://github.com/cainus/logdriver/blob/bba1761737ca72f04d6b445629848538d038484a/index.js#L50
 * @param options A logger options or strig to logger in console
 */
declare const logger: (options?: string | number | types.LoggerOptions) => types.Logger;
export { logger };
