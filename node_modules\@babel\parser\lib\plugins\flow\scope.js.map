{"version": 3, "names": ["_scope", "require", "_scopeflags", "FlowScope", "<PERSON><PERSON>", "constructor", "args", "declareFunctions", "Set", "FlowScopeHandler", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createScope", "flags", "declareName", "name", "bindingType", "loc", "scope", "currentScope", "BIND_FLAGS_FLOW_DECLARE_FN", "checkRedeclarationInScope", "maybeExportDefined", "add", "isRedeclaredInScope", "has", "lexical", "functions", "checkLocalExport", "id", "scopeStack", "exports", "default"], "sources": ["../../../src/plugins/flow/scope.ts"], "sourcesContent": ["import type { Position } from \"../../util/location\";\nimport <PERSON><PERSON><PERSON><PERSON><PERSON>, { <PERSON>ope } from \"../../util/scope\";\nimport {\n  BIND_FLAGS_FLOW_DECLARE_FN,\n  type ScopeFlags,\n  type BindingTypes,\n} from \"../../util/scopeflags\";\nimport type * as N from \"../../types\";\n\n// Reference implementation: https://github.com/facebook/flow/blob/23aeb2a2ef6eb4241ce178fde5d8f17c5f747fb5/src/typing/env.ml#L536-L584\nclass FlowScope extends Scope {\n  // declare function foo(): type;\n  declareFunctions: Set<string> = new Set();\n}\n\nexport default class FlowScopeHandler extends ScopeHandler<FlowScope> {\n  createScope(flags: ScopeFlags): FlowScope {\n    return new FlowScope(flags);\n  }\n\n  declareName(name: string, bindingType: BindingTypes, loc: Position) {\n    const scope = this.currentScope();\n    if (bindingType & BIND_FLAGS_FLOW_DECLARE_FN) {\n      this.checkRedeclarationInScope(scope, name, bindingType, loc);\n      this.maybeExportDefined(scope, name);\n      scope.declareFunctions.add(name);\n      return;\n    }\n\n    super.declareName(name, bindingType, loc);\n  }\n\n  isRedeclaredInScope(\n    scope: FlowScope,\n    name: string,\n    bindingType: BindingTypes,\n  ): boolean {\n    if (super.isRedeclaredInScope(scope, name, bindingType)) return true;\n\n    if (bindingType & BIND_FLAGS_FLOW_DECLARE_FN) {\n      return (\n        !scope.declareFunctions.has(name) &&\n        (scope.lexical.has(name) || scope.functions.has(name))\n      );\n    }\n\n    return false;\n  }\n\n  checkLocalExport(id: N.Identifier) {\n    if (!this.scopeStack[0].declareFunctions.has(id.name)) {\n      super.checkLocalExport(id);\n    }\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AAQA,MAAME,SAAS,SAASC,YAAK,CAAC;EAAAC,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA,KAE5BC,gBAAgB,GAAgB,IAAIC,GAAG,CAAC,CAAC;EAAA;AAC3C;AAEe,MAAMC,gBAAgB,SAASC,cAAY,CAAY;EACpEC,WAAWA,CAACC,KAAiB,EAAa;IACxC,OAAO,IAAIT,SAAS,CAACS,KAAK,CAAC;EAC7B;EAEAC,WAAWA,CAACC,IAAY,EAAEC,WAAyB,EAAEC,GAAa,EAAE;IAClE,MAAMC,KAAK,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IACjC,IAAIH,WAAW,GAAGI,sCAA0B,EAAE;MAC5C,IAAI,CAACC,yBAAyB,CAACH,KAAK,EAAEH,IAAI,EAAEC,WAAW,EAAEC,GAAG,CAAC;MAC7D,IAAI,CAACK,kBAAkB,CAACJ,KAAK,EAAEH,IAAI,CAAC;MACpCG,KAAK,CAACV,gBAAgB,CAACe,GAAG,CAACR,IAAI,CAAC;MAChC;IACF;IAEA,KAAK,CAACD,WAAW,CAACC,IAAI,EAAEC,WAAW,EAAEC,GAAG,CAAC;EAC3C;EAEAO,mBAAmBA,CACjBN,KAAgB,EAChBH,IAAY,EACZC,WAAyB,EAChB;IACT,IAAI,KAAK,CAACQ,mBAAmB,CAACN,KAAK,EAAEH,IAAI,EAAEC,WAAW,CAAC,EAAE,OAAO,IAAI;IAEpE,IAAIA,WAAW,GAAGI,sCAA0B,EAAE;MAC5C,OACE,CAACF,KAAK,CAACV,gBAAgB,CAACiB,GAAG,CAACV,IAAI,CAAC,KAChCG,KAAK,CAACQ,OAAO,CAACD,GAAG,CAACV,IAAI,CAAC,IAAIG,KAAK,CAACS,SAAS,CAACF,GAAG,CAACV,IAAI,CAAC,CAAC;IAE1D;IAEA,OAAO,KAAK;EACd;EAEAa,gBAAgBA,CAACC,EAAgB,EAAE;IACjC,IAAI,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC,CAACtB,gBAAgB,CAACiB,GAAG,CAACI,EAAE,CAACd,IAAI,CAAC,EAAE;MACrD,KAAK,CAACa,gBAAgB,CAACC,EAAE,CAAC;IAC5B;EACF;AACF;AAACE,OAAA,CAAAC,OAAA,GAAAtB,gBAAA"}