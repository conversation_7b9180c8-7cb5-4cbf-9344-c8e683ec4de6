{"version": 3, "names": ["_helperFunctionName", "require", "_helperReplaceSupers", "_helperEnvironmentVisitor", "_helperOptimiseCallExpression", "_core", "_helperAnnotateAsPure", "_inlineCreateSuperHelpers", "buildConstructor", "classRef", "constructorBody", "node", "func", "t", "functionDeclaration", "cloneNode", "inherits", "transformClass", "path", "file", "builtinClasses", "isLoose", "assumptions", "supportUnicodeId", "classState", "parent", "undefined", "scope", "classId", "superFnId", "superName", "superReturns", "isDerived", "extendsNative", "construct", "userConstructor", "userConstructorPath", "hasConstructor", "body", "superThises", "pushedConstructor", "pushedInherits", "pushedCreateClass", "protoAlias", "dynamic<PERSON>eys", "Map", "methods", "instance", "hasComputed", "list", "map", "static", "setState", "newState", "Object", "assign", "findThisesVisitor", "traverse", "visitors", "merge", "environmentVisitor", "ThisExpression", "push", "createClassHelper", "args", "callExpression", "addHelper", "maybeCreateConstructor", "paths", "get", "equals", "params", "constructor", "template", "expression", "ast", "blockStatement", "unshiftContainer", "classMethod", "identifier", "buildBody", "pushBody", "verifyConstructor", "pushDescriptors", "classBodyPaths", "isClassProperty", "buildCodeFrameError", "decorators", "isClassMethod", "isConstructor", "kind", "replaceSupers", "ReplaceSupers", "methodPath", "objectRef", "superRef", "constant<PERSON>uper", "refToPreserve", "replace", "ReturnStatement", "getFunctionParent", "isArrowFunctionExpression", "pushConstructor", "pushMethod", "pushInheritsToBody", "props", "placement", "length", "desc", "obj", "objectExpression", "objectProperty", "key", "properties", "arrayExpression", "nullLiteral", "lastNonNullIndex", "i", "is<PERSON>ull<PERSON>iteral", "slice", "expressionStatement", "wrapSuperCall", "bareSuper", "thisRef", "bareSuperNode", "call", "superIsCallableConstructor", "arguments", "unshift", "thisExpression", "isSpreadElement", "isIdentifier", "argument", "name", "callee", "memberExpression", "logicalExpression", "optimiseCall", "parentPath", "isExpressionStatement", "container", "assignmentExpression", "replaceWith", "returnStatement", "ref", "generateDeclaredUidIdentifier", "thisPath", "isMemberExpression", "object", "bareSupers", "Super", "isCallExpression", "guaranteedSuperBefore<PERSON>inish", "find", "isLoop", "isConditional", "wrapReturn", "returnArg", "thisExpr", "returnParams", "bodyPaths", "pop", "isReturnStatement", "pushContainer", "returnPath", "processMethod", "<PERSON><PERSON><PERSON><PERSON>", "isNumericLiteral", "isBigIntLiteral", "stringLiteral", "String", "value", "toCom<PERSON><PERSON>ey", "fn", "toExpression", "isStringLiteral", "_nameFunction", "nameFunction", "id", "descriptor", "has", "set", "setClassMethods", "insertProtoAliasOnce", "methodName", "computed", "isLiteral", "functionExpression", "generator", "async", "_nameFunction2", "expr", "inheritsComments", "generateUidIdentifier", "classProto", "protoDeclaration", "variableDeclaration", "variableDeclarator", "method", "directives", "pushConstructorToBody", "hasInstanceDescriptors", "hasStaticDescriptors", "addCreateSuperHelper", "extractDynamicKeys", "elem", "isPure", "generateUidIdentifierBasedOnNode", "setupClosureParamsArgs", "closureParams", "closureArgs", "arg", "annotateAsPure", "param", "classTransformer", "superClass", "hasBinding", "noClassCalls", "isStrict", "isInStrictMode", "constructorOnly", "directive", "directiveLiteral", "arrowFunctionExpression"], "sources": ["../src/transformClass.ts"], "sourcesContent": ["import type { Node<PERSON><PERSON>, Scope, Visitor } from \"@babel/traverse\";\nimport nameFunction from \"@babel/helper-function-name\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport environmentVisitor from \"@babel/helper-environment-visitor\";\nimport optimiseCall from \"@babel/helper-optimise-call-expression\";\nimport { traverse, template, types as t, type File } from \"@babel/core\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\n\nimport addCreateSuperHelper from \"./inline-createSuper-helpers\";\n\ntype ClassAssumptions = {\n  setClassMethods: boolean;\n  constantSuper: boolean;\n  superIsCallableConstructor: boolean;\n  noClassCalls: boolean;\n};\n\ntype ClassConstructor = t.ClassMethod & { kind: \"constructor\" };\n\nfunction buildConstructor(\n  classRef: t.Identifier,\n  constructorBody: t.BlockStatement,\n  node: t.Class,\n) {\n  const func = t.functionDeclaration(\n    t.cloneNode(classRef),\n    [],\n    constructorBody,\n  );\n  t.inherits(func, node);\n  return func;\n}\n\ntype Descriptor = {\n  key: t.Expression;\n  get?: t.Expression | null;\n  set?: t.Expression | null;\n  value?: t.Expression | null;\n  constructor?: t.Expression | null;\n};\n\ntype State = {\n  parent: t.Node;\n  scope: Scope;\n  node: t.Class;\n  path: NodePath<t.Class>;\n  file: File;\n\n  classId: t.Identifier | void;\n  classRef: t.Identifier;\n  superFnId: t.Identifier;\n  superName: t.Expression | null;\n  superReturns: NodePath<t.ReturnStatement>[];\n  isDerived: boolean;\n  extendsNative: boolean;\n\n  construct: t.FunctionDeclaration;\n  constructorBody: t.BlockStatement;\n  userConstructor: ClassConstructor;\n  userConstructorPath: NodePath<ClassConstructor>;\n  hasConstructor: boolean;\n\n  body: t.Statement[];\n  superThises: NodePath<t.ThisExpression>[];\n  pushedConstructor: boolean;\n  pushedInherits: boolean;\n  pushedCreateClass: boolean;\n  protoAlias: t.Identifier | null;\n  isLoose: boolean;\n\n  dynamicKeys: Map<string, t.Expression>;\n\n  methods: {\n    // 'list' is in the same order as the elements appear in the class body.\n    // if there aren't computed keys, we can safely reorder class elements\n    // and use 'map' to merge duplicates.\n    instance: {\n      hasComputed: boolean;\n      list: Descriptor[];\n      map: Map<string, Descriptor>;\n    };\n    static: {\n      hasComputed: boolean;\n      list: Descriptor[];\n      map: Map<string, Descriptor>;\n    };\n  };\n};\n\ntype PropertyInfo = {\n  instance: t.ObjectExpression[] | null;\n  static: t.ObjectExpression[] | null;\n};\n\nexport default function transformClass(\n  path: NodePath<t.Class>,\n  file: File,\n  builtinClasses: ReadonlySet<string>,\n  isLoose: boolean,\n  assumptions: ClassAssumptions,\n  supportUnicodeId: boolean,\n) {\n  const classState: State = {\n    parent: undefined,\n    scope: undefined,\n    node: undefined,\n    path: undefined,\n    file: undefined,\n\n    classId: undefined,\n    classRef: undefined,\n    superFnId: undefined,\n    superName: null,\n    superReturns: [],\n    isDerived: false,\n    extendsNative: false,\n\n    construct: undefined,\n    constructorBody: undefined,\n    userConstructor: undefined,\n    userConstructorPath: undefined,\n    hasConstructor: false,\n\n    body: [],\n    superThises: [],\n    pushedConstructor: false,\n    pushedInherits: false,\n    pushedCreateClass: false,\n    protoAlias: null,\n    isLoose: false,\n\n    dynamicKeys: new Map(),\n\n    methods: {\n      instance: {\n        hasComputed: false,\n        list: [],\n        map: new Map(),\n      },\n      static: {\n        hasComputed: false,\n        list: [],\n        map: new Map(),\n      },\n    },\n  };\n\n  const setState = (newState: Partial<State>) => {\n    Object.assign(classState, newState);\n  };\n\n  const findThisesVisitor = traverse.visitors.merge([\n    environmentVisitor,\n    {\n      ThisExpression(path) {\n        classState.superThises.push(path);\n      },\n    },\n  ]);\n\n  function createClassHelper(args: t.Expression[]) {\n    return t.callExpression(classState.file.addHelper(\"createClass\"), args);\n  }\n\n  /**\n   * Creates a class constructor or bail out if there is none\n   */\n  function maybeCreateConstructor() {\n    let hasConstructor = false;\n    const paths = classState.path.get(\"body.body\");\n    for (const path of paths) {\n      // @ts-expect-error: StaticBlock does not have `kind` property\n      hasConstructor = path.equals(\"kind\", \"constructor\");\n      if (hasConstructor) break;\n    }\n    if (hasConstructor) return;\n\n    let params: t.FunctionExpression[\"params\"], body;\n\n    if (classState.isDerived) {\n      const constructor = template.expression.ast`\n        (function () {\n          super(...arguments);\n        })\n      ` as t.FunctionExpression;\n      params = constructor.params;\n      body = constructor.body;\n    } else {\n      params = [];\n      body = t.blockStatement([]);\n    }\n\n    classState.path\n      .get(\"body\")\n      .unshiftContainer(\n        \"body\",\n        t.classMethod(\"constructor\", t.identifier(\"constructor\"), params, body),\n      );\n  }\n\n  function buildBody() {\n    maybeCreateConstructor();\n    pushBody();\n    verifyConstructor();\n\n    if (classState.userConstructor) {\n      const { constructorBody, userConstructor, construct } = classState;\n\n      constructorBody.body.push(...userConstructor.body.body);\n      t.inherits(construct, userConstructor);\n      t.inherits(constructorBody, userConstructor.body);\n    }\n\n    pushDescriptors();\n  }\n\n  function pushBody() {\n    const classBodyPaths: Array<any> = classState.path.get(\"body.body\");\n\n    for (const path of classBodyPaths) {\n      const node = path.node;\n\n      if (path.isClassProperty()) {\n        throw path.buildCodeFrameError(\"Missing class properties transform.\");\n      }\n\n      if (node.decorators) {\n        throw path.buildCodeFrameError(\n          \"Method has decorators, put the decorator plugin before the classes one.\",\n        );\n      }\n\n      if (t.isClassMethod(node)) {\n        const isConstructor = node.kind === \"constructor\";\n\n        const replaceSupers = new ReplaceSupers({\n          methodPath: path,\n          objectRef: classState.classRef,\n          superRef: classState.superName,\n          constantSuper: assumptions.constantSuper,\n          file: classState.file,\n          refToPreserve: classState.classRef,\n        });\n\n        replaceSupers.replace();\n\n        const superReturns: NodePath<t.ReturnStatement>[] = [];\n        path.traverse(\n          traverse.visitors.merge([\n            environmentVisitor,\n            {\n              ReturnStatement(path) {\n                if (!path.getFunctionParent().isArrowFunctionExpression()) {\n                  superReturns.push(path);\n                }\n              },\n            },\n          ]),\n        );\n\n        if (isConstructor) {\n          pushConstructor(superReturns, node as ClassConstructor, path);\n        } else {\n          pushMethod(node, path);\n        }\n      }\n    }\n  }\n\n  function pushDescriptors() {\n    pushInheritsToBody();\n\n    const { body } = classState;\n\n    const props: PropertyInfo = {\n      instance: null,\n      static: null,\n    };\n\n    for (const placement of [\"static\", \"instance\"] as const) {\n      if (classState.methods[placement].list.length) {\n        props[placement] = classState.methods[placement].list.map(desc => {\n          const obj = t.objectExpression([\n            t.objectProperty(t.identifier(\"key\"), desc.key),\n          ]);\n\n          for (const kind of [\"get\", \"set\", \"value\"] as const) {\n            if (desc[kind] != null) {\n              obj.properties.push(\n                t.objectProperty(t.identifier(kind), desc[kind]),\n              );\n            }\n          }\n\n          return obj;\n        });\n      }\n    }\n\n    if (props.instance || props.static) {\n      let args = [\n        t.cloneNode(classState.classRef), // Constructor\n        props.instance ? t.arrayExpression(props.instance) : t.nullLiteral(), // instanceDescriptors\n        props.static ? t.arrayExpression(props.static) : t.nullLiteral(), // staticDescriptors\n      ];\n\n      let lastNonNullIndex = 0;\n      for (let i = 0; i < args.length; i++) {\n        if (!t.isNullLiteral(args[i])) lastNonNullIndex = i;\n      }\n      args = args.slice(0, lastNonNullIndex + 1);\n\n      body.push(t.expressionStatement(createClassHelper(args)));\n      classState.pushedCreateClass = true;\n    }\n  }\n\n  function wrapSuperCall(\n    bareSuper: NodePath<t.CallExpression>,\n    superRef: t.Expression,\n    thisRef: () => t.Identifier,\n    body: NodePath<t.BlockStatement>,\n  ) {\n    const bareSuperNode = bareSuper.node;\n    let call;\n\n    if (assumptions.superIsCallableConstructor) {\n      bareSuperNode.arguments.unshift(t.thisExpression());\n      if (\n        bareSuperNode.arguments.length === 2 &&\n        t.isSpreadElement(bareSuperNode.arguments[1]) &&\n        t.isIdentifier(bareSuperNode.arguments[1].argument, {\n          name: \"arguments\",\n        })\n      ) {\n        // special case single arguments spread\n        bareSuperNode.arguments[1] = bareSuperNode.arguments[1].argument;\n        bareSuperNode.callee = t.memberExpression(\n          t.cloneNode(superRef),\n          t.identifier(\"apply\"),\n        );\n      } else {\n        bareSuperNode.callee = t.memberExpression(\n          t.cloneNode(superRef),\n          t.identifier(\"call\"),\n        );\n      }\n\n      call = t.logicalExpression(\"||\", bareSuperNode, t.thisExpression());\n    } else {\n      call = optimiseCall(\n        t.cloneNode(classState.superFnId),\n        t.thisExpression(),\n        bareSuperNode.arguments,\n        false,\n      );\n    }\n\n    if (\n      bareSuper.parentPath.isExpressionStatement() &&\n      bareSuper.parentPath.container === body.node.body &&\n      body.node.body.length - 1 === bareSuper.parentPath.key\n    ) {\n      // this super call is the last statement in the body so we can just straight up\n      // turn it into a return\n\n      if (classState.superThises.length) {\n        call = t.assignmentExpression(\"=\", thisRef(), call);\n      }\n\n      bareSuper.parentPath.replaceWith(t.returnStatement(call));\n    } else {\n      bareSuper.replaceWith(t.assignmentExpression(\"=\", thisRef(), call));\n    }\n  }\n\n  function verifyConstructor() {\n    if (!classState.isDerived) return;\n\n    const path = classState.userConstructorPath;\n    const body = path.get(\"body\");\n\n    path.traverse(findThisesVisitor);\n\n    let thisRef = function () {\n      const ref = path.scope.generateDeclaredUidIdentifier(\"this\");\n      thisRef = () => t.cloneNode(ref);\n      return ref;\n    };\n\n    for (const thisPath of classState.superThises) {\n      const { node, parentPath } = thisPath;\n      if (parentPath.isMemberExpression({ object: node })) {\n        thisPath.replaceWith(thisRef());\n        continue;\n      }\n      thisPath.replaceWith(\n        t.callExpression(classState.file.addHelper(\"assertThisInitialized\"), [\n          thisRef(),\n        ]),\n      );\n    }\n\n    const bareSupers: NodePath<t.CallExpression>[] = [];\n    path.traverse(\n      traverse.visitors.merge([\n        environmentVisitor,\n        {\n          Super(path) {\n            const { node, parentPath } = path;\n            if (parentPath.isCallExpression({ callee: node })) {\n              bareSupers.unshift(parentPath);\n            }\n          },\n        } as Visitor,\n      ]),\n    );\n\n    let guaranteedSuperBeforeFinish = !!bareSupers.length;\n\n    for (const bareSuper of bareSupers) {\n      wrapSuperCall(bareSuper, classState.superName, thisRef, body);\n\n      if (guaranteedSuperBeforeFinish) {\n        bareSuper.find(function (parentPath) {\n          // hit top so short circuit\n          if (parentPath === path) {\n            return true;\n          }\n\n          if (\n            parentPath.isLoop() ||\n            parentPath.isConditional() ||\n            parentPath.isArrowFunctionExpression()\n          ) {\n            guaranteedSuperBeforeFinish = false;\n            return true;\n          }\n        });\n      }\n    }\n\n    let wrapReturn;\n\n    if (classState.isLoose) {\n      wrapReturn = (returnArg: t.Expression | void) => {\n        const thisExpr = t.callExpression(\n          classState.file.addHelper(\"assertThisInitialized\"),\n          [thisRef()],\n        );\n        return returnArg\n          ? t.logicalExpression(\"||\", returnArg, thisExpr)\n          : thisExpr;\n      };\n    } else {\n      wrapReturn = (returnArg: t.Expression | undefined) => {\n        const returnParams: t.Expression[] = [thisRef()];\n        if (returnArg != null) {\n          returnParams.push(returnArg);\n        }\n        return t.callExpression(\n          classState.file.addHelper(\"possibleConstructorReturn\"),\n          returnParams,\n        );\n      };\n    }\n\n    // if we have a return as the last node in the body then we've already caught that\n    // return\n    const bodyPaths = body.get(\"body\");\n    if (!bodyPaths.length || !bodyPaths.pop().isReturnStatement()) {\n      body.pushContainer(\n        \"body\",\n        t.returnStatement(\n          guaranteedSuperBeforeFinish ? thisRef() : wrapReturn(),\n        ),\n      );\n    }\n\n    for (const returnPath of classState.superReturns) {\n      returnPath\n        .get(\"argument\")\n        .replaceWith(wrapReturn(returnPath.node.argument));\n    }\n  }\n\n  /**\n   * Push a method to its respective mutatorMap.\n   */\n  function pushMethod(node: t.ClassMethod, path?: NodePath) {\n    const scope = path ? path.scope : classState.scope;\n\n    if (node.kind === \"method\") {\n      if (processMethod(node, scope)) return;\n    }\n\n    const placement = node.static ? \"static\" : \"instance\";\n    const methods = classState.methods[placement];\n\n    const descKey = node.kind === \"method\" ? \"value\" : node.kind;\n    const key =\n      t.isNumericLiteral(node.key) || t.isBigIntLiteral(node.key)\n        ? t.stringLiteral(String(node.key.value))\n        : t.toComputedKey(node);\n\n    let fn: t.Expression = t.toExpression(node);\n\n    if (t.isStringLiteral(key)) {\n      // infer function name\n      if (node.kind === \"method\") {\n        // @ts-expect-error Fixme: we are passing a ClassMethod to nameFunction, but nameFunction\n        // does not seem to support it\n        fn =\n          nameFunction(\n            // @ts-expect-error Fixme: we are passing a ClassMethod to nameFunction, but nameFunction\n            // does not seem to support it\n            { id: key, node: node, scope },\n            undefined,\n            supportUnicodeId,\n          ) ?? fn;\n      }\n    } else {\n      // todo(flow->ts) find a way to avoid \"key as t.StringLiteral\" below which relies on this assignment\n      methods.hasComputed = true;\n    }\n\n    let descriptor: Descriptor;\n    if (\n      !methods.hasComputed &&\n      methods.map.has((key as t.StringLiteral).value)\n    ) {\n      descriptor = methods.map.get((key as t.StringLiteral).value);\n      descriptor[descKey] = fn;\n\n      if (descKey === \"value\") {\n        descriptor.get = null;\n        descriptor.set = null;\n      } else {\n        descriptor.value = null;\n      }\n    } else {\n      descriptor = {\n        key:\n          // private name has been handled in class-properties transform\n          key as t.Expression,\n        [descKey]: fn,\n      } as Descriptor;\n      methods.list.push(descriptor);\n\n      if (!methods.hasComputed) {\n        methods.map.set((key as t.StringLiteral).value, descriptor);\n      }\n    }\n  }\n\n  function processMethod(node: t.ClassMethod, scope: Scope) {\n    if (assumptions.setClassMethods && !node.decorators) {\n      // use assignments instead of define properties for loose classes\n      let { classRef } = classState;\n      if (!node.static) {\n        insertProtoAliasOnce();\n        classRef = classState.protoAlias;\n      }\n      const methodName = t.memberExpression(\n        t.cloneNode(classRef),\n        node.key,\n        node.computed || t.isLiteral(node.key),\n      );\n\n      let func: t.Expression = t.functionExpression(\n        null,\n        // @ts-expect-error Fixme: should throw when we see TSParameterProperty\n        node.params,\n        node.body,\n        node.generator,\n        node.async,\n      );\n      t.inherits(func, node);\n\n      const key = t.toComputedKey(node, node.key);\n      if (t.isStringLiteral(key)) {\n        // @ts-expect-error: requires strictNullCheck\n        func =\n          nameFunction(\n            {\n              node: func,\n              id: key,\n              scope,\n            },\n            undefined,\n            supportUnicodeId,\n          ) ?? func;\n      }\n\n      const expr = t.expressionStatement(\n        t.assignmentExpression(\"=\", methodName, func),\n      );\n      t.inheritsComments(expr, node);\n      classState.body.push(expr);\n      return true;\n    }\n\n    return false;\n  }\n\n  function insertProtoAliasOnce() {\n    if (classState.protoAlias === null) {\n      setState({ protoAlias: classState.scope.generateUidIdentifier(\"proto\") });\n      const classProto = t.memberExpression(\n        classState.classRef,\n        t.identifier(\"prototype\"),\n      );\n      const protoDeclaration = t.variableDeclaration(\"var\", [\n        t.variableDeclarator(classState.protoAlias, classProto),\n      ]);\n\n      classState.body.push(protoDeclaration);\n    }\n  }\n\n  /**\n   * Replace the constructor body of our class.\n   */\n  function pushConstructor(\n    superReturns: NodePath<t.ReturnStatement>[],\n    method: ClassConstructor,\n    path: NodePath<ClassConstructor>,\n  ) {\n    setState({\n      userConstructorPath: path,\n      userConstructor: method,\n      hasConstructor: true,\n      superReturns,\n    });\n\n    const { construct } = classState;\n\n    t.inheritsComments(construct, method);\n\n    // @ts-expect-error Fixme: should throw when we see TSParameterProperty\n    construct.params = method.params;\n\n    t.inherits(construct.body, method.body);\n    construct.body.directives = method.body.directives;\n\n    pushConstructorToBody();\n  }\n\n  function pushConstructorToBody() {\n    if (classState.pushedConstructor) return;\n    classState.pushedConstructor = true;\n\n    // we haven't pushed any descriptors yet\n    // @ts-expect-error todo(flow->ts) maybe remove this block - properties from condition are not used anywhere else\n    if (classState.hasInstanceDescriptors || classState.hasStaticDescriptors) {\n      pushDescriptors();\n    }\n\n    classState.body.push(classState.construct);\n\n    pushInheritsToBody();\n  }\n\n  /**\n   * Push inherits helper to body.\n   */\n  function pushInheritsToBody() {\n    if (!classState.isDerived || classState.pushedInherits) return;\n\n    const superFnId = path.scope.generateUidIdentifier(\"super\");\n\n    setState({ pushedInherits: true, superFnId });\n\n    // Unshift to ensure that the constructor inheritance is set up before\n    // any properties can be assigned to the prototype.\n\n    if (!assumptions.superIsCallableConstructor) {\n      classState.body.unshift(\n        t.variableDeclaration(\"var\", [\n          t.variableDeclarator(\n            superFnId,\n            t.callExpression(addCreateSuperHelper(classState.file), [\n              t.cloneNode(classState.classRef),\n            ]),\n          ),\n        ]),\n      );\n    }\n\n    classState.body.unshift(\n      t.expressionStatement(\n        t.callExpression(\n          classState.file.addHelper(\n            classState.isLoose ? \"inheritsLoose\" : \"inherits\",\n          ),\n          [t.cloneNode(classState.classRef), t.cloneNode(classState.superName)],\n        ),\n      ),\n    );\n  }\n\n  function extractDynamicKeys() {\n    const { dynamicKeys, node, scope } = classState;\n\n    for (const elem of node.body.body) {\n      if (!t.isClassMethod(elem) || !elem.computed) continue;\n      if (scope.isPure(elem.key, /* constants only*/ true)) continue;\n\n      const id = scope.generateUidIdentifierBasedOnNode(elem.key);\n      dynamicKeys.set(id.name, elem.key);\n\n      elem.key = id;\n    }\n  }\n\n  function setupClosureParamsArgs() {\n    const { superName, dynamicKeys } = classState;\n    const closureParams = [];\n    const closureArgs = [];\n\n    if (classState.isDerived) {\n      let arg = t.cloneNode(superName);\n      if (classState.extendsNative) {\n        arg = t.callExpression(classState.file.addHelper(\"wrapNativeSuper\"), [\n          arg,\n        ]);\n        annotateAsPure(arg);\n      }\n\n      const param =\n        classState.scope.generateUidIdentifierBasedOnNode(superName);\n\n      closureParams.push(param);\n      closureArgs.push(arg);\n\n      setState({ superName: t.cloneNode(param) });\n    }\n\n    for (const [name, value] of dynamicKeys) {\n      closureParams.push(t.identifier(name));\n      closureArgs.push(value);\n    }\n\n    return { closureParams, closureArgs };\n  }\n\n  function classTransformer(\n    path: NodePath<t.Class>,\n    file: File,\n    builtinClasses: ReadonlySet<string>,\n    isLoose: boolean,\n  ) {\n    setState({\n      parent: path.parent,\n      scope: path.scope,\n      node: path.node,\n      path,\n      file,\n      isLoose,\n    });\n\n    setState({\n      classId: classState.node.id,\n      // this is the name of the binding that will **always** reference the class we've constructed\n      classRef: classState.node.id\n        ? t.identifier(classState.node.id.name)\n        : classState.scope.generateUidIdentifier(\"class\"),\n      superName: classState.node.superClass,\n      isDerived: !!classState.node.superClass,\n      constructorBody: t.blockStatement([]),\n    });\n\n    setState({\n      extendsNative:\n        t.isIdentifier(classState.superName) &&\n        builtinClasses.has(classState.superName.name) &&\n        !classState.scope.hasBinding(\n          classState.superName.name,\n          /* noGlobals */ true,\n        ),\n    });\n\n    const { classRef, node, constructorBody } = classState;\n\n    setState({\n      construct: buildConstructor(classRef, constructorBody, node),\n    });\n\n    extractDynamicKeys();\n\n    const { body } = classState;\n    const { closureParams, closureArgs } = setupClosureParamsArgs();\n\n    buildBody();\n\n    // make sure this class isn't directly called (with A() instead new A())\n    if (!assumptions.noClassCalls) {\n      constructorBody.body.unshift(\n        t.expressionStatement(\n          t.callExpression(classState.file.addHelper(\"classCallCheck\"), [\n            t.thisExpression(),\n            t.cloneNode(classState.classRef),\n          ]),\n        ),\n      );\n    }\n\n    const isStrict = path.isInStrictMode();\n    let constructorOnly = classState.classId && body.length === 1;\n    if (constructorOnly && !isStrict) {\n      for (const param of classState.construct.params) {\n        // It's illegal to put a use strict directive into the body of a function\n        // with non-simple parameters for some reason. So, we have to use a strict\n        // wrapper function.\n        if (!t.isIdentifier(param)) {\n          constructorOnly = false;\n          break;\n        }\n      }\n    }\n\n    const directives = constructorOnly\n      ? (body[0] as t.FunctionExpression | t.FunctionDeclaration).body\n          .directives\n      : [];\n    if (!isStrict) {\n      directives.push(t.directive(t.directiveLiteral(\"use strict\")));\n    }\n\n    if (constructorOnly) {\n      // named class with only a constructor\n      const expr = t.toExpression(\n        body[0] as t.FunctionExpression | t.FunctionDeclaration,\n      );\n      return classState.isLoose ? expr : createClassHelper([expr]);\n    }\n\n    let returnArg: t.Expression = t.cloneNode(classState.classRef);\n    if (!classState.pushedCreateClass && !classState.isLoose) {\n      returnArg = createClassHelper([returnArg]);\n    }\n\n    body.push(t.returnStatement(returnArg));\n    const container = t.arrowFunctionExpression(\n      closureParams,\n      t.blockStatement(body, directives),\n    );\n    return t.callExpression(container, closureArgs);\n  }\n\n  return classTransformer(path, file, builtinClasses, isLoose);\n}\n"], "mappings": ";;;;;;AACA,IAAAA,mBAAA,GAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAD,OAAA;AACA,IAAAE,yBAAA,GAAAF,OAAA;AACA,IAAAG,6BAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,qBAAA,GAAAL,OAAA;AAEA,IAAAM,yBAAA,GAAAN,OAAA;AAWA,SAASO,gBAAgBA,CACvBC,QAAsB,EACtBC,eAAiC,EACjCC,IAAa,EACb;EACA,MAAMC,IAAI,GAAGC,WAAC,CAACC,mBAAmB,CAChCD,WAAC,CAACE,SAAS,CAACN,QAAQ,CAAC,EACrB,EAAE,EACFC,eACF,CAAC;EACDG,WAAC,CAACG,QAAQ,CAACJ,IAAI,EAAED,IAAI,CAAC;EACtB,OAAOC,IAAI;AACb;AA+De,SAASK,cAAcA,CACpCC,IAAuB,EACvBC,IAAU,EACVC,cAAmC,EACnCC,OAAgB,EAChBC,WAA6B,EAC7BC,gBAAyB,EACzB;EACA,MAAMC,UAAiB,GAAG;IACxBC,MAAM,EAAEC,SAAS;IACjBC,KAAK,EAAED,SAAS;IAChBf,IAAI,EAAEe,SAAS;IACfR,IAAI,EAAEQ,SAAS;IACfP,IAAI,EAAEO,SAAS;IAEfE,OAAO,EAAEF,SAAS;IAClBjB,QAAQ,EAAEiB,SAAS;IACnBG,SAAS,EAAEH,SAAS;IACpBI,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,KAAK;IAEpBC,SAAS,EAAER,SAAS;IACpBhB,eAAe,EAAEgB,SAAS;IAC1BS,eAAe,EAAET,SAAS;IAC1BU,mBAAmB,EAAEV,SAAS;IAC9BW,cAAc,EAAE,KAAK;IAErBC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE,KAAK;IACxBC,cAAc,EAAE,KAAK;IACrBC,iBAAiB,EAAE,KAAK;IACxBC,UAAU,EAAE,IAAI;IAChBtB,OAAO,EAAE,KAAK;IAEduB,WAAW,EAAE,IAAIC,GAAG,CAAC,CAAC;IAEtBC,OAAO,EAAE;MACPC,QAAQ,EAAE;QACRC,WAAW,EAAE,KAAK;QAClBC,IAAI,EAAE,EAAE;QACRC,GAAG,EAAE,IAAIL,GAAG,CAAC;MACf,CAAC;MACDM,MAAM,EAAE;QACNH,WAAW,EAAE,KAAK;QAClBC,IAAI,EAAE,EAAE;QACRC,GAAG,EAAE,IAAIL,GAAG,CAAC;MACf;IACF;EACF,CAAC;EAED,MAAMO,QAAQ,GAAIC,QAAwB,IAAK;IAC7CC,MAAM,CAACC,MAAM,CAAC/B,UAAU,EAAE6B,QAAQ,CAAC;EACrC,CAAC;EAED,MAAMG,iBAAiB,GAAGC,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CAChDC,iCAAkB,EAClB;IACEC,cAAcA,CAAC3C,IAAI,EAAE;MACnBM,UAAU,CAACe,WAAW,CAACuB,IAAI,CAAC5C,IAAI,CAAC;IACnC;EACF,CAAC,CACF,CAAC;EAEF,SAAS6C,iBAAiBA,CAACC,IAAoB,EAAE;IAC/C,OAAOnD,WAAC,CAACoD,cAAc,CAACzC,UAAU,CAACL,IAAI,CAAC+C,SAAS,CAAC,aAAa,CAAC,EAAEF,IAAI,CAAC;EACzE;EAKA,SAASG,sBAAsBA,CAAA,EAAG;IAChC,IAAI9B,cAAc,GAAG,KAAK;IAC1B,MAAM+B,KAAK,GAAG5C,UAAU,CAACN,IAAI,CAACmD,GAAG,CAAC,WAAW,CAAC;IAC9C,KAAK,MAAMnD,IAAI,IAAIkD,KAAK,EAAE;MAExB/B,cAAc,GAAGnB,IAAI,CAACoD,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC;MACnD,IAAIjC,cAAc,EAAE;IACtB;IACA,IAAIA,cAAc,EAAE;IAEpB,IAAIkC,MAAsC,EAAEjC,IAAI;IAEhD,IAAId,UAAU,CAACQ,SAAS,EAAE;MACxB,MAAMwC,WAAW,GAAGC,cAAQ,CAACC,UAAU,CAACC,GAAI;AAClD;AACA;AACA;AACA,OAA+B;MACzBJ,MAAM,GAAGC,WAAW,CAACD,MAAM;MAC3BjC,IAAI,GAAGkC,WAAW,CAAClC,IAAI;IACzB,CAAC,MAAM;MACLiC,MAAM,GAAG,EAAE;MACXjC,IAAI,GAAGzB,WAAC,CAAC+D,cAAc,CAAC,EAAE,CAAC;IAC7B;IAEApD,UAAU,CAACN,IAAI,CACZmD,GAAG,CAAC,MAAM,CAAC,CACXQ,gBAAgB,CACf,MAAM,EACNhE,WAAC,CAACiE,WAAW,CAAC,aAAa,EAAEjE,WAAC,CAACkE,UAAU,CAAC,aAAa,CAAC,EAAER,MAAM,EAAEjC,IAAI,CACxE,CAAC;EACL;EAEA,SAAS0C,SAASA,CAAA,EAAG;IACnBb,sBAAsB,CAAC,CAAC;IACxBc,QAAQ,CAAC,CAAC;IACVC,iBAAiB,CAAC,CAAC;IAEnB,IAAI1D,UAAU,CAACW,eAAe,EAAE;MAC9B,MAAM;QAAEzB,eAAe;QAAEyB,eAAe;QAAED;MAAU,CAAC,GAAGV,UAAU;MAElEd,eAAe,CAAC4B,IAAI,CAACwB,IAAI,CAAC,GAAG3B,eAAe,CAACG,IAAI,CAACA,IAAI,CAAC;MACvDzB,WAAC,CAACG,QAAQ,CAACkB,SAAS,EAAEC,eAAe,CAAC;MACtCtB,WAAC,CAACG,QAAQ,CAACN,eAAe,EAAEyB,eAAe,CAACG,IAAI,CAAC;IACnD;IAEA6C,eAAe,CAAC,CAAC;EACnB;EAEA,SAASF,QAAQA,CAAA,EAAG;IAClB,MAAMG,cAA0B,GAAG5D,UAAU,CAACN,IAAI,CAACmD,GAAG,CAAC,WAAW,CAAC;IAEnE,KAAK,MAAMnD,IAAI,IAAIkE,cAAc,EAAE;MACjC,MAAMzE,IAAI,GAAGO,IAAI,CAACP,IAAI;MAEtB,IAAIO,IAAI,CAACmE,eAAe,CAAC,CAAC,EAAE;QAC1B,MAAMnE,IAAI,CAACoE,mBAAmB,CAAC,qCAAqC,CAAC;MACvE;MAEA,IAAI3E,IAAI,CAAC4E,UAAU,EAAE;QACnB,MAAMrE,IAAI,CAACoE,mBAAmB,CAC5B,yEACF,CAAC;MACH;MAEA,IAAIzE,WAAC,CAAC2E,aAAa,CAAC7E,IAAI,CAAC,EAAE;QACzB,MAAM8E,aAAa,GAAG9E,IAAI,CAAC+E,IAAI,KAAK,aAAa;QAEjD,MAAMC,aAAa,GAAG,IAAIC,4BAAa,CAAC;UACtCC,UAAU,EAAE3E,IAAI;UAChB4E,SAAS,EAAEtE,UAAU,CAACf,QAAQ;UAC9BsF,QAAQ,EAAEvE,UAAU,CAACM,SAAS;UAC9BkE,aAAa,EAAE1E,WAAW,CAAC0E,aAAa;UACxC7E,IAAI,EAAEK,UAAU,CAACL,IAAI;UACrB8E,aAAa,EAAEzE,UAAU,CAACf;QAC5B,CAAC,CAAC;QAEFkF,aAAa,CAACO,OAAO,CAAC,CAAC;QAEvB,MAAMnE,YAA2C,GAAG,EAAE;QACtDb,IAAI,CAACuC,QAAQ,CACXA,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CACtBC,iCAAkB,EAClB;UACEuC,eAAeA,CAACjF,IAAI,EAAE;YACpB,IAAI,CAACA,IAAI,CAACkF,iBAAiB,CAAC,CAAC,CAACC,yBAAyB,CAAC,CAAC,EAAE;cACzDtE,YAAY,CAAC+B,IAAI,CAAC5C,IAAI,CAAC;YACzB;UACF;QACF,CAAC,CACF,CACH,CAAC;QAED,IAAIuE,aAAa,EAAE;UACjBa,eAAe,CAACvE,YAAY,EAAEpB,IAAI,EAAsBO,IAAI,CAAC;QAC/D,CAAC,MAAM;UACLqF,UAAU,CAAC5F,IAAI,EAAEO,IAAI,CAAC;QACxB;MACF;IACF;EACF;EAEA,SAASiE,eAAeA,CAAA,EAAG;IACzBqB,kBAAkB,CAAC,CAAC;IAEpB,MAAM;MAAElE;IAAK,CAAC,GAAGd,UAAU;IAE3B,MAAMiF,KAAmB,GAAG;MAC1B1D,QAAQ,EAAE,IAAI;MACdI,MAAM,EAAE;IACV,CAAC;IAED,KAAK,MAAMuD,SAAS,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAW;MACvD,IAAIlF,UAAU,CAACsB,OAAO,CAAC4D,SAAS,CAAC,CAACzD,IAAI,CAAC0D,MAAM,EAAE;QAC7CF,KAAK,CAACC,SAAS,CAAC,GAAGlF,UAAU,CAACsB,OAAO,CAAC4D,SAAS,CAAC,CAACzD,IAAI,CAACC,GAAG,CAAC0D,IAAI,IAAI;UAChE,MAAMC,GAAG,GAAGhG,WAAC,CAACiG,gBAAgB,CAAC,CAC7BjG,WAAC,CAACkG,cAAc,CAAClG,WAAC,CAACkE,UAAU,CAAC,KAAK,CAAC,EAAE6B,IAAI,CAACI,GAAG,CAAC,CAChD,CAAC;UAEF,KAAK,MAAMtB,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,EAAW;YACnD,IAAIkB,IAAI,CAAClB,IAAI,CAAC,IAAI,IAAI,EAAE;cACtBmB,GAAG,CAACI,UAAU,CAACnD,IAAI,CACjBjD,WAAC,CAACkG,cAAc,CAAClG,WAAC,CAACkE,UAAU,CAACW,IAAI,CAAC,EAAEkB,IAAI,CAAClB,IAAI,CAAC,CACjD,CAAC;YACH;UACF;UAEA,OAAOmB,GAAG;QACZ,CAAC,CAAC;MACJ;IACF;IAEA,IAAIJ,KAAK,CAAC1D,QAAQ,IAAI0D,KAAK,CAACtD,MAAM,EAAE;MAClC,IAAIa,IAAI,GAAG,CACTnD,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,EAChCgG,KAAK,CAAC1D,QAAQ,GAAGlC,WAAC,CAACqG,eAAe,CAACT,KAAK,CAAC1D,QAAQ,CAAC,GAAGlC,WAAC,CAACsG,WAAW,CAAC,CAAC,EACpEV,KAAK,CAACtD,MAAM,GAAGtC,WAAC,CAACqG,eAAe,CAACT,KAAK,CAACtD,MAAM,CAAC,GAAGtC,WAAC,CAACsG,WAAW,CAAC,CAAC,CACjE;MAED,IAAIC,gBAAgB,GAAG,CAAC;MACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrD,IAAI,CAAC2C,MAAM,EAAEU,CAAC,EAAE,EAAE;QACpC,IAAI,CAACxG,WAAC,CAACyG,aAAa,CAACtD,IAAI,CAACqD,CAAC,CAAC,CAAC,EAAED,gBAAgB,GAAGC,CAAC;MACrD;MACArD,IAAI,GAAGA,IAAI,CAACuD,KAAK,CAAC,CAAC,EAAEH,gBAAgB,GAAG,CAAC,CAAC;MAE1C9E,IAAI,CAACwB,IAAI,CAACjD,WAAC,CAAC2G,mBAAmB,CAACzD,iBAAiB,CAACC,IAAI,CAAC,CAAC,CAAC;MACzDxC,UAAU,CAACkB,iBAAiB,GAAG,IAAI;IACrC;EACF;EAEA,SAAS+E,aAAaA,CACpBC,SAAqC,EACrC3B,QAAsB,EACtB4B,OAA2B,EAC3BrF,IAAgC,EAChC;IACA,MAAMsF,aAAa,GAAGF,SAAS,CAAC/G,IAAI;IACpC,IAAIkH,IAAI;IAER,IAAIvG,WAAW,CAACwG,0BAA0B,EAAE;MAC1CF,aAAa,CAACG,SAAS,CAACC,OAAO,CAACnH,WAAC,CAACoH,cAAc,CAAC,CAAC,CAAC;MACnD,IACEL,aAAa,CAACG,SAAS,CAACpB,MAAM,KAAK,CAAC,IACpC9F,WAAC,CAACqH,eAAe,CAACN,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,CAAC,IAC7ClH,WAAC,CAACsH,YAAY,CAACP,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,CAACK,QAAQ,EAAE;QAClDC,IAAI,EAAE;MACR,CAAC,CAAC,EACF;QAEAT,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,GAAGH,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,CAACK,QAAQ;QAChER,aAAa,CAACU,MAAM,GAAGzH,WAAC,CAAC0H,gBAAgB,CACvC1H,WAAC,CAACE,SAAS,CAACgF,QAAQ,CAAC,EACrBlF,WAAC,CAACkE,UAAU,CAAC,OAAO,CACtB,CAAC;MACH,CAAC,MAAM;QACL6C,aAAa,CAACU,MAAM,GAAGzH,WAAC,CAAC0H,gBAAgB,CACvC1H,WAAC,CAACE,SAAS,CAACgF,QAAQ,CAAC,EACrBlF,WAAC,CAACkE,UAAU,CAAC,MAAM,CACrB,CAAC;MACH;MAEA8C,IAAI,GAAGhH,WAAC,CAAC2H,iBAAiB,CAAC,IAAI,EAAEZ,aAAa,EAAE/G,WAAC,CAACoH,cAAc,CAAC,CAAC,CAAC;IACrE,CAAC,MAAM;MACLJ,IAAI,GAAG,IAAAY,qCAAY,EACjB5H,WAAC,CAACE,SAAS,CAACS,UAAU,CAACK,SAAS,CAAC,EACjChB,WAAC,CAACoH,cAAc,CAAC,CAAC,EAClBL,aAAa,CAACG,SAAS,EACvB,KACF,CAAC;IACH;IAEA,IACEL,SAAS,CAACgB,UAAU,CAACC,qBAAqB,CAAC,CAAC,IAC5CjB,SAAS,CAACgB,UAAU,CAACE,SAAS,KAAKtG,IAAI,CAAC3B,IAAI,CAAC2B,IAAI,IACjDA,IAAI,CAAC3B,IAAI,CAAC2B,IAAI,CAACqE,MAAM,GAAG,CAAC,KAAKe,SAAS,CAACgB,UAAU,CAAC1B,GAAG,EACtD;MAIA,IAAIxF,UAAU,CAACe,WAAW,CAACoE,MAAM,EAAE;QACjCkB,IAAI,GAAGhH,WAAC,CAACgI,oBAAoB,CAAC,GAAG,EAAElB,OAAO,CAAC,CAAC,EAAEE,IAAI,CAAC;MACrD;MAEAH,SAAS,CAACgB,UAAU,CAACI,WAAW,CAACjI,WAAC,CAACkI,eAAe,CAAClB,IAAI,CAAC,CAAC;IAC3D,CAAC,MAAM;MACLH,SAAS,CAACoB,WAAW,CAACjI,WAAC,CAACgI,oBAAoB,CAAC,GAAG,EAAElB,OAAO,CAAC,CAAC,EAAEE,IAAI,CAAC,CAAC;IACrE;EACF;EAEA,SAAS3C,iBAAiBA,CAAA,EAAG;IAC3B,IAAI,CAAC1D,UAAU,CAACQ,SAAS,EAAE;IAE3B,MAAMd,IAAI,GAAGM,UAAU,CAACY,mBAAmB;IAC3C,MAAME,IAAI,GAAGpB,IAAI,CAACmD,GAAG,CAAC,MAAM,CAAC;IAE7BnD,IAAI,CAACuC,QAAQ,CAACD,iBAAiB,CAAC;IAEhC,IAAImE,OAAO,GAAG,SAAAA,CAAA,EAAY;MACxB,MAAMqB,GAAG,GAAG9H,IAAI,CAACS,KAAK,CAACsH,6BAA6B,CAAC,MAAM,CAAC;MAC5DtB,OAAO,GAAGA,CAAA,KAAM9G,WAAC,CAACE,SAAS,CAACiI,GAAG,CAAC;MAChC,OAAOA,GAAG;IACZ,CAAC;IAED,KAAK,MAAME,QAAQ,IAAI1H,UAAU,CAACe,WAAW,EAAE;MAC7C,MAAM;QAAE5B,IAAI;QAAE+H;MAAW,CAAC,GAAGQ,QAAQ;MACrC,IAAIR,UAAU,CAACS,kBAAkB,CAAC;QAAEC,MAAM,EAAEzI;MAAK,CAAC,CAAC,EAAE;QACnDuI,QAAQ,CAACJ,WAAW,CAACnB,OAAO,CAAC,CAAC,CAAC;QAC/B;MACF;MACAuB,QAAQ,CAACJ,WAAW,CAClBjI,WAAC,CAACoD,cAAc,CAACzC,UAAU,CAACL,IAAI,CAAC+C,SAAS,CAAC,uBAAuB,CAAC,EAAE,CACnEyD,OAAO,CAAC,CAAC,CACV,CACH,CAAC;IACH;IAEA,MAAM0B,UAAwC,GAAG,EAAE;IACnDnI,IAAI,CAACuC,QAAQ,CACXA,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CACtBC,iCAAkB,EAClB;MACE0F,KAAKA,CAACpI,IAAI,EAAE;QACV,MAAM;UAAEP,IAAI;UAAE+H;QAAW,CAAC,GAAGxH,IAAI;QACjC,IAAIwH,UAAU,CAACa,gBAAgB,CAAC;UAAEjB,MAAM,EAAE3H;QAAK,CAAC,CAAC,EAAE;UACjD0I,UAAU,CAACrB,OAAO,CAACU,UAAU,CAAC;QAChC;MACF;IACF,CAAC,CACF,CACH,CAAC;IAED,IAAIc,2BAA2B,GAAG,CAAC,CAACH,UAAU,CAAC1C,MAAM;IAErD,KAAK,MAAMe,SAAS,IAAI2B,UAAU,EAAE;MAClC5B,aAAa,CAACC,SAAS,EAAElG,UAAU,CAACM,SAAS,EAAE6F,OAAO,EAAErF,IAAI,CAAC;MAE7D,IAAIkH,2BAA2B,EAAE;QAC/B9B,SAAS,CAAC+B,IAAI,CAAC,UAAUf,UAAU,EAAE;UAEnC,IAAIA,UAAU,KAAKxH,IAAI,EAAE;YACvB,OAAO,IAAI;UACb;UAEA,IACEwH,UAAU,CAACgB,MAAM,CAAC,CAAC,IACnBhB,UAAU,CAACiB,aAAa,CAAC,CAAC,IAC1BjB,UAAU,CAACrC,yBAAyB,CAAC,CAAC,EACtC;YACAmD,2BAA2B,GAAG,KAAK;YACnC,OAAO,IAAI;UACb;QACF,CAAC,CAAC;MACJ;IACF;IAEA,IAAII,UAAU;IAEd,IAAIpI,UAAU,CAACH,OAAO,EAAE;MACtBuI,UAAU,GAAIC,SAA8B,IAAK;QAC/C,MAAMC,QAAQ,GAAGjJ,WAAC,CAACoD,cAAc,CAC/BzC,UAAU,CAACL,IAAI,CAAC+C,SAAS,CAAC,uBAAuB,CAAC,EAClD,CAACyD,OAAO,CAAC,CAAC,CACZ,CAAC;QACD,OAAOkC,SAAS,GACZhJ,WAAC,CAAC2H,iBAAiB,CAAC,IAAI,EAAEqB,SAAS,EAAEC,QAAQ,CAAC,GAC9CA,QAAQ;MACd,CAAC;IACH,CAAC,MAAM;MACLF,UAAU,GAAIC,SAAmC,IAAK;QACpD,MAAME,YAA4B,GAAG,CAACpC,OAAO,CAAC,CAAC,CAAC;QAChD,IAAIkC,SAAS,IAAI,IAAI,EAAE;UACrBE,YAAY,CAACjG,IAAI,CAAC+F,SAAS,CAAC;QAC9B;QACA,OAAOhJ,WAAC,CAACoD,cAAc,CACrBzC,UAAU,CAACL,IAAI,CAAC+C,SAAS,CAAC,2BAA2B,CAAC,EACtD6F,YACF,CAAC;MACH,CAAC;IACH;IAIA,MAAMC,SAAS,GAAG1H,IAAI,CAAC+B,GAAG,CAAC,MAAM,CAAC;IAClC,IAAI,CAAC2F,SAAS,CAACrD,MAAM,IAAI,CAACqD,SAAS,CAACC,GAAG,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC,EAAE;MAC7D5H,IAAI,CAAC6H,aAAa,CAChB,MAAM,EACNtJ,WAAC,CAACkI,eAAe,CACfS,2BAA2B,GAAG7B,OAAO,CAAC,CAAC,GAAGiC,UAAU,CAAC,CACvD,CACF,CAAC;IACH;IAEA,KAAK,MAAMQ,UAAU,IAAI5I,UAAU,CAACO,YAAY,EAAE;MAChDqI,UAAU,CACP/F,GAAG,CAAC,UAAU,CAAC,CACfyE,WAAW,CAACc,UAAU,CAACQ,UAAU,CAACzJ,IAAI,CAACyH,QAAQ,CAAC,CAAC;IACtD;EACF;EAKA,SAAS7B,UAAUA,CAAC5F,IAAmB,EAAEO,IAAe,EAAE;IACxD,MAAMS,KAAK,GAAGT,IAAI,GAAGA,IAAI,CAACS,KAAK,GAAGH,UAAU,CAACG,KAAK;IAElD,IAAIhB,IAAI,CAAC+E,IAAI,KAAK,QAAQ,EAAE;MAC1B,IAAI2E,aAAa,CAAC1J,IAAI,EAAEgB,KAAK,CAAC,EAAE;IAClC;IAEA,MAAM+E,SAAS,GAAG/F,IAAI,CAACwC,MAAM,GAAG,QAAQ,GAAG,UAAU;IACrD,MAAML,OAAO,GAAGtB,UAAU,CAACsB,OAAO,CAAC4D,SAAS,CAAC;IAE7C,MAAM4D,OAAO,GAAG3J,IAAI,CAAC+E,IAAI,KAAK,QAAQ,GAAG,OAAO,GAAG/E,IAAI,CAAC+E,IAAI;IAC5D,MAAMsB,GAAG,GACPnG,WAAC,CAAC0J,gBAAgB,CAAC5J,IAAI,CAACqG,GAAG,CAAC,IAAInG,WAAC,CAAC2J,eAAe,CAAC7J,IAAI,CAACqG,GAAG,CAAC,GACvDnG,WAAC,CAAC4J,aAAa,CAACC,MAAM,CAAC/J,IAAI,CAACqG,GAAG,CAAC2D,KAAK,CAAC,CAAC,GACvC9J,WAAC,CAAC+J,aAAa,CAACjK,IAAI,CAAC;IAE3B,IAAIkK,EAAgB,GAAGhK,WAAC,CAACiK,YAAY,CAACnK,IAAI,CAAC;IAE3C,IAAIE,WAAC,CAACkK,eAAe,CAAC/D,GAAG,CAAC,EAAE;MAE1B,IAAIrG,IAAI,CAAC+E,IAAI,KAAK,QAAQ,EAAE;QAAA,IAAAsF,aAAA;QAG1BH,EAAE,IAAAG,aAAA,GACA,IAAAC,2BAAY,EAGV;UAAEC,EAAE,EAAElE,GAAG;UAAErG,IAAI,EAAEA,IAAI;UAAEgB;QAAM,CAAC,EAC9BD,SAAS,EACTH,gBACF,CAAC,YAAAyJ,aAAA,GAAIH,EAAE;MACX;IACF,CAAC,MAAM;MAEL/H,OAAO,CAACE,WAAW,GAAG,IAAI;IAC5B;IAEA,IAAImI,UAAsB;IAC1B,IACE,CAACrI,OAAO,CAACE,WAAW,IACpBF,OAAO,CAACI,GAAG,CAACkI,GAAG,CAAEpE,GAAG,CAAqB2D,KAAK,CAAC,EAC/C;MACAQ,UAAU,GAAGrI,OAAO,CAACI,GAAG,CAACmB,GAAG,CAAE2C,GAAG,CAAqB2D,KAAK,CAAC;MAC5DQ,UAAU,CAACb,OAAO,CAAC,GAAGO,EAAE;MAExB,IAAIP,OAAO,KAAK,OAAO,EAAE;QACvBa,UAAU,CAAC9G,GAAG,GAAG,IAAI;QACrB8G,UAAU,CAACE,GAAG,GAAG,IAAI;MACvB,CAAC,MAAM;QACLF,UAAU,CAACR,KAAK,GAAG,IAAI;MACzB;IACF,CAAC,MAAM;MACLQ,UAAU,GAAG;QACXnE,GAAG,EAEDA,GAAmB;QACrB,CAACsD,OAAO,GAAGO;MACb,CAAe;MACf/H,OAAO,CAACG,IAAI,CAACa,IAAI,CAACqH,UAAU,CAAC;MAE7B,IAAI,CAACrI,OAAO,CAACE,WAAW,EAAE;QACxBF,OAAO,CAACI,GAAG,CAACmI,GAAG,CAAErE,GAAG,CAAqB2D,KAAK,EAAEQ,UAAU,CAAC;MAC7D;IACF;EACF;EAEA,SAASd,aAAaA,CAAC1J,IAAmB,EAAEgB,KAAY,EAAE;IACxD,IAAIL,WAAW,CAACgK,eAAe,IAAI,CAAC3K,IAAI,CAAC4E,UAAU,EAAE;MAEnD,IAAI;QAAE9E;MAAS,CAAC,GAAGe,UAAU;MAC7B,IAAI,CAACb,IAAI,CAACwC,MAAM,EAAE;QAChBoI,oBAAoB,CAAC,CAAC;QACtB9K,QAAQ,GAAGe,UAAU,CAACmB,UAAU;MAClC;MACA,MAAM6I,UAAU,GAAG3K,WAAC,CAAC0H,gBAAgB,CACnC1H,WAAC,CAACE,SAAS,CAACN,QAAQ,CAAC,EACrBE,IAAI,CAACqG,GAAG,EACRrG,IAAI,CAAC8K,QAAQ,IAAI5K,WAAC,CAAC6K,SAAS,CAAC/K,IAAI,CAACqG,GAAG,CACvC,CAAC;MAED,IAAIpG,IAAkB,GAAGC,WAAC,CAAC8K,kBAAkB,CAC3C,IAAI,EAEJhL,IAAI,CAAC4D,MAAM,EACX5D,IAAI,CAAC2B,IAAI,EACT3B,IAAI,CAACiL,SAAS,EACdjL,IAAI,CAACkL,KACP,CAAC;MACDhL,WAAC,CAACG,QAAQ,CAACJ,IAAI,EAAED,IAAI,CAAC;MAEtB,MAAMqG,GAAG,GAAGnG,WAAC,CAAC+J,aAAa,CAACjK,IAAI,EAAEA,IAAI,CAACqG,GAAG,CAAC;MAC3C,IAAInG,WAAC,CAACkK,eAAe,CAAC/D,GAAG,CAAC,EAAE;QAAA,IAAA8E,cAAA;QAE1BlL,IAAI,IAAAkL,cAAA,GACF,IAAAb,2BAAY,EACV;UACEtK,IAAI,EAAEC,IAAI;UACVsK,EAAE,EAAElE,GAAG;UACPrF;QACF,CAAC,EACDD,SAAS,EACTH,gBACF,CAAC,YAAAuK,cAAA,GAAIlL,IAAI;MACb;MAEA,MAAMmL,IAAI,GAAGlL,WAAC,CAAC2G,mBAAmB,CAChC3G,WAAC,CAACgI,oBAAoB,CAAC,GAAG,EAAE2C,UAAU,EAAE5K,IAAI,CAC9C,CAAC;MACDC,WAAC,CAACmL,gBAAgB,CAACD,IAAI,EAAEpL,IAAI,CAAC;MAC9Ba,UAAU,CAACc,IAAI,CAACwB,IAAI,CAACiI,IAAI,CAAC;MAC1B,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEA,SAASR,oBAAoBA,CAAA,EAAG;IAC9B,IAAI/J,UAAU,CAACmB,UAAU,KAAK,IAAI,EAAE;MAClCS,QAAQ,CAAC;QAAET,UAAU,EAAEnB,UAAU,CAACG,KAAK,CAACsK,qBAAqB,CAAC,OAAO;MAAE,CAAC,CAAC;MACzE,MAAMC,UAAU,GAAGrL,WAAC,CAAC0H,gBAAgB,CACnC/G,UAAU,CAACf,QAAQ,EACnBI,WAAC,CAACkE,UAAU,CAAC,WAAW,CAC1B,CAAC;MACD,MAAMoH,gBAAgB,GAAGtL,WAAC,CAACuL,mBAAmB,CAAC,KAAK,EAAE,CACpDvL,WAAC,CAACwL,kBAAkB,CAAC7K,UAAU,CAACmB,UAAU,EAAEuJ,UAAU,CAAC,CACxD,CAAC;MAEF1K,UAAU,CAACc,IAAI,CAACwB,IAAI,CAACqI,gBAAgB,CAAC;IACxC;EACF;EAKA,SAAS7F,eAAeA,CACtBvE,YAA2C,EAC3CuK,MAAwB,EACxBpL,IAAgC,EAChC;IACAkC,QAAQ,CAAC;MACPhB,mBAAmB,EAAElB,IAAI;MACzBiB,eAAe,EAAEmK,MAAM;MACvBjK,cAAc,EAAE,IAAI;MACpBN;IACF,CAAC,CAAC;IAEF,MAAM;MAAEG;IAAU,CAAC,GAAGV,UAAU;IAEhCX,WAAC,CAACmL,gBAAgB,CAAC9J,SAAS,EAAEoK,MAAM,CAAC;IAGrCpK,SAAS,CAACqC,MAAM,GAAG+H,MAAM,CAAC/H,MAAM;IAEhC1D,WAAC,CAACG,QAAQ,CAACkB,SAAS,CAACI,IAAI,EAAEgK,MAAM,CAAChK,IAAI,CAAC;IACvCJ,SAAS,CAACI,IAAI,CAACiK,UAAU,GAAGD,MAAM,CAAChK,IAAI,CAACiK,UAAU;IAElDC,qBAAqB,CAAC,CAAC;EACzB;EAEA,SAASA,qBAAqBA,CAAA,EAAG;IAC/B,IAAIhL,UAAU,CAACgB,iBAAiB,EAAE;IAClChB,UAAU,CAACgB,iBAAiB,GAAG,IAAI;IAInC,IAAIhB,UAAU,CAACiL,sBAAsB,IAAIjL,UAAU,CAACkL,oBAAoB,EAAE;MACxEvH,eAAe,CAAC,CAAC;IACnB;IAEA3D,UAAU,CAACc,IAAI,CAACwB,IAAI,CAACtC,UAAU,CAACU,SAAS,CAAC;IAE1CsE,kBAAkB,CAAC,CAAC;EACtB;EAKA,SAASA,kBAAkBA,CAAA,EAAG;IAC5B,IAAI,CAAChF,UAAU,CAACQ,SAAS,IAAIR,UAAU,CAACiB,cAAc,EAAE;IAExD,MAAMZ,SAAS,GAAGX,IAAI,CAACS,KAAK,CAACsK,qBAAqB,CAAC,OAAO,CAAC;IAE3D7I,QAAQ,CAAC;MAAEX,cAAc,EAAE,IAAI;MAAEZ;IAAU,CAAC,CAAC;IAK7C,IAAI,CAACP,WAAW,CAACwG,0BAA0B,EAAE;MAC3CtG,UAAU,CAACc,IAAI,CAAC0F,OAAO,CACrBnH,WAAC,CAACuL,mBAAmB,CAAC,KAAK,EAAE,CAC3BvL,WAAC,CAACwL,kBAAkB,CAClBxK,SAAS,EACThB,WAAC,CAACoD,cAAc,CAAC,IAAA0I,iCAAoB,EAACnL,UAAU,CAACL,IAAI,CAAC,EAAE,CACtDN,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,CACjC,CACH,CAAC,CACF,CACH,CAAC;IACH;IAEAe,UAAU,CAACc,IAAI,CAAC0F,OAAO,CACrBnH,WAAC,CAAC2G,mBAAmB,CACnB3G,WAAC,CAACoD,cAAc,CACdzC,UAAU,CAACL,IAAI,CAAC+C,SAAS,CACvB1C,UAAU,CAACH,OAAO,GAAG,eAAe,GAAG,UACzC,CAAC,EACD,CAACR,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,EAAEI,WAAC,CAACE,SAAS,CAACS,UAAU,CAACM,SAAS,CAAC,CACtE,CACF,CACF,CAAC;EACH;EAEA,SAAS8K,kBAAkBA,CAAA,EAAG;IAC5B,MAAM;MAAEhK,WAAW;MAAEjC,IAAI;MAAEgB;IAAM,CAAC,GAAGH,UAAU;IAE/C,KAAK,MAAMqL,IAAI,IAAIlM,IAAI,CAAC2B,IAAI,CAACA,IAAI,EAAE;MACjC,IAAI,CAACzB,WAAC,CAAC2E,aAAa,CAACqH,IAAI,CAAC,IAAI,CAACA,IAAI,CAACpB,QAAQ,EAAE;MAC9C,IAAI9J,KAAK,CAACmL,MAAM,CAACD,IAAI,CAAC7F,GAAG,EAAsB,IAAI,CAAC,EAAE;MAEtD,MAAMkE,EAAE,GAAGvJ,KAAK,CAACoL,gCAAgC,CAACF,IAAI,CAAC7F,GAAG,CAAC;MAC3DpE,WAAW,CAACyI,GAAG,CAACH,EAAE,CAAC7C,IAAI,EAAEwE,IAAI,CAAC7F,GAAG,CAAC;MAElC6F,IAAI,CAAC7F,GAAG,GAAGkE,EAAE;IACf;EACF;EAEA,SAAS8B,sBAAsBA,CAAA,EAAG;IAChC,MAAM;MAAElL,SAAS;MAAEc;IAAY,CAAC,GAAGpB,UAAU;IAC7C,MAAMyL,aAAa,GAAG,EAAE;IACxB,MAAMC,WAAW,GAAG,EAAE;IAEtB,IAAI1L,UAAU,CAACQ,SAAS,EAAE;MACxB,IAAImL,GAAG,GAAGtM,WAAC,CAACE,SAAS,CAACe,SAAS,CAAC;MAChC,IAAIN,UAAU,CAACS,aAAa,EAAE;QAC5BkL,GAAG,GAAGtM,WAAC,CAACoD,cAAc,CAACzC,UAAU,CAACL,IAAI,CAAC+C,SAAS,CAAC,iBAAiB,CAAC,EAAE,CACnEiJ,GAAG,CACJ,CAAC;QACF,IAAAC,6BAAc,EAACD,GAAG,CAAC;MACrB;MAEA,MAAME,KAAK,GACT7L,UAAU,CAACG,KAAK,CAACoL,gCAAgC,CAACjL,SAAS,CAAC;MAE9DmL,aAAa,CAACnJ,IAAI,CAACuJ,KAAK,CAAC;MACzBH,WAAW,CAACpJ,IAAI,CAACqJ,GAAG,CAAC;MAErB/J,QAAQ,CAAC;QAAEtB,SAAS,EAAEjB,WAAC,CAACE,SAAS,CAACsM,KAAK;MAAE,CAAC,CAAC;IAC7C;IAEA,KAAK,MAAM,CAAChF,IAAI,EAAEsC,KAAK,CAAC,IAAI/H,WAAW,EAAE;MACvCqK,aAAa,CAACnJ,IAAI,CAACjD,WAAC,CAACkE,UAAU,CAACsD,IAAI,CAAC,CAAC;MACtC6E,WAAW,CAACpJ,IAAI,CAAC6G,KAAK,CAAC;IACzB;IAEA,OAAO;MAAEsC,aAAa;MAAEC;IAAY,CAAC;EACvC;EAEA,SAASI,gBAAgBA,CACvBpM,IAAuB,EACvBC,IAAU,EACVC,cAAmC,EACnCC,OAAgB,EAChB;IACA+B,QAAQ,CAAC;MACP3B,MAAM,EAAEP,IAAI,CAACO,MAAM;MACnBE,KAAK,EAAET,IAAI,CAACS,KAAK;MACjBhB,IAAI,EAAEO,IAAI,CAACP,IAAI;MACfO,IAAI;MACJC,IAAI;MACJE;IACF,CAAC,CAAC;IAEF+B,QAAQ,CAAC;MACPxB,OAAO,EAAEJ,UAAU,CAACb,IAAI,CAACuK,EAAE;MAE3BzK,QAAQ,EAAEe,UAAU,CAACb,IAAI,CAACuK,EAAE,GACxBrK,WAAC,CAACkE,UAAU,CAACvD,UAAU,CAACb,IAAI,CAACuK,EAAE,CAAC7C,IAAI,CAAC,GACrC7G,UAAU,CAACG,KAAK,CAACsK,qBAAqB,CAAC,OAAO,CAAC;MACnDnK,SAAS,EAAEN,UAAU,CAACb,IAAI,CAAC4M,UAAU;MACrCvL,SAAS,EAAE,CAAC,CAACR,UAAU,CAACb,IAAI,CAAC4M,UAAU;MACvC7M,eAAe,EAAEG,WAAC,CAAC+D,cAAc,CAAC,EAAE;IACtC,CAAC,CAAC;IAEFxB,QAAQ,CAAC;MACPnB,aAAa,EACXpB,WAAC,CAACsH,YAAY,CAAC3G,UAAU,CAACM,SAAS,CAAC,IACpCV,cAAc,CAACgK,GAAG,CAAC5J,UAAU,CAACM,SAAS,CAACuG,IAAI,CAAC,IAC7C,CAAC7G,UAAU,CAACG,KAAK,CAAC6L,UAAU,CAC1BhM,UAAU,CAACM,SAAS,CAACuG,IAAI,EACT,IAClB;IACJ,CAAC,CAAC;IAEF,MAAM;MAAE5H,QAAQ;MAAEE,IAAI;MAAED;IAAgB,CAAC,GAAGc,UAAU;IAEtD4B,QAAQ,CAAC;MACPlB,SAAS,EAAE1B,gBAAgB,CAACC,QAAQ,EAAEC,eAAe,EAAEC,IAAI;IAC7D,CAAC,CAAC;IAEFiM,kBAAkB,CAAC,CAAC;IAEpB,MAAM;MAAEtK;IAAK,CAAC,GAAGd,UAAU;IAC3B,MAAM;MAAEyL,aAAa;MAAEC;IAAY,CAAC,GAAGF,sBAAsB,CAAC,CAAC;IAE/DhI,SAAS,CAAC,CAAC;IAGX,IAAI,CAAC1D,WAAW,CAACmM,YAAY,EAAE;MAC7B/M,eAAe,CAAC4B,IAAI,CAAC0F,OAAO,CAC1BnH,WAAC,CAAC2G,mBAAmB,CACnB3G,WAAC,CAACoD,cAAc,CAACzC,UAAU,CAACL,IAAI,CAAC+C,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAC5DrD,WAAC,CAACoH,cAAc,CAAC,CAAC,EAClBpH,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,CACjC,CACH,CACF,CAAC;IACH;IAEA,MAAMiN,QAAQ,GAAGxM,IAAI,CAACyM,cAAc,CAAC,CAAC;IACtC,IAAIC,eAAe,GAAGpM,UAAU,CAACI,OAAO,IAAIU,IAAI,CAACqE,MAAM,KAAK,CAAC;IAC7D,IAAIiH,eAAe,IAAI,CAACF,QAAQ,EAAE;MAChC,KAAK,MAAML,KAAK,IAAI7L,UAAU,CAACU,SAAS,CAACqC,MAAM,EAAE;QAI/C,IAAI,CAAC1D,WAAC,CAACsH,YAAY,CAACkF,KAAK,CAAC,EAAE;UAC1BO,eAAe,GAAG,KAAK;UACvB;QACF;MACF;IACF;IAEA,MAAMrB,UAAU,GAAGqB,eAAe,GAC7BtL,IAAI,CAAC,CAAC,CAAC,CAAkDA,IAAI,CAC3DiK,UAAU,GACb,EAAE;IACN,IAAI,CAACmB,QAAQ,EAAE;MACbnB,UAAU,CAACzI,IAAI,CAACjD,WAAC,CAACgN,SAAS,CAAChN,WAAC,CAACiN,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC;IAChE;IAEA,IAAIF,eAAe,EAAE;MAEnB,MAAM7B,IAAI,GAAGlL,WAAC,CAACiK,YAAY,CACzBxI,IAAI,CAAC,CAAC,CACR,CAAC;MACD,OAAOd,UAAU,CAACH,OAAO,GAAG0K,IAAI,GAAGhI,iBAAiB,CAAC,CAACgI,IAAI,CAAC,CAAC;IAC9D;IAEA,IAAIlC,SAAuB,GAAGhJ,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC;IAC9D,IAAI,CAACe,UAAU,CAACkB,iBAAiB,IAAI,CAAClB,UAAU,CAACH,OAAO,EAAE;MACxDwI,SAAS,GAAG9F,iBAAiB,CAAC,CAAC8F,SAAS,CAAC,CAAC;IAC5C;IAEAvH,IAAI,CAACwB,IAAI,CAACjD,WAAC,CAACkI,eAAe,CAACc,SAAS,CAAC,CAAC;IACvC,MAAMjB,SAAS,GAAG/H,WAAC,CAACkN,uBAAuB,CACzCd,aAAa,EACbpM,WAAC,CAAC+D,cAAc,CAACtC,IAAI,EAAEiK,UAAU,CACnC,CAAC;IACD,OAAO1L,WAAC,CAACoD,cAAc,CAAC2E,SAAS,EAAEsE,WAAW,CAAC;EACjD;EAEA,OAAOI,gBAAgB,CAACpM,IAAI,EAAEC,IAAI,EAAEC,cAAc,EAAEC,OAAO,CAAC;AAC9D"}