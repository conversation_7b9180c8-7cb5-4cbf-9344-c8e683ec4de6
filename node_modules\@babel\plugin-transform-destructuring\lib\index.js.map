{"version": 3, "file": "index.js", "sources": ["../src/util.ts", "../src/index.ts"], "sourcesContent": ["import { types as t } from \"@babel/core\";\nimport type { File } from \"@babel/core\";\nimport type { Scope, NodePath } from \"@babel/traverse\";\nimport type { TraversalAncestors } from \"@babel/types\";\n\nfunction isPureVoid(node: t.Node) {\n  return (\n    t.isUnaryExpression(node) &&\n    node.operator === \"void\" &&\n    t.isPureish(node.argument)\n  );\n}\n\nexport function unshiftForXStatementBody(\n  statementPath: NodePath<t.ForXStatement>,\n  newStatements: t.Statement[],\n) {\n  statementPath.ensureBlock();\n  const { scope, node } = statementPath;\n  const bodyScopeBindings = statementPath.get(\"body\").scope.bindings;\n  const hasShadowedBlockScopedBindings = Object.keys(bodyScopeBindings).some(\n    name => scope.hasBinding(name),\n  );\n\n  if (hasShadowedBlockScopedBindings) {\n    // handle shadowed variables referenced in computed keys:\n    // var a = 0;for (const { #x: x, [a++]: y } of z) { const a = 1; }\n    node.body = t.blockStatement([...newStatements, node.body]);\n  } else {\n    node.body.body.unshift(...newStatements);\n  }\n}\n\n/**\n * Test if an ArrayPattern's elements contain any RestElements.\n */\n\nfunction hasArrayRest(pattern: t.ArrayPattern) {\n  return pattern.elements.some(elem => t.isRestElement(elem));\n}\n\n/**\n * Test if an ObjectPattern's properties contain any RestElements.\n */\n\nfunction hasObjectRest(pattern: t.ObjectPattern) {\n  return pattern.properties.some(prop => t.isRestElement(prop));\n}\n\ninterface UnpackableArrayExpression extends t.ArrayExpression {\n  elements: (null | t.Expression)[];\n}\n\nconst STOP_TRAVERSAL = {};\n\ninterface ArrayUnpackVisitorState {\n  deopt: boolean;\n  bindings: Record<string, t.Identifier>;\n}\n\n// NOTE: This visitor is meant to be used via t.traverse\nconst arrayUnpackVisitor = (\n  node: t.Node,\n  ancestors: TraversalAncestors,\n  state: ArrayUnpackVisitorState,\n) => {\n  if (!ancestors.length) {\n    // Top-level node: this is the array literal.\n    return;\n  }\n\n  if (\n    t.isIdentifier(node) &&\n    t.isReferenced(node, ancestors[ancestors.length - 1].node) &&\n    state.bindings[node.name]\n  ) {\n    state.deopt = true;\n    throw STOP_TRAVERSAL;\n  }\n};\n\nexport type DestructuringTransformerNode =\n  | t.VariableDeclaration\n  | t.ExpressionStatement\n  | t.ReturnStatement;\n\ninterface DestructuringTransformerOption {\n  blockHoist?: number;\n  operator?: t.AssignmentExpression[\"operator\"];\n  nodes?: DestructuringTransformerNode[];\n  kind?: t.VariableDeclaration[\"kind\"];\n  scope: Scope;\n  arrayLikeIsIterable: boolean;\n  iterableIsArray: boolean;\n  objectRestNoSymbols: boolean;\n  useBuiltIns: boolean;\n  addHelper: File[\"addHelper\"];\n}\nexport class DestructuringTransformer {\n  private blockHoist: number;\n  private operator: t.AssignmentExpression[\"operator\"];\n  arrayRefSet: Set<string>;\n  private nodes: DestructuringTransformerNode[];\n  private scope: Scope;\n  private kind: t.VariableDeclaration[\"kind\"];\n  private iterableIsArray: boolean;\n  private arrayLikeIsIterable: boolean;\n  private objectRestNoSymbols: boolean;\n  private useBuiltIns: boolean;\n  private addHelper: File[\"addHelper\"];\n  constructor(opts: DestructuringTransformerOption) {\n    this.blockHoist = opts.blockHoist;\n    this.operator = opts.operator;\n    this.arrayRefSet = new Set();\n    this.nodes = opts.nodes || [];\n    this.scope = opts.scope;\n    this.kind = opts.kind;\n    this.iterableIsArray = opts.iterableIsArray;\n    this.arrayLikeIsIterable = opts.arrayLikeIsIterable;\n    this.objectRestNoSymbols = opts.objectRestNoSymbols;\n    this.useBuiltIns = opts.useBuiltIns;\n    this.addHelper = opts.addHelper;\n  }\n\n  getExtendsHelper() {\n    return this.useBuiltIns\n      ? t.memberExpression(t.identifier(\"Object\"), t.identifier(\"assign\"))\n      : this.addHelper(\"extends\");\n  }\n\n  buildVariableAssignment(\n    id: t.AssignmentExpression[\"left\"],\n    init: t.Expression,\n  ) {\n    let op = this.operator;\n    if (t.isMemberExpression(id)) op = \"=\";\n\n    let node: t.ExpressionStatement | t.VariableDeclaration;\n\n    if (op) {\n      node = t.expressionStatement(\n        t.assignmentExpression(\n          op,\n          id,\n          t.cloneNode(init) || this.scope.buildUndefinedNode(),\n        ),\n      );\n    } else {\n      let nodeInit: t.Expression;\n\n      if ((this.kind === \"const\" || this.kind === \"using\") && init === null) {\n        nodeInit = this.scope.buildUndefinedNode();\n      } else {\n        nodeInit = t.cloneNode(init);\n      }\n\n      node = t.variableDeclaration(this.kind, [\n        t.variableDeclarator(id, nodeInit),\n      ]);\n    }\n\n    //@ts-expect-error(todo): document block hoist property\n    node._blockHoist = this.blockHoist;\n\n    return node;\n  }\n\n  buildVariableDeclaration(id: t.Identifier, init: t.Expression) {\n    const declar = t.variableDeclaration(\"var\", [\n      t.variableDeclarator(t.cloneNode(id), t.cloneNode(init)),\n    ]);\n    // @ts-expect-error todo(flow->ts): avoid mutations\n    declar._blockHoist = this.blockHoist;\n    return declar;\n  }\n\n  push(id: t.LVal, _init: t.Expression | null) {\n    const init = t.cloneNode(_init);\n    if (t.isObjectPattern(id)) {\n      this.pushObjectPattern(id, init);\n    } else if (t.isArrayPattern(id)) {\n      this.pushArrayPattern(id, init);\n    } else if (t.isAssignmentPattern(id)) {\n      this.pushAssignmentPattern(id, init);\n    } else {\n      this.nodes.push(this.buildVariableAssignment(id, init));\n    }\n  }\n\n  toArray(node: t.Expression, count?: boolean | number) {\n    if (\n      this.iterableIsArray ||\n      (t.isIdentifier(node) && this.arrayRefSet.has(node.name))\n    ) {\n      return node;\n    } else {\n      return this.scope.toArray(node, count, this.arrayLikeIsIterable);\n    }\n  }\n\n  pushAssignmentPattern(\n    { left, right }: t.AssignmentPattern,\n    valueRef: t.Expression | null,\n  ) {\n    // handle array init with void 0. This also happens when\n    // the value was originally a hole.\n    // const [x = 42] = [void 0,];\n    // -> const x = 42;\n    if (isPureVoid(valueRef)) {\n      this.push(left, right);\n      return;\n    }\n\n    // we need to assign the current value of the assignment to avoid evaluating\n    // it more than once\n    const tempId = this.scope.generateUidIdentifierBasedOnNode(valueRef);\n\n    this.nodes.push(this.buildVariableDeclaration(tempId, valueRef));\n\n    const tempConditional = t.conditionalExpression(\n      t.binaryExpression(\n        \"===\",\n        t.cloneNode(tempId),\n        this.scope.buildUndefinedNode(),\n      ),\n      right,\n      t.cloneNode(tempId),\n    );\n\n    if (t.isPattern(left)) {\n      let patternId;\n      let node;\n\n      if (\n        this.kind === \"const\" ||\n        this.kind === \"let\" ||\n        this.kind === \"using\"\n      ) {\n        patternId = this.scope.generateUidIdentifier(tempId.name);\n        node = this.buildVariableDeclaration(patternId, tempConditional);\n      } else {\n        patternId = tempId;\n\n        node = t.expressionStatement(\n          t.assignmentExpression(\"=\", t.cloneNode(tempId), tempConditional),\n        );\n      }\n\n      this.nodes.push(node);\n      this.push(left, patternId);\n    } else {\n      this.nodes.push(this.buildVariableAssignment(left, tempConditional));\n    }\n  }\n\n  pushObjectRest(\n    pattern: t.ObjectPattern,\n    objRef: t.Expression,\n    spreadProp: t.RestElement,\n    spreadPropIndex: number,\n  ) {\n    const value = buildObjectExcludingKeys(\n      pattern.properties.slice(0, spreadPropIndex) as t.ObjectProperty[],\n      objRef,\n      this.scope,\n      name => this.addHelper(name),\n      this.objectRestNoSymbols,\n      this.useBuiltIns,\n    );\n    this.nodes.push(this.buildVariableAssignment(spreadProp.argument, value));\n  }\n\n  pushObjectProperty(prop: t.ObjectProperty, propRef: t.Expression) {\n    if (t.isLiteral(prop.key)) prop.computed = true;\n\n    const pattern = prop.value as t.LVal;\n    const objRef = t.memberExpression(\n      t.cloneNode(propRef),\n      prop.key,\n      prop.computed,\n    );\n\n    if (t.isPattern(pattern)) {\n      this.push(pattern, objRef);\n    } else {\n      this.nodes.push(this.buildVariableAssignment(pattern, objRef));\n    }\n  }\n\n  pushObjectPattern(pattern: t.ObjectPattern, objRef: t.Expression) {\n    // https://github.com/babel/babel/issues/681\n\n    if (!pattern.properties.length) {\n      this.nodes.push(\n        t.expressionStatement(\n          t.callExpression(\n            this.addHelper(\"objectDestructuringEmpty\"),\n            isPureVoid(objRef) ? [] : [objRef],\n          ),\n        ),\n      );\n      return;\n    }\n\n    // if we have more than one properties in this pattern and the objectRef is a\n    // member expression then we need to assign it to a temporary variable so it's\n    // only evaluated once\n\n    if (pattern.properties.length > 1 && !this.scope.isStatic(objRef)) {\n      const temp = this.scope.generateUidIdentifierBasedOnNode(objRef);\n      this.nodes.push(this.buildVariableDeclaration(temp, objRef));\n      objRef = temp;\n    }\n\n    // Replace impure computed key expressions if we have a rest parameter\n    if (hasObjectRest(pattern)) {\n      let copiedPattern: t.ObjectPattern;\n      for (let i = 0; i < pattern.properties.length; i++) {\n        const prop = pattern.properties[i];\n        if (t.isRestElement(prop)) {\n          break;\n        }\n        const key = prop.key;\n        if (prop.computed && !this.scope.isPure(key)) {\n          const name = this.scope.generateUidIdentifierBasedOnNode(key);\n          this.nodes.push(\n            //@ts-expect-error PrivateName has been handled by destructuring-private\n            this.buildVariableDeclaration(name, key),\n          );\n          if (!copiedPattern) {\n            copiedPattern = pattern = {\n              ...pattern,\n              properties: pattern.properties.slice(),\n            };\n          }\n          copiedPattern.properties[i] = {\n            ...prop,\n            key: name,\n          };\n        }\n      }\n    }\n\n    for (let i = 0; i < pattern.properties.length; i++) {\n      const prop = pattern.properties[i];\n      if (t.isRestElement(prop)) {\n        this.pushObjectRest(pattern, objRef, prop, i);\n      } else {\n        this.pushObjectProperty(prop, objRef);\n      }\n    }\n  }\n\n  canUnpackArrayPattern(\n    pattern: t.ArrayPattern,\n    arr: t.Expression,\n  ): arr is UnpackableArrayExpression {\n    // not an array so there's no way we can deal with this\n    if (!t.isArrayExpression(arr)) return false;\n\n    // pattern has less elements than the array and doesn't have a rest so some\n    // elements wont be evaluated\n    if (pattern.elements.length > arr.elements.length) return;\n    if (\n      pattern.elements.length < arr.elements.length &&\n      !hasArrayRest(pattern)\n    ) {\n      return false;\n    }\n\n    for (const elem of pattern.elements) {\n      // deopt on holes\n      if (!elem) return false;\n\n      // deopt on member expressions as they may be included in the RHS\n      if (t.isMemberExpression(elem)) return false;\n    }\n\n    for (const elem of arr.elements) {\n      // deopt on spread elements\n      if (t.isSpreadElement(elem)) return false;\n\n      // deopt call expressions as they might change values of LHS variables\n      if (t.isCallExpression(elem)) return false;\n\n      // deopt on member expressions as they may be getter/setters and have side-effects\n      if (t.isMemberExpression(elem)) return false;\n    }\n\n    // deopt on reference to left side identifiers\n    const bindings = t.getBindingIdentifiers(pattern);\n    const state: ArrayUnpackVisitorState = { deopt: false, bindings };\n\n    try {\n      t.traverse(arr, arrayUnpackVisitor, state);\n    } catch (e) {\n      if (e !== STOP_TRAVERSAL) throw e;\n    }\n\n    return !state.deopt;\n  }\n\n  pushUnpackedArrayPattern(\n    pattern: t.ArrayPattern,\n    arr: UnpackableArrayExpression,\n  ) {\n    const holeToUndefined = (el: t.Expression) =>\n      el ?? this.scope.buildUndefinedNode();\n\n    for (let i = 0; i < pattern.elements.length; i++) {\n      const elem = pattern.elements[i];\n      if (t.isRestElement(elem)) {\n        this.push(\n          elem.argument,\n          t.arrayExpression(arr.elements.slice(i).map(holeToUndefined)),\n        );\n      } else {\n        this.push(elem, holeToUndefined(arr.elements[i]));\n      }\n    }\n  }\n\n  pushArrayPattern(pattern: t.ArrayPattern, arrayRef: t.Expression | null) {\n    if (arrayRef === null) {\n      this.nodes.push(\n        t.expressionStatement(\n          t.callExpression(this.addHelper(\"objectDestructuringEmpty\"), []),\n        ),\n      );\n      return;\n    }\n    if (!pattern.elements) return;\n\n    // optimise basic array destructuring of an array expression\n    //\n    // we can't do this to a pattern of unequal size to it's right hand\n    // array expression as then there will be values that wont be evaluated\n    //\n    // eg: let [a, b] = [1, 2];\n\n    if (this.canUnpackArrayPattern(pattern, arrayRef)) {\n      this.pushUnpackedArrayPattern(pattern, arrayRef);\n      return;\n    }\n\n    // if we have a rest then we need all the elements so don't tell\n    // `scope.toArray` to only get a certain amount\n\n    const count = !hasArrayRest(pattern) && pattern.elements.length;\n\n    // so we need to ensure that the `arrayRef` is an array, `scope.toArray` will\n    // return a locally bound identifier if it's been inferred to be an array,\n    // otherwise it'll be a call to a helper that will ensure it's one\n\n    const toArray = this.toArray(arrayRef, count);\n\n    if (t.isIdentifier(toArray)) {\n      // we've been given an identifier so it must have been inferred to be an\n      // array\n      arrayRef = toArray;\n    } else {\n      arrayRef = this.scope.generateUidIdentifierBasedOnNode(arrayRef);\n      this.arrayRefSet.add(arrayRef.name);\n      this.nodes.push(this.buildVariableDeclaration(arrayRef, toArray));\n    }\n\n    for (let i = 0; i < pattern.elements.length; i++) {\n      const elem = pattern.elements[i];\n\n      // hole\n      if (!elem) continue;\n\n      let elemRef;\n\n      if (t.isRestElement(elem)) {\n        elemRef = this.toArray(arrayRef);\n        elemRef = t.callExpression(\n          t.memberExpression(elemRef, t.identifier(\"slice\")),\n          [t.numericLiteral(i)],\n        );\n\n        // set the element to the rest element argument since we've dealt with it\n        // being a rest already\n        this.push(elem.argument, elemRef);\n      } else {\n        elemRef = t.memberExpression(arrayRef, t.numericLiteral(i), true);\n        this.push(elem, elemRef);\n      }\n    }\n  }\n\n  init(pattern: t.LVal, ref: t.Expression) {\n    // trying to destructure a value that we can't evaluate more than once so we\n    // need to save it to a variable\n\n    if (!t.isArrayExpression(ref) && !t.isMemberExpression(ref)) {\n      const memo = this.scope.maybeGenerateMemoised(ref, true);\n      if (memo) {\n        this.nodes.push(this.buildVariableDeclaration(memo, t.cloneNode(ref)));\n        ref = memo;\n      }\n    }\n\n    this.push(pattern, ref);\n\n    return this.nodes;\n  }\n}\n\ninterface ExcludingKey {\n  key: t.Expression | t.PrivateName;\n  computed: boolean;\n}\n\nexport function buildObjectExcludingKeys<T extends ExcludingKey>(\n  excludedKeys: T[],\n  objRef: t.Expression,\n  scope: Scope,\n  addHelper: File[\"addHelper\"],\n  objectRestNoSymbols: boolean,\n  useBuiltIns: boolean,\n): t.CallExpression {\n  // get all the keys that appear in this object before the current spread\n\n  const keys = [];\n  let allLiteral = true;\n  let hasTemplateLiteral = false;\n  for (let i = 0; i < excludedKeys.length; i++) {\n    const prop = excludedKeys[i];\n    const key = prop.key;\n    if (t.isIdentifier(key) && !prop.computed) {\n      keys.push(t.stringLiteral(key.name));\n    } else if (t.isTemplateLiteral(key)) {\n      keys.push(t.cloneNode(key));\n      hasTemplateLiteral = true;\n    } else if (t.isLiteral(key)) {\n      // @ts-expect-error todo(flow->ts) NullLiteral\n      keys.push(t.stringLiteral(String(key.value)));\n    } else if (t.isPrivateName(key)) {\n      // private key is not enumerable\n    } else {\n      keys.push(t.cloneNode(key));\n      allLiteral = false;\n    }\n  }\n\n  let value;\n  if (keys.length === 0) {\n    const extendsHelper = useBuiltIns\n      ? t.memberExpression(t.identifier(\"Object\"), t.identifier(\"assign\"))\n      : addHelper(\"extends\");\n    value = t.callExpression(extendsHelper, [\n      t.objectExpression([]),\n      t.sequenceExpression([\n        t.callExpression(addHelper(\"objectDestructuringEmpty\"), [\n          t.cloneNode(objRef),\n        ]),\n        t.cloneNode(objRef),\n      ]),\n    ]);\n  } else {\n    let keyExpression: t.Expression = t.arrayExpression(keys);\n\n    if (!allLiteral) {\n      keyExpression = t.callExpression(\n        t.memberExpression(keyExpression, t.identifier(\"map\")),\n        [addHelper(\"toPropertyKey\")],\n      );\n    } else if (!hasTemplateLiteral && !t.isProgram(scope.block)) {\n      // Hoist definition of excluded keys, so that it's not created each time.\n      const programScope = scope.getProgramParent();\n      const id = programScope.generateUidIdentifier(\"excluded\");\n\n      programScope.push({\n        id,\n        init: keyExpression,\n        kind: \"const\",\n      });\n\n      keyExpression = t.cloneNode(id);\n    }\n\n    value = t.callExpression(\n      addHelper(`objectWithoutProperties${objectRestNoSymbols ? \"Loose\" : \"\"}`),\n      [t.cloneNode(objRef), keyExpression],\n    );\n  }\n  return value;\n}\n\nexport function convertVariableDeclaration(\n  path: NodePath<t.VariableDeclaration>,\n  addHelper: File[\"addHelper\"],\n  arrayLikeIsIterable: boolean,\n  iterableIsArray: boolean,\n  objectRestNoSymbols: boolean,\n  useBuiltIns: boolean,\n) {\n  const { node, scope } = path;\n\n  const nodeKind = node.kind;\n  const nodeLoc = node.loc;\n  const nodes = [];\n\n  for (let i = 0; i < node.declarations.length; i++) {\n    const declar = node.declarations[i];\n\n    const patternId = declar.init;\n    const pattern = declar.id;\n\n    const destructuring: DestructuringTransformer =\n      new DestructuringTransformer({\n        // @ts-expect-error(todo): avoid internal properties access\n        blockHoist: node._blockHoist,\n        nodes: nodes,\n        scope: scope,\n        kind: node.kind,\n        iterableIsArray,\n        arrayLikeIsIterable,\n        useBuiltIns,\n        objectRestNoSymbols,\n        addHelper,\n      });\n\n    if (t.isPattern(pattern)) {\n      destructuring.init(pattern, patternId);\n\n      if (+i !== node.declarations.length - 1) {\n        // we aren't the last declarator so let's just make the\n        // last transformed node inherit from us\n        t.inherits(nodes[nodes.length - 1], declar);\n      }\n    } else {\n      nodes.push(\n        t.inherits(\n          destructuring.buildVariableAssignment(pattern, patternId),\n          declar,\n        ),\n      );\n    }\n  }\n\n  let tail: t.VariableDeclaration | null = null;\n  let nodesOut = [];\n  for (const node of nodes) {\n    if (t.isVariableDeclaration(node)) {\n      if (tail !== null) {\n        // Create a single compound declarations\n        tail.declarations.push(...node.declarations);\n        continue;\n      } else {\n        // Make sure the original node kind is used for each compound declaration\n        node.kind = nodeKind;\n        tail = node;\n      }\n    } else {\n      tail = null;\n    }\n    // Propagate the original declaration node's location\n    if (!node.loc) {\n      node.loc = nodeLoc;\n    }\n    nodesOut.push(node);\n  }\n\n  if (\n    nodesOut.length === 2 &&\n    t.isVariableDeclaration(nodesOut[0]) &&\n    t.isExpressionStatement(nodesOut[1]) &&\n    t.isCallExpression(nodesOut[1].expression) &&\n    nodesOut[0].declarations.length === 1\n  ) {\n    // This can only happen when we generate this code:\n    //    var _ref = DESTRUCTURED_VALUE;\n    //     babelHelpers.objectDestructuringEmpty(_ref);\n    // Since pushing those two statements to the for loop .init will require an IIFE,\n    // we can optimize them to\n    //     babelHelpers.objectDestructuringEmpty(DESTRUCTURED_VALUE);\n    const expr = nodesOut[1].expression;\n    expr.arguments = [nodesOut[0].declarations[0].init];\n    nodesOut = [expr];\n  } else {\n    // We must keep nodes all are expressions or statements, so `replaceWithMultiple` can work.\n    if (\n      t.isForStatement(path.parent, { init: node }) &&\n      !nodesOut.some(v => t.isVariableDeclaration(v))\n    ) {\n      for (let i = 0; i < nodesOut.length; i++) {\n        const node: t.Node = nodesOut[i];\n        if (t.isExpressionStatement(node)) {\n          nodesOut[i] = node.expression;\n        }\n      }\n    }\n  }\n\n  if (nodesOut.length === 1) {\n    path.replaceWith(nodesOut[0]);\n  } else {\n    path.replaceWithMultiple(nodesOut);\n  }\n  scope.crawl();\n}\n\nexport function convertAssignmentExpression(\n  path: NodePath<t.AssignmentExpression>,\n  addHelper: File[\"addHelper\"],\n  arrayLikeIsIterable: boolean,\n  iterableIsArray: boolean,\n  objectRestNoSymbols: boolean,\n  useBuiltIns: boolean,\n) {\n  const { node, scope, parentPath } = path;\n\n  const nodes: DestructuringTransformerNode[] = [];\n\n  const destructuring = new DestructuringTransformer({\n    operator: node.operator,\n    scope: scope,\n    nodes: nodes,\n    arrayLikeIsIterable,\n    iterableIsArray,\n    objectRestNoSymbols,\n    useBuiltIns,\n    addHelper,\n  });\n\n  let ref: t.Identifier | void;\n  if (\n    (!parentPath.isExpressionStatement() &&\n      !parentPath.isSequenceExpression()) ||\n    path.isCompletionRecord()\n  ) {\n    ref = scope.generateUidIdentifierBasedOnNode(node.right, \"ref\");\n\n    nodes.push(\n      t.variableDeclaration(\"var\", [t.variableDeclarator(ref, node.right)]),\n    );\n\n    if (t.isArrayExpression(node.right)) {\n      destructuring.arrayRefSet.add(ref.name);\n    }\n  }\n\n  destructuring.init(node.left, ref || node.right);\n\n  if (ref) {\n    if (parentPath.isArrowFunctionExpression()) {\n      path.replaceWith(t.blockStatement([]));\n      nodes.push(t.returnStatement(t.cloneNode(ref)));\n    } else {\n      nodes.push(t.expressionStatement(t.cloneNode(ref)));\n    }\n  }\n\n  path.replaceWithMultiple(nodes);\n  scope.crawl();\n}\n", "import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t } from \"@babel/core\";\nimport {\n  DestructuringTransformer,\n  convertVariableDeclaration,\n  convertAssignmentExpression,\n  unshiftForXStatementBody,\n  type DestructuringTransformerNode,\n} from \"./util\";\nexport { buildObjectExcludingKeys, unshiftForXStatementBody } from \"./util\";\nimport type { NodePath } from \"@babel/traverse\";\n\n/**\n * Test if a VariableDeclaration's declarations contains any Patterns.\n */\n\nfunction variableDeclarationHasPattern(node: t.VariableDeclaration) {\n  for (const declar of node.declarations) {\n    if (t.isPattern(declar.id)) {\n      return true;\n    }\n  }\n  return false;\n}\n\nexport interface Options {\n  allowArrayLike?: boolean;\n  loose?: boolean;\n  useBuiltIns?: boolean;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(7);\n\n  const { useBuiltIns = false } = options;\n\n  const iterableIsArray =\n    api.assumption(\"iterableIsArray\") ?? options.loose ?? false;\n  const arrayLikeIsIterable =\n    options.allowArrayLike ?? api.assumption(\"arrayLikeIsIterable\") ?? false;\n  const objectRestNoSymbols =\n    api.assumption(\"objectRestNoSymbols\") ?? options.loose ?? false;\n\n  return {\n    name: \"transform-destructuring\",\n\n    visitor: {\n      ExportNamedDeclaration(path) {\n        const declaration = path.get(\"declaration\");\n        if (!declaration.isVariableDeclaration()) return;\n        if (!variableDeclarationHasPattern(declaration.node)) return;\n\n        const specifiers = [];\n\n        for (const name of Object.keys(path.getOuterBindingIdentifiers())) {\n          specifiers.push(\n            t.exportSpecifier(t.identifier(name), t.identifier(name)),\n          );\n        }\n\n        // Split the declaration and export list into two declarations so that the variable\n        // declaration can be split up later without needing to worry about not being a\n        // top-level statement.\n        path.replaceWith(declaration.node);\n        path.insertAfter(t.exportNamedDeclaration(null, specifiers));\n        path.scope.crawl();\n      },\n\n      ForXStatement(path: NodePath<t.ForXStatement>) {\n        const { node, scope } = path;\n        const left = node.left;\n\n        if (t.isPattern(left)) {\n          // for ({ length: k } in { abc: 3 });\n\n          const temp = scope.generateUidIdentifier(\"ref\");\n\n          node.left = t.variableDeclaration(\"var\", [\n            t.variableDeclarator(temp),\n          ]);\n\n          path.ensureBlock();\n          const statementBody = path.node.body.body;\n          const nodes = [];\n          // todo: the completion of a for statement can only be observed from\n          // a do block (or eval that we don't support),\n          // but the new do-expression proposal plans to ban iteration ends in the\n          // do block, maybe we can get rid of this\n          if (statementBody.length === 0 && path.isCompletionRecord()) {\n            nodes.unshift(t.expressionStatement(scope.buildUndefinedNode()));\n          }\n\n          nodes.unshift(\n            t.expressionStatement(\n              t.assignmentExpression(\"=\", left, t.cloneNode(temp)),\n            ),\n          );\n\n          unshiftForXStatementBody(path, nodes);\n          scope.crawl();\n          return;\n        }\n\n        if (!t.isVariableDeclaration(left)) return;\n\n        const pattern = left.declarations[0].id;\n        if (!t.isPattern(pattern)) return;\n\n        const key = scope.generateUidIdentifier(\"ref\");\n        node.left = t.variableDeclaration(left.kind, [\n          t.variableDeclarator(key, null),\n        ]);\n\n        const nodes: DestructuringTransformerNode[] = [];\n\n        const destructuring = new DestructuringTransformer({\n          kind: left.kind,\n          scope: scope,\n          nodes: nodes,\n          arrayLikeIsIterable,\n          iterableIsArray,\n          objectRestNoSymbols,\n          useBuiltIns,\n          addHelper: name => this.addHelper(name),\n        });\n\n        destructuring.init(pattern, key);\n\n        unshiftForXStatementBody(path, nodes);\n        scope.crawl();\n      },\n\n      CatchClause({ node, scope }) {\n        const pattern = node.param;\n        if (!t.isPattern(pattern)) return;\n\n        const ref = scope.generateUidIdentifier(\"ref\");\n        node.param = ref;\n\n        const nodes: DestructuringTransformerNode[] = [];\n\n        const destructuring = new DestructuringTransformer({\n          kind: \"let\",\n          scope: scope,\n          nodes: nodes,\n          arrayLikeIsIterable,\n          iterableIsArray,\n          objectRestNoSymbols,\n          useBuiltIns,\n          addHelper: name => this.addHelper(name),\n        });\n        destructuring.init(pattern, ref);\n\n        node.body.body = [...nodes, ...node.body.body];\n        scope.crawl();\n      },\n\n      AssignmentExpression(path, state) {\n        if (!t.isPattern(path.node.left)) return;\n        convertAssignmentExpression(\n          path,\n          name => state.addHelper(name),\n          arrayLikeIsIterable,\n          iterableIsArray,\n          objectRestNoSymbols,\n          useBuiltIns,\n        );\n      },\n\n      VariableDeclaration(path, state) {\n        const { node, parent } = path;\n        if (t.isForXStatement(parent)) return;\n        if (!parent || !path.container) return; // i don't know why this is necessary - TODO\n        if (!variableDeclarationHasPattern(node)) return;\n        convertVariableDeclaration(\n          path,\n          name => state.addHelper(name),\n          arrayLikeIsIterable,\n          iterableIsArray,\n          objectRestNoSymbols,\n          useBuiltIns,\n        );\n      },\n    },\n  };\n});\n"], "names": ["isPureVoid", "node", "t", "isUnaryExpression", "operator", "isPureish", "argument", "unshiftForXStatementBody", "statementPath", "newStatements", "ensureBlock", "scope", "bodyScopeBindings", "get", "bindings", "hasShadowedBlockScopedBindings", "Object", "keys", "some", "name", "hasBinding", "body", "blockStatement", "unshift", "hasArrayRest", "pattern", "elements", "elem", "isRestElement", "hasObjectRest", "properties", "prop", "STOP_TRAVERSAL", "arrayUnpackVisitor", "ancestors", "state", "length", "isIdentifier", "isReferenced", "de<PERSON>t", "DestructuringTransformer", "constructor", "opts", "blockHoist", "arrayRefSet", "nodes", "kind", "iterableIsArray", "arrayLikeIsIterable", "objectRestNoSymbols", "useBuiltIns", "addHelper", "Set", "getExtendsHelper", "memberExpression", "identifier", "buildVariableAssignment", "id", "init", "op", "isMemberExpression", "expressionStatement", "assignmentExpression", "cloneNode", "buildUndefinedNode", "nodeInit", "variableDeclaration", "variableDeclarator", "_blockHoist", "buildVariableDeclaration", "declar", "push", "_init", "isObjectPattern", "pushObjectPattern", "isArrayPattern", "pushArrayPattern", "isAssignmentPattern", "pushAssignmentPattern", "toArray", "count", "has", "left", "right", "valueRef", "tempId", "generateUidIdentifierBasedOnNode", "tempConditional", "conditionalExpression", "binaryExpression", "isPattern", "patternId", "generateUidIdentifier", "pushObjectRest", "objRef", "spreadProp", "spreadPropIndex", "value", "buildObjectExcludingKeys", "slice", "pushObjectProperty", "propRef", "isLiteral", "key", "computed", "callExpression", "isStatic", "temp", "copiedPattern", "i", "isPure", "assign", "canUnpackArrayPattern", "arr", "isArrayExpression", "isSpreadElement", "isCallExpression", "getBindingIdentifiers", "traverse", "e", "pushUnpackedArrayPattern", "holeToUndefined", "el", "arrayExpression", "map", "arrayRef", "add", "elemRef", "numericLiteral", "ref", "memo", "maybeGenerateMemoised", "<PERSON><PERSON><PERSON><PERSON>", "allLiteral", "hasTemplateLiteral", "stringLiteral", "isTemplateLiteral", "String", "isPrivateName", "extendsHelper", "objectExpression", "sequenceExpression", "keyExpression", "isProgram", "block", "programScope", "getProgramParent", "convertVariableDeclaration", "path", "nodeKind", "nodeLoc", "loc", "declarations", "destructuring", "inherits", "tail", "nodesOut", "isVariableDeclaration", "isExpressionStatement", "expression", "expr", "arguments", "isForStatement", "parent", "v", "replaceWith", "replaceWithMultiple", "crawl", "convertAssignmentExpression", "parentPath", "isSequenceExpression", "isCompletionRecord", "isArrowFunctionExpression", "returnStatement", "variableDeclarationHasPattern", "declare", "api", "options", "_ref", "_api$assumption", "_ref2", "_options$allowArrayLi", "_ref3", "_api$assumption2", "assertVersion", "assumption", "loose", "allowArrayLike", "visitor", "ExportNamedDeclaration", "declaration", "specifiers", "getOuterBindingIdentifiers", "exportSpecifier", "insertAfter", "exportNamedDeclaration", "ForXStatement", "statementBody", "CatchClause", "param", "AssignmentExpression", "VariableDeclaration", "isForXStatement", "container"], "mappings": ";;;;;;;AAKA,SAASA,UAAUA,CAACC,IAAY,EAAE;EAChC,OACEC,UAAC,CAACC,iBAAiB,CAACF,IAAI,CAAC,IACzBA,IAAI,CAACG,QAAQ,KAAK,MAAM,IACxBF,UAAC,CAACG,SAAS,CAACJ,IAAI,CAACK,QAAQ,CAAC,CAAA;AAE9B,CAAA;AAEO,SAASC,wBAAwBA,CACtCC,aAAwC,EACxCC,aAA4B,EAC5B;EACAD,aAAa,CAACE,WAAW,EAAE,CAAA;EAC3B,MAAM;IAAEC,KAAK;AAAEV,IAAAA,IAAAA;AAAK,GAAC,GAAGO,aAAa,CAAA;EACrC,MAAMI,iBAAiB,GAAGJ,aAAa,CAACK,GAAG,CAAC,MAAM,CAAC,CAACF,KAAK,CAACG,QAAQ,CAAA;AAClE,EAAA,MAAMC,8BAA8B,GAAGC,MAAM,CAACC,IAAI,CAACL,iBAAiB,CAAC,CAACM,IAAI,CACxEC,IAAI,IAAIR,KAAK,CAACS,UAAU,CAACD,IAAI,CAC/B,CAAC,CAAA;AAED,EAAA,IAAIJ,8BAA8B,EAAE;AAGlCd,IAAAA,IAAI,CAACoB,IAAI,GAAGnB,UAAC,CAACoB,cAAc,CAAC,CAAC,GAAGb,aAAa,EAAER,IAAI,CAACoB,IAAI,CAAC,CAAC,CAAA;AAC7D,GAAC,MAAM;IACLpB,IAAI,CAACoB,IAAI,CAACA,IAAI,CAACE,OAAO,CAAC,GAAGd,aAAa,CAAC,CAAA;AAC1C,GAAA;AACF,CAAA;AAMA,SAASe,YAAYA,CAACC,OAAuB,EAAE;AAC7C,EAAA,OAAOA,OAAO,CAACC,QAAQ,CAACR,IAAI,CAACS,IAAI,IAAIzB,UAAC,CAAC0B,aAAa,CAACD,IAAI,CAAC,CAAC,CAAA;AAC7D,CAAA;AAMA,SAASE,aAAaA,CAACJ,OAAwB,EAAE;AAC/C,EAAA,OAAOA,OAAO,CAACK,UAAU,CAACZ,IAAI,CAACa,IAAI,IAAI7B,UAAC,CAAC0B,aAAa,CAACG,IAAI,CAAC,CAAC,CAAA;AAC/D,CAAA;AAMA,MAAMC,cAAc,GAAG,EAAE,CAAA;AAQzB,MAAMC,kBAAkB,GAAGA,CACzBhC,IAAY,EACZiC,SAA6B,EAC7BC,KAA8B,KAC3B;AACH,EAAA,IAAI,CAACD,SAAS,CAACE,MAAM,EAAE;AAErB,IAAA,OAAA;AACF,GAAA;AAEA,EAAA,IACElC,UAAC,CAACmC,YAAY,CAACpC,IAAI,CAAC,IACpBC,UAAC,CAACoC,YAAY,CAACrC,IAAI,EAAEiC,SAAS,CAACA,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC,CAACnC,IAAI,CAAC,IAC1DkC,KAAK,CAACrB,QAAQ,CAACb,IAAI,CAACkB,IAAI,CAAC,EACzB;IACAgB,KAAK,CAACI,KAAK,GAAG,IAAI,CAAA;AAClB,IAAA,MAAMP,cAAc,CAAA;AACtB,GAAA;AACF,CAAC,CAAA;AAmBM,MAAMQ,wBAAwB,CAAC;EAYpCC,WAAWA,CAACC,IAAoC,EAAE;AAAA,IAAA,IAAA,CAX1CC,UAAU,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACVvC,QAAQ,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAChBwC,WAAW,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACHC,KAAK,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACLlC,KAAK,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACLmC,IAAI,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACJC,eAAe,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACfC,mBAAmB,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACnBC,mBAAmB,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACnBC,WAAW,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACXC,SAAS,GAAA,KAAA,CAAA,CAAA;AAEf,IAAA,IAAI,CAACR,UAAU,GAAGD,IAAI,CAACC,UAAU,CAAA;AACjC,IAAA,IAAI,CAACvC,QAAQ,GAAGsC,IAAI,CAACtC,QAAQ,CAAA;AAC7B,IAAA,IAAI,CAACwC,WAAW,GAAG,IAAIQ,GAAG,EAAE,CAAA;AAC5B,IAAA,IAAI,CAACP,KAAK,GAAGH,IAAI,CAACG,KAAK,IAAI,EAAE,CAAA;AAC7B,IAAA,IAAI,CAAClC,KAAK,GAAG+B,IAAI,CAAC/B,KAAK,CAAA;AACvB,IAAA,IAAI,CAACmC,IAAI,GAAGJ,IAAI,CAACI,IAAI,CAAA;AACrB,IAAA,IAAI,CAACC,eAAe,GAAGL,IAAI,CAACK,eAAe,CAAA;AAC3C,IAAA,IAAI,CAACC,mBAAmB,GAAGN,IAAI,CAACM,mBAAmB,CAAA;AACnD,IAAA,IAAI,CAACC,mBAAmB,GAAGP,IAAI,CAACO,mBAAmB,CAAA;AACnD,IAAA,IAAI,CAACC,WAAW,GAAGR,IAAI,CAACQ,WAAW,CAAA;AACnC,IAAA,IAAI,CAACC,SAAS,GAAGT,IAAI,CAACS,SAAS,CAAA;AACjC,GAAA;AAEAE,EAAAA,gBAAgBA,GAAG;AACjB,IAAA,OAAO,IAAI,CAACH,WAAW,GACnBhD,UAAC,CAACoD,gBAAgB,CAACpD,UAAC,CAACqD,UAAU,CAAC,QAAQ,CAAC,EAAErD,UAAC,CAACqD,UAAU,CAAC,QAAQ,CAAC,CAAC,GAClE,IAAI,CAACJ,SAAS,CAAC,SAAS,CAAC,CAAA;AAC/B,GAAA;AAEAK,EAAAA,uBAAuBA,CACrBC,EAAkC,EAClCC,IAAkB,EAClB;AACA,IAAA,IAAIC,EAAE,GAAG,IAAI,CAACvD,QAAQ,CAAA;IACtB,IAAIF,UAAC,CAAC0D,kBAAkB,CAACH,EAAE,CAAC,EAAEE,EAAE,GAAG,GAAG,CAAA;AAEtC,IAAA,IAAI1D,IAAmD,CAAA;AAEvD,IAAA,IAAI0D,EAAE,EAAE;AACN1D,MAAAA,IAAI,GAAGC,UAAC,CAAC2D,mBAAmB,CAC1B3D,UAAC,CAAC4D,oBAAoB,CACpBH,EAAE,EACFF,EAAE,EACFvD,UAAC,CAAC6D,SAAS,CAACL,IAAI,CAAC,IAAI,IAAI,CAAC/C,KAAK,CAACqD,kBAAkB,EACpD,CACF,CAAC,CAAA;AACH,KAAC,MAAM;AACL,MAAA,IAAIC,QAAsB,CAAA;AAE1B,MAAA,IAAI,CAAC,IAAI,CAACnB,IAAI,KAAK,OAAO,IAAI,IAAI,CAACA,IAAI,KAAK,OAAO,KAAKY,IAAI,KAAK,IAAI,EAAE;AACrEO,QAAAA,QAAQ,GAAG,IAAI,CAACtD,KAAK,CAACqD,kBAAkB,EAAE,CAAA;AAC5C,OAAC,MAAM;AACLC,QAAAA,QAAQ,GAAG/D,UAAC,CAAC6D,SAAS,CAACL,IAAI,CAAC,CAAA;AAC9B,OAAA;AAEAzD,MAAAA,IAAI,GAAGC,UAAC,CAACgE,mBAAmB,CAAC,IAAI,CAACpB,IAAI,EAAE,CACtC5C,UAAC,CAACiE,kBAAkB,CAACV,EAAE,EAAEQ,QAAQ,CAAC,CACnC,CAAC,CAAA;AACJ,KAAA;AAGAhE,IAAAA,IAAI,CAACmE,WAAW,GAAG,IAAI,CAACzB,UAAU,CAAA;AAElC,IAAA,OAAO1C,IAAI,CAAA;AACb,GAAA;AAEAoE,EAAAA,wBAAwBA,CAACZ,EAAgB,EAAEC,IAAkB,EAAE;AAC7D,IAAA,MAAMY,MAAM,GAAGpE,UAAC,CAACgE,mBAAmB,CAAC,KAAK,EAAE,CAC1ChE,UAAC,CAACiE,kBAAkB,CAACjE,UAAC,CAAC6D,SAAS,CAACN,EAAE,CAAC,EAAEvD,UAAC,CAAC6D,SAAS,CAACL,IAAI,CAAC,CAAC,CACzD,CAAC,CAAA;AAEFY,IAAAA,MAAM,CAACF,WAAW,GAAG,IAAI,CAACzB,UAAU,CAAA;AACpC,IAAA,OAAO2B,MAAM,CAAA;AACf,GAAA;AAEAC,EAAAA,IAAIA,CAACd,EAAU,EAAEe,KAA0B,EAAE;AAC3C,IAAA,MAAMd,IAAI,GAAGxD,UAAC,CAAC6D,SAAS,CAACS,KAAK,CAAC,CAAA;AAC/B,IAAA,IAAItE,UAAC,CAACuE,eAAe,CAAChB,EAAE,CAAC,EAAE;AACzB,MAAA,IAAI,CAACiB,iBAAiB,CAACjB,EAAE,EAAEC,IAAI,CAAC,CAAA;KACjC,MAAM,IAAIxD,UAAC,CAACyE,cAAc,CAAClB,EAAE,CAAC,EAAE;AAC/B,MAAA,IAAI,CAACmB,gBAAgB,CAACnB,EAAE,EAAEC,IAAI,CAAC,CAAA;KAChC,MAAM,IAAIxD,UAAC,CAAC2E,mBAAmB,CAACpB,EAAE,CAAC,EAAE;AACpC,MAAA,IAAI,CAACqB,qBAAqB,CAACrB,EAAE,EAAEC,IAAI,CAAC,CAAA;AACtC,KAAC,MAAM;AACL,MAAA,IAAI,CAACb,KAAK,CAAC0B,IAAI,CAAC,IAAI,CAACf,uBAAuB,CAACC,EAAE,EAAEC,IAAI,CAAC,CAAC,CAAA;AACzD,KAAA;AACF,GAAA;AAEAqB,EAAAA,OAAOA,CAAC9E,IAAkB,EAAE+E,KAAwB,EAAE;IACpD,IACE,IAAI,CAACjC,eAAe,IACnB7C,UAAC,CAACmC,YAAY,CAACpC,IAAI,CAAC,IAAI,IAAI,CAAC2C,WAAW,CAACqC,GAAG,CAAChF,IAAI,CAACkB,IAAI,CAAE,EACzD;AACA,MAAA,OAAOlB,IAAI,CAAA;AACb,KAAC,MAAM;AACL,MAAA,OAAO,IAAI,CAACU,KAAK,CAACoE,OAAO,CAAC9E,IAAI,EAAE+E,KAAK,EAAE,IAAI,CAAChC,mBAAmB,CAAC,CAAA;AAClE,KAAA;AACF,GAAA;AAEA8B,EAAAA,qBAAqBA,CACnB;IAAEI,IAAI;AAAEC,IAAAA,KAAAA;GAA4B,EACpCC,QAA6B,EAC7B;AAKA,IAAA,IAAIpF,UAAU,CAACoF,QAAQ,CAAC,EAAE;AACxB,MAAA,IAAI,CAACb,IAAI,CAACW,IAAI,EAAEC,KAAK,CAAC,CAAA;AACtB,MAAA,OAAA;AACF,KAAA;IAIA,MAAME,MAAM,GAAG,IAAI,CAAC1E,KAAK,CAAC2E,gCAAgC,CAACF,QAAQ,CAAC,CAAA;AAEpE,IAAA,IAAI,CAACvC,KAAK,CAAC0B,IAAI,CAAC,IAAI,CAACF,wBAAwB,CAACgB,MAAM,EAAED,QAAQ,CAAC,CAAC,CAAA;AAEhE,IAAA,MAAMG,eAAe,GAAGrF,UAAC,CAACsF,qBAAqB,CAC7CtF,UAAC,CAACuF,gBAAgB,CAChB,KAAK,EACLvF,UAAC,CAAC6D,SAAS,CAACsB,MAAM,CAAC,EACnB,IAAI,CAAC1E,KAAK,CAACqD,kBAAkB,EAC/B,CAAC,EACDmB,KAAK,EACLjF,UAAC,CAAC6D,SAAS,CAACsB,MAAM,CACpB,CAAC,CAAA;AAED,IAAA,IAAInF,UAAC,CAACwF,SAAS,CAACR,IAAI,CAAC,EAAE;AACrB,MAAA,IAAIS,SAAS,CAAA;AACb,MAAA,IAAI1F,IAAI,CAAA;AAER,MAAA,IACE,IAAI,CAAC6C,IAAI,KAAK,OAAO,IACrB,IAAI,CAACA,IAAI,KAAK,KAAK,IACnB,IAAI,CAACA,IAAI,KAAK,OAAO,EACrB;QACA6C,SAAS,GAAG,IAAI,CAAChF,KAAK,CAACiF,qBAAqB,CAACP,MAAM,CAAClE,IAAI,CAAC,CAAA;QACzDlB,IAAI,GAAG,IAAI,CAACoE,wBAAwB,CAACsB,SAAS,EAAEJ,eAAe,CAAC,CAAA;AAClE,OAAC,MAAM;AACLI,QAAAA,SAAS,GAAGN,MAAM,CAAA;QAElBpF,IAAI,GAAGC,UAAC,CAAC2D,mBAAmB,CAC1B3D,UAAC,CAAC4D,oBAAoB,CAAC,GAAG,EAAE5D,UAAC,CAAC6D,SAAS,CAACsB,MAAM,CAAC,EAAEE,eAAe,CAClE,CAAC,CAAA;AACH,OAAA;AAEA,MAAA,IAAI,CAAC1C,KAAK,CAAC0B,IAAI,CAACtE,IAAI,CAAC,CAAA;AACrB,MAAA,IAAI,CAACsE,IAAI,CAACW,IAAI,EAAES,SAAS,CAAC,CAAA;AAC5B,KAAC,MAAM;AACL,MAAA,IAAI,CAAC9C,KAAK,CAAC0B,IAAI,CAAC,IAAI,CAACf,uBAAuB,CAAC0B,IAAI,EAAEK,eAAe,CAAC,CAAC,CAAA;AACtE,KAAA;AACF,GAAA;EAEAM,cAAcA,CACZpE,OAAwB,EACxBqE,MAAoB,EACpBC,UAAyB,EACzBC,eAAuB,EACvB;AACA,IAAA,MAAMC,KAAK,GAAGC,wBAAwB,CACpCzE,OAAO,CAACK,UAAU,CAACqE,KAAK,CAAC,CAAC,EAAEH,eAAe,CAAC,EAC5CF,MAAM,EACN,IAAI,CAACnF,KAAK,EACVQ,IAAI,IAAI,IAAI,CAACgC,SAAS,CAAChC,IAAI,CAAC,EAC5B,IAAI,CAAC8B,mBAAmB,EACxB,IAAI,CAACC,WACP,CAAC,CAAA;AACD,IAAA,IAAI,CAACL,KAAK,CAAC0B,IAAI,CAAC,IAAI,CAACf,uBAAuB,CAACuC,UAAU,CAACzF,QAAQ,EAAE2F,KAAK,CAAC,CAAC,CAAA;AAC3E,GAAA;AAEAG,EAAAA,kBAAkBA,CAACrE,IAAsB,EAAEsE,OAAqB,EAAE;AAChE,IAAA,IAAInG,UAAC,CAACoG,SAAS,CAACvE,IAAI,CAACwE,GAAG,CAAC,EAAExE,IAAI,CAACyE,QAAQ,GAAG,IAAI,CAAA;AAE/C,IAAA,MAAM/E,OAAO,GAAGM,IAAI,CAACkE,KAAe,CAAA;IACpC,MAAMH,MAAM,GAAG5F,UAAC,CAACoD,gBAAgB,CAC/BpD,UAAC,CAAC6D,SAAS,CAACsC,OAAO,CAAC,EACpBtE,IAAI,CAACwE,GAAG,EACRxE,IAAI,CAACyE,QACP,CAAC,CAAA;AAED,IAAA,IAAItG,UAAC,CAACwF,SAAS,CAACjE,OAAO,CAAC,EAAE;AACxB,MAAA,IAAI,CAAC8C,IAAI,CAAC9C,OAAO,EAAEqE,MAAM,CAAC,CAAA;AAC5B,KAAC,MAAM;AACL,MAAA,IAAI,CAACjD,KAAK,CAAC0B,IAAI,CAAC,IAAI,CAACf,uBAAuB,CAAC/B,OAAO,EAAEqE,MAAM,CAAC,CAAC,CAAA;AAChE,KAAA;AACF,GAAA;AAEApB,EAAAA,iBAAiBA,CAACjD,OAAwB,EAAEqE,MAAoB,EAAE;AAGhE,IAAA,IAAI,CAACrE,OAAO,CAACK,UAAU,CAACM,MAAM,EAAE;AAC9B,MAAA,IAAI,CAACS,KAAK,CAAC0B,IAAI,CACbrE,UAAC,CAAC2D,mBAAmB,CACnB3D,UAAC,CAACuG,cAAc,CACd,IAAI,CAACtD,SAAS,CAAC,0BAA0B,CAAC,EAC1CnD,UAAU,CAAC8F,MAAM,CAAC,GAAG,EAAE,GAAG,CAACA,MAAM,CACnC,CACF,CACF,CAAC,CAAA;AACD,MAAA,OAAA;AACF,KAAA;AAMA,IAAA,IAAIrE,OAAO,CAACK,UAAU,CAACM,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAACzB,KAAK,CAAC+F,QAAQ,CAACZ,MAAM,CAAC,EAAE;MACjE,MAAMa,IAAI,GAAG,IAAI,CAAChG,KAAK,CAAC2E,gCAAgC,CAACQ,MAAM,CAAC,CAAA;AAChE,MAAA,IAAI,CAACjD,KAAK,CAAC0B,IAAI,CAAC,IAAI,CAACF,wBAAwB,CAACsC,IAAI,EAAEb,MAAM,CAAC,CAAC,CAAA;AAC5DA,MAAAA,MAAM,GAAGa,IAAI,CAAA;AACf,KAAA;AAGA,IAAA,IAAI9E,aAAa,CAACJ,OAAO,CAAC,EAAE;AAC1B,MAAA,IAAImF,aAA8B,CAAA;AAClC,MAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpF,OAAO,CAACK,UAAU,CAACM,MAAM,EAAEyE,CAAC,EAAE,EAAE;AAClD,QAAA,MAAM9E,IAAI,GAAGN,OAAO,CAACK,UAAU,CAAC+E,CAAC,CAAC,CAAA;AAClC,QAAA,IAAI3G,UAAC,CAAC0B,aAAa,CAACG,IAAI,CAAC,EAAE;AACzB,UAAA,MAAA;AACF,SAAA;AACA,QAAA,MAAMwE,GAAG,GAAGxE,IAAI,CAACwE,GAAG,CAAA;AACpB,QAAA,IAAIxE,IAAI,CAACyE,QAAQ,IAAI,CAAC,IAAI,CAAC7F,KAAK,CAACmG,MAAM,CAACP,GAAG,CAAC,EAAE;UAC5C,MAAMpF,IAAI,GAAG,IAAI,CAACR,KAAK,CAAC2E,gCAAgC,CAACiB,GAAG,CAAC,CAAA;AAC7D,UAAA,IAAI,CAAC1D,KAAK,CAAC0B,IAAI,CAEb,IAAI,CAACF,wBAAwB,CAAClD,IAAI,EAAEoF,GAAG,CACzC,CAAC,CAAA;UACD,IAAI,CAACK,aAAa,EAAE;AAClBA,YAAAA,aAAa,GAAGnF,OAAO,GAAAT,MAAA,CAAA+F,MAAA,KAClBtF,OAAO,EAAA;AACVK,cAAAA,UAAU,EAAEL,OAAO,CAACK,UAAU,CAACqE,KAAK,EAAC;aACtC,CAAA,CAAA;AACH,WAAA;UACAS,aAAa,CAAC9E,UAAU,CAAC+E,CAAC,CAAC,GAAA7F,MAAA,CAAA+F,MAAA,CAAA,EAAA,EACtBhF,IAAI,EAAA;AACPwE,YAAAA,GAAG,EAAEpF,IAAAA;WACN,CAAA,CAAA;AACH,SAAA;AACF,OAAA;AACF,KAAA;AAEA,IAAA,KAAK,IAAI0F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpF,OAAO,CAACK,UAAU,CAACM,MAAM,EAAEyE,CAAC,EAAE,EAAE;AAClD,MAAA,MAAM9E,IAAI,GAAGN,OAAO,CAACK,UAAU,CAAC+E,CAAC,CAAC,CAAA;AAClC,MAAA,IAAI3G,UAAC,CAAC0B,aAAa,CAACG,IAAI,CAAC,EAAE;QACzB,IAAI,CAAC8D,cAAc,CAACpE,OAAO,EAAEqE,MAAM,EAAE/D,IAAI,EAAE8E,CAAC,CAAC,CAAA;AAC/C,OAAC,MAAM;AACL,QAAA,IAAI,CAACT,kBAAkB,CAACrE,IAAI,EAAE+D,MAAM,CAAC,CAAA;AACvC,OAAA;AACF,KAAA;AACF,GAAA;AAEAkB,EAAAA,qBAAqBA,CACnBvF,OAAuB,EACvBwF,GAAiB,EACiB;IAElC,IAAI,CAAC/G,UAAC,CAACgH,iBAAiB,CAACD,GAAG,CAAC,EAAE,OAAO,KAAK,CAAA;IAI3C,IAAIxF,OAAO,CAACC,QAAQ,CAACU,MAAM,GAAG6E,GAAG,CAACvF,QAAQ,CAACU,MAAM,EAAE,OAAA;AACnD,IAAA,IACEX,OAAO,CAACC,QAAQ,CAACU,MAAM,GAAG6E,GAAG,CAACvF,QAAQ,CAACU,MAAM,IAC7C,CAACZ,YAAY,CAACC,OAAO,CAAC,EACtB;AACA,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;AAEA,IAAA,KAAK,MAAME,IAAI,IAAIF,OAAO,CAACC,QAAQ,EAAE;AAEnC,MAAA,IAAI,CAACC,IAAI,EAAE,OAAO,KAAK,CAAA;MAGvB,IAAIzB,UAAC,CAAC0D,kBAAkB,CAACjC,IAAI,CAAC,EAAE,OAAO,KAAK,CAAA;AAC9C,KAAA;AAEA,IAAA,KAAK,MAAMA,IAAI,IAAIsF,GAAG,CAACvF,QAAQ,EAAE;MAE/B,IAAIxB,UAAC,CAACiH,eAAe,CAACxF,IAAI,CAAC,EAAE,OAAO,KAAK,CAAA;MAGzC,IAAIzB,UAAC,CAACkH,gBAAgB,CAACzF,IAAI,CAAC,EAAE,OAAO,KAAK,CAAA;MAG1C,IAAIzB,UAAC,CAAC0D,kBAAkB,CAACjC,IAAI,CAAC,EAAE,OAAO,KAAK,CAAA;AAC9C,KAAA;AAGA,IAAA,MAAMb,QAAQ,GAAGZ,UAAC,CAACmH,qBAAqB,CAAC5F,OAAO,CAAC,CAAA;AACjD,IAAA,MAAMU,KAA8B,GAAG;AAAEI,MAAAA,KAAK,EAAE,KAAK;AAAEzB,MAAAA,QAAAA;KAAU,CAAA;IAEjE,IAAI;MACFZ,UAAC,CAACoH,QAAQ,CAACL,GAAG,EAAEhF,kBAAkB,EAAEE,KAAK,CAAC,CAAA;KAC3C,CAAC,OAAOoF,CAAC,EAAE;AACV,MAAA,IAAIA,CAAC,KAAKvF,cAAc,EAAE,MAAMuF,CAAC,CAAA;AACnC,KAAA;IAEA,OAAO,CAACpF,KAAK,CAACI,KAAK,CAAA;AACrB,GAAA;AAEAiF,EAAAA,wBAAwBA,CACtB/F,OAAuB,EACvBwF,GAA8B,EAC9B;AACA,IAAA,MAAMQ,eAAe,GAAIC,EAAgB,IACvCA,EAAE,IAAFA,IAAAA,GAAAA,EAAE,GAAI,IAAI,CAAC/G,KAAK,CAACqD,kBAAkB,EAAE,CAAA;AAEvC,IAAA,KAAK,IAAI6C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpF,OAAO,CAACC,QAAQ,CAACU,MAAM,EAAEyE,CAAC,EAAE,EAAE;AAChD,MAAA,MAAMlF,IAAI,GAAGF,OAAO,CAACC,QAAQ,CAACmF,CAAC,CAAC,CAAA;AAChC,MAAA,IAAI3G,UAAC,CAAC0B,aAAa,CAACD,IAAI,CAAC,EAAE;QACzB,IAAI,CAAC4C,IAAI,CACP5C,IAAI,CAACrB,QAAQ,EACbJ,UAAC,CAACyH,eAAe,CAACV,GAAG,CAACvF,QAAQ,CAACyE,KAAK,CAACU,CAAC,CAAC,CAACe,GAAG,CAACH,eAAe,CAAC,CAC9D,CAAC,CAAA;AACH,OAAC,MAAM;AACL,QAAA,IAAI,CAAClD,IAAI,CAAC5C,IAAI,EAAE8F,eAAe,CAACR,GAAG,CAACvF,QAAQ,CAACmF,CAAC,CAAC,CAAC,CAAC,CAAA;AACnD,OAAA;AACF,KAAA;AACF,GAAA;AAEAjC,EAAAA,gBAAgBA,CAACnD,OAAuB,EAAEoG,QAA6B,EAAE;IACvE,IAAIA,QAAQ,KAAK,IAAI,EAAE;MACrB,IAAI,CAAChF,KAAK,CAAC0B,IAAI,CACbrE,UAAC,CAAC2D,mBAAmB,CACnB3D,UAAC,CAACuG,cAAc,CAAC,IAAI,CAACtD,SAAS,CAAC,0BAA0B,CAAC,EAAE,EAAE,CACjE,CACF,CAAC,CAAA;AACD,MAAA,OAAA;AACF,KAAA;AACA,IAAA,IAAI,CAAC1B,OAAO,CAACC,QAAQ,EAAE,OAAA;IASvB,IAAI,IAAI,CAACsF,qBAAqB,CAACvF,OAAO,EAAEoG,QAAQ,CAAC,EAAE;AACjD,MAAA,IAAI,CAACL,wBAAwB,CAAC/F,OAAO,EAAEoG,QAAQ,CAAC,CAAA;AAChD,MAAA,OAAA;AACF,KAAA;AAKA,IAAA,MAAM7C,KAAK,GAAG,CAACxD,YAAY,CAACC,OAAO,CAAC,IAAIA,OAAO,CAACC,QAAQ,CAACU,MAAM,CAAA;IAM/D,MAAM2C,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC8C,QAAQ,EAAE7C,KAAK,CAAC,CAAA;AAE7C,IAAA,IAAI9E,UAAC,CAACmC,YAAY,CAAC0C,OAAO,CAAC,EAAE;AAG3B8C,MAAAA,QAAQ,GAAG9C,OAAO,CAAA;AACpB,KAAC,MAAM;MACL8C,QAAQ,GAAG,IAAI,CAAClH,KAAK,CAAC2E,gCAAgC,CAACuC,QAAQ,CAAC,CAAA;MAChE,IAAI,CAACjF,WAAW,CAACkF,GAAG,CAACD,QAAQ,CAAC1G,IAAI,CAAC,CAAA;AACnC,MAAA,IAAI,CAAC0B,KAAK,CAAC0B,IAAI,CAAC,IAAI,CAACF,wBAAwB,CAACwD,QAAQ,EAAE9C,OAAO,CAAC,CAAC,CAAA;AACnE,KAAA;AAEA,IAAA,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpF,OAAO,CAACC,QAAQ,CAACU,MAAM,EAAEyE,CAAC,EAAE,EAAE;AAChD,MAAA,MAAMlF,IAAI,GAAGF,OAAO,CAACC,QAAQ,CAACmF,CAAC,CAAC,CAAA;MAGhC,IAAI,CAAClF,IAAI,EAAE,SAAA;AAEX,MAAA,IAAIoG,OAAO,CAAA;AAEX,MAAA,IAAI7H,UAAC,CAAC0B,aAAa,CAACD,IAAI,CAAC,EAAE;AACzBoG,QAAAA,OAAO,GAAG,IAAI,CAAChD,OAAO,CAAC8C,QAAQ,CAAC,CAAA;QAChCE,OAAO,GAAG7H,UAAC,CAACuG,cAAc,CACxBvG,UAAC,CAACoD,gBAAgB,CAACyE,OAAO,EAAE7H,UAAC,CAACqD,UAAU,CAAC,OAAO,CAAC,CAAC,EAClD,CAACrD,UAAC,CAAC8H,cAAc,CAACnB,CAAC,CAAC,CACtB,CAAC,CAAA;QAID,IAAI,CAACtC,IAAI,CAAC5C,IAAI,CAACrB,QAAQ,EAAEyH,OAAO,CAAC,CAAA;AACnC,OAAC,MAAM;AACLA,QAAAA,OAAO,GAAG7H,UAAC,CAACoD,gBAAgB,CAACuE,QAAQ,EAAE3H,UAAC,CAAC8H,cAAc,CAACnB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;AACjE,QAAA,IAAI,CAACtC,IAAI,CAAC5C,IAAI,EAAEoG,OAAO,CAAC,CAAA;AAC1B,OAAA;AACF,KAAA;AACF,GAAA;AAEArE,EAAAA,IAAIA,CAACjC,OAAe,EAAEwG,GAAiB,EAAE;AAIvC,IAAA,IAAI,CAAC/H,UAAC,CAACgH,iBAAiB,CAACe,GAAG,CAAC,IAAI,CAAC/H,UAAC,CAAC0D,kBAAkB,CAACqE,GAAG,CAAC,EAAE;MAC3D,MAAMC,IAAI,GAAG,IAAI,CAACvH,KAAK,CAACwH,qBAAqB,CAACF,GAAG,EAAE,IAAI,CAAC,CAAA;AACxD,MAAA,IAAIC,IAAI,EAAE;AACR,QAAA,IAAI,CAACrF,KAAK,CAAC0B,IAAI,CAAC,IAAI,CAACF,wBAAwB,CAAC6D,IAAI,EAAEhI,UAAC,CAAC6D,SAAS,CAACkE,GAAG,CAAC,CAAC,CAAC,CAAA;AACtEA,QAAAA,GAAG,GAAGC,IAAI,CAAA;AACZ,OAAA;AACF,KAAA;AAEA,IAAA,IAAI,CAAC3D,IAAI,CAAC9C,OAAO,EAAEwG,GAAG,CAAC,CAAA;IAEvB,OAAO,IAAI,CAACpF,KAAK,CAAA;AACnB,GAAA;AACF,CAAA;AAOO,SAASqD,wBAAwBA,CACtCkC,YAAiB,EACjBtC,MAAoB,EACpBnF,KAAY,EACZwC,SAA4B,EAC5BF,mBAA4B,EAC5BC,WAAoB,EACF;EAGlB,MAAMjC,IAAI,GAAG,EAAE,CAAA;EACf,IAAIoH,UAAU,GAAG,IAAI,CAAA;EACrB,IAAIC,kBAAkB,GAAG,KAAK,CAAA;AAC9B,EAAA,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,YAAY,CAAChG,MAAM,EAAEyE,CAAC,EAAE,EAAE;AAC5C,IAAA,MAAM9E,IAAI,GAAGqG,YAAY,CAACvB,CAAC,CAAC,CAAA;AAC5B,IAAA,MAAMN,GAAG,GAAGxE,IAAI,CAACwE,GAAG,CAAA;IACpB,IAAIrG,UAAC,CAACmC,YAAY,CAACkE,GAAG,CAAC,IAAI,CAACxE,IAAI,CAACyE,QAAQ,EAAE;MACzCvF,IAAI,CAACsD,IAAI,CAACrE,UAAC,CAACqI,aAAa,CAAChC,GAAG,CAACpF,IAAI,CAAC,CAAC,CAAA;KACrC,MAAM,IAAIjB,UAAC,CAACsI,iBAAiB,CAACjC,GAAG,CAAC,EAAE;MACnCtF,IAAI,CAACsD,IAAI,CAACrE,UAAC,CAAC6D,SAAS,CAACwC,GAAG,CAAC,CAAC,CAAA;AAC3B+B,MAAAA,kBAAkB,GAAG,IAAI,CAAA;KAC1B,MAAM,IAAIpI,UAAC,CAACoG,SAAS,CAACC,GAAG,CAAC,EAAE;AAE3BtF,MAAAA,IAAI,CAACsD,IAAI,CAACrE,UAAC,CAACqI,aAAa,CAACE,MAAM,CAAClC,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC,CAAA;KAC9C,MAAM,IAAI/F,UAAC,CAACwI,aAAa,CAACnC,GAAG,CAAC,EAAE,CAEhC,MAAM;MACLtF,IAAI,CAACsD,IAAI,CAACrE,UAAC,CAAC6D,SAAS,CAACwC,GAAG,CAAC,CAAC,CAAA;AAC3B8B,MAAAA,UAAU,GAAG,KAAK,CAAA;AACpB,KAAA;AACF,GAAA;AAEA,EAAA,IAAIpC,KAAK,CAAA;AACT,EAAA,IAAIhF,IAAI,CAACmB,MAAM,KAAK,CAAC,EAAE;IACrB,MAAMuG,aAAa,GAAGzF,WAAW,GAC7BhD,UAAC,CAACoD,gBAAgB,CAACpD,UAAC,CAACqD,UAAU,CAAC,QAAQ,CAAC,EAAErD,UAAC,CAACqD,UAAU,CAAC,QAAQ,CAAC,CAAC,GAClEJ,SAAS,CAAC,SAAS,CAAC,CAAA;IACxB8C,KAAK,GAAG/F,UAAC,CAACuG,cAAc,CAACkC,aAAa,EAAE,CACtCzI,UAAC,CAAC0I,gBAAgB,CAAC,EAAE,CAAC,EACtB1I,UAAC,CAAC2I,kBAAkB,CAAC,CACnB3I,UAAC,CAACuG,cAAc,CAACtD,SAAS,CAAC,0BAA0B,CAAC,EAAE,CACtDjD,UAAC,CAAC6D,SAAS,CAAC+B,MAAM,CAAC,CACpB,CAAC,EACF5F,UAAC,CAAC6D,SAAS,CAAC+B,MAAM,CAAC,CACpB,CAAC,CACH,CAAC,CAAA;AACJ,GAAC,MAAM;AACL,IAAA,IAAIgD,aAA2B,GAAG5I,UAAC,CAACyH,eAAe,CAAC1G,IAAI,CAAC,CAAA;IAEzD,IAAI,CAACoH,UAAU,EAAE;MACfS,aAAa,GAAG5I,UAAC,CAACuG,cAAc,CAC9BvG,UAAC,CAACoD,gBAAgB,CAACwF,aAAa,EAAE5I,UAAC,CAACqD,UAAU,CAAC,KAAK,CAAC,CAAC,EACtD,CAACJ,SAAS,CAAC,eAAe,CAAC,CAC7B,CAAC,CAAA;AACH,KAAC,MAAM,IAAI,CAACmF,kBAAkB,IAAI,CAACpI,UAAC,CAAC6I,SAAS,CAACpI,KAAK,CAACqI,KAAK,CAAC,EAAE;AAE3D,MAAA,MAAMC,YAAY,GAAGtI,KAAK,CAACuI,gBAAgB,EAAE,CAAA;AAC7C,MAAA,MAAMzF,EAAE,GAAGwF,YAAY,CAACrD,qBAAqB,CAAC,UAAU,CAAC,CAAA;MAEzDqD,YAAY,CAAC1E,IAAI,CAAC;QAChBd,EAAE;AACFC,QAAAA,IAAI,EAAEoF,aAAa;AACnBhG,QAAAA,IAAI,EAAE,OAAA;AACR,OAAC,CAAC,CAAA;AAEFgG,MAAAA,aAAa,GAAG5I,UAAC,CAAC6D,SAAS,CAACN,EAAE,CAAC,CAAA;AACjC,KAAA;IAEAwC,KAAK,GAAG/F,UAAC,CAACuG,cAAc,CACtBtD,SAAS,CAAE,CAAyBF,uBAAAA,EAAAA,mBAAmB,GAAG,OAAO,GAAG,EAAG,EAAC,CAAC,EACzE,CAAC/C,UAAC,CAAC6D,SAAS,CAAC+B,MAAM,CAAC,EAAEgD,aAAa,CACrC,CAAC,CAAA;AACH,GAAA;AACA,EAAA,OAAO7C,KAAK,CAAA;AACd,CAAA;AAEO,SAASkD,0BAA0BA,CACxCC,IAAqC,EACrCjG,SAA4B,EAC5BH,mBAA4B,EAC5BD,eAAwB,EACxBE,mBAA4B,EAC5BC,WAAoB,EACpB;EACA,MAAM;IAAEjD,IAAI;AAAEU,IAAAA,KAAAA;AAAM,GAAC,GAAGyI,IAAI,CAAA;AAE5B,EAAA,MAAMC,QAAQ,GAAGpJ,IAAI,CAAC6C,IAAI,CAAA;AAC1B,EAAA,MAAMwG,OAAO,GAAGrJ,IAAI,CAACsJ,GAAG,CAAA;EACxB,MAAM1G,KAAK,GAAG,EAAE,CAAA;AAEhB,EAAA,KAAK,IAAIgE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5G,IAAI,CAACuJ,YAAY,CAACpH,MAAM,EAAEyE,CAAC,EAAE,EAAE;AACjD,IAAA,MAAMvC,MAAM,GAAGrE,IAAI,CAACuJ,YAAY,CAAC3C,CAAC,CAAC,CAAA;AAEnC,IAAA,MAAMlB,SAAS,GAAGrB,MAAM,CAACZ,IAAI,CAAA;AAC7B,IAAA,MAAMjC,OAAO,GAAG6C,MAAM,CAACb,EAAE,CAAA;AAEzB,IAAA,MAAMgG,aAAuC,GAC3C,IAAIjH,wBAAwB,CAAC;MAE3BG,UAAU,EAAE1C,IAAI,CAACmE,WAAW;AAC5BvB,MAAAA,KAAK,EAAEA,KAAK;AACZlC,MAAAA,KAAK,EAAEA,KAAK;MACZmC,IAAI,EAAE7C,IAAI,CAAC6C,IAAI;MACfC,eAAe;MACfC,mBAAmB;MACnBE,WAAW;MACXD,mBAAmB;AACnBE,MAAAA,SAAAA;AACF,KAAC,CAAC,CAAA;AAEJ,IAAA,IAAIjD,UAAC,CAACwF,SAAS,CAACjE,OAAO,CAAC,EAAE;AACxBgI,MAAAA,aAAa,CAAC/F,IAAI,CAACjC,OAAO,EAAEkE,SAAS,CAAC,CAAA;MAEtC,IAAI,CAACkB,CAAC,KAAK5G,IAAI,CAACuJ,YAAY,CAACpH,MAAM,GAAG,CAAC,EAAE;AAGvClC,QAAAA,UAAC,CAACwJ,QAAQ,CAAC7G,KAAK,CAACA,KAAK,CAACT,MAAM,GAAG,CAAC,CAAC,EAAEkC,MAAM,CAAC,CAAA;AAC7C,OAAA;AACF,KAAC,MAAM;AACLzB,MAAAA,KAAK,CAAC0B,IAAI,CACRrE,UAAC,CAACwJ,QAAQ,CACRD,aAAa,CAACjG,uBAAuB,CAAC/B,OAAO,EAAEkE,SAAS,CAAC,EACzDrB,MACF,CACF,CAAC,CAAA;AACH,KAAA;AACF,GAAA;EAEA,IAAIqF,IAAkC,GAAG,IAAI,CAAA;EAC7C,IAAIC,QAAQ,GAAG,EAAE,CAAA;AACjB,EAAA,KAAK,MAAM3J,IAAI,IAAI4C,KAAK,EAAE;AACxB,IAAA,IAAI3C,UAAC,CAAC2J,qBAAqB,CAAC5J,IAAI,CAAC,EAAE;MACjC,IAAI0J,IAAI,KAAK,IAAI,EAAE;QAEjBA,IAAI,CAACH,YAAY,CAACjF,IAAI,CAAC,GAAGtE,IAAI,CAACuJ,YAAY,CAAC,CAAA;AAC5C,QAAA,SAAA;AACF,OAAC,MAAM;QAELvJ,IAAI,CAAC6C,IAAI,GAAGuG,QAAQ,CAAA;AACpBM,QAAAA,IAAI,GAAG1J,IAAI,CAAA;AACb,OAAA;AACF,KAAC,MAAM;AACL0J,MAAAA,IAAI,GAAG,IAAI,CAAA;AACb,KAAA;AAEA,IAAA,IAAI,CAAC1J,IAAI,CAACsJ,GAAG,EAAE;MACbtJ,IAAI,CAACsJ,GAAG,GAAGD,OAAO,CAAA;AACpB,KAAA;AACAM,IAAAA,QAAQ,CAACrF,IAAI,CAACtE,IAAI,CAAC,CAAA;AACrB,GAAA;EAEA,IACE2J,QAAQ,CAACxH,MAAM,KAAK,CAAC,IACrBlC,UAAC,CAAC2J,qBAAqB,CAACD,QAAQ,CAAC,CAAC,CAAC,CAAC,IACpC1J,UAAC,CAAC4J,qBAAqB,CAACF,QAAQ,CAAC,CAAC,CAAC,CAAC,IACpC1J,UAAC,CAACkH,gBAAgB,CAACwC,QAAQ,CAAC,CAAC,CAAC,CAACG,UAAU,CAAC,IAC1CH,QAAQ,CAAC,CAAC,CAAC,CAACJ,YAAY,CAACpH,MAAM,KAAK,CAAC,EACrC;AAOA,IAAA,MAAM4H,IAAI,GAAGJ,QAAQ,CAAC,CAAC,CAAC,CAACG,UAAU,CAAA;AACnCC,IAAAA,IAAI,CAACC,SAAS,GAAG,CAACL,QAAQ,CAAC,CAAC,CAAC,CAACJ,YAAY,CAAC,CAAC,CAAC,CAAC9F,IAAI,CAAC,CAAA;IACnDkG,QAAQ,GAAG,CAACI,IAAI,CAAC,CAAA;AACnB,GAAC,MAAM;AAEL,IAAA,IACE9J,UAAC,CAACgK,cAAc,CAACd,IAAI,CAACe,MAAM,EAAE;AAAEzG,MAAAA,IAAI,EAAEzD,IAAAA;AAAK,KAAC,CAAC,IAC7C,CAAC2J,QAAQ,CAAC1I,IAAI,CAACkJ,CAAC,IAAIlK,UAAC,CAAC2J,qBAAqB,CAACO,CAAC,CAAC,CAAC,EAC/C;AACA,MAAA,KAAK,IAAIvD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+C,QAAQ,CAACxH,MAAM,EAAEyE,CAAC,EAAE,EAAE;AACxC,QAAA,MAAM5G,IAAY,GAAG2J,QAAQ,CAAC/C,CAAC,CAAC,CAAA;AAChC,QAAA,IAAI3G,UAAC,CAAC4J,qBAAqB,CAAC7J,IAAI,CAAC,EAAE;AACjC2J,UAAAA,QAAQ,CAAC/C,CAAC,CAAC,GAAG5G,IAAI,CAAC8J,UAAU,CAAA;AAC/B,SAAA;AACF,OAAA;AACF,KAAA;AACF,GAAA;AAEA,EAAA,IAAIH,QAAQ,CAACxH,MAAM,KAAK,CAAC,EAAE;AACzBgH,IAAAA,IAAI,CAACiB,WAAW,CAACT,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;AAC/B,GAAC,MAAM;AACLR,IAAAA,IAAI,CAACkB,mBAAmB,CAACV,QAAQ,CAAC,CAAA;AACpC,GAAA;EACAjJ,KAAK,CAAC4J,KAAK,EAAE,CAAA;AACf,CAAA;AAEO,SAASC,2BAA2BA,CACzCpB,IAAsC,EACtCjG,SAA4B,EAC5BH,mBAA4B,EAC5BD,eAAwB,EACxBE,mBAA4B,EAC5BC,WAAoB,EACpB;EACA,MAAM;IAAEjD,IAAI;IAAEU,KAAK;AAAE8J,IAAAA,UAAAA;AAAW,GAAC,GAAGrB,IAAI,CAAA;EAExC,MAAMvG,KAAqC,GAAG,EAAE,CAAA;AAEhD,EAAA,MAAM4G,aAAa,GAAG,IAAIjH,wBAAwB,CAAC;IACjDpC,QAAQ,EAAEH,IAAI,CAACG,QAAQ;AACvBO,IAAAA,KAAK,EAAEA,KAAK;AACZkC,IAAAA,KAAK,EAAEA,KAAK;IACZG,mBAAmB;IACnBD,eAAe;IACfE,mBAAmB;IACnBC,WAAW;AACXC,IAAAA,SAAAA;AACF,GAAC,CAAC,CAAA;AAEF,EAAA,IAAI8E,GAAwB,CAAA;AAC5B,EAAA,IACG,CAACwC,UAAU,CAACX,qBAAqB,EAAE,IAClC,CAACW,UAAU,CAACC,oBAAoB,EAAE,IACpCtB,IAAI,CAACuB,kBAAkB,EAAE,EACzB;IACA1C,GAAG,GAAGtH,KAAK,CAAC2E,gCAAgC,CAACrF,IAAI,CAACkF,KAAK,EAAE,KAAK,CAAC,CAAA;IAE/DtC,KAAK,CAAC0B,IAAI,CACRrE,UAAC,CAACgE,mBAAmB,CAAC,KAAK,EAAE,CAAChE,UAAC,CAACiE,kBAAkB,CAAC8D,GAAG,EAAEhI,IAAI,CAACkF,KAAK,CAAC,CAAC,CACtE,CAAC,CAAA;IAED,IAAIjF,UAAC,CAACgH,iBAAiB,CAACjH,IAAI,CAACkF,KAAK,CAAC,EAAE;MACnCsE,aAAa,CAAC7G,WAAW,CAACkF,GAAG,CAACG,GAAG,CAAC9G,IAAI,CAAC,CAAA;AACzC,KAAA;AACF,GAAA;AAEAsI,EAAAA,aAAa,CAAC/F,IAAI,CAACzD,IAAI,CAACiF,IAAI,EAAE+C,GAAG,IAAIhI,IAAI,CAACkF,KAAK,CAAC,CAAA;AAEhD,EAAA,IAAI8C,GAAG,EAAE;AACP,IAAA,IAAIwC,UAAU,CAACG,yBAAyB,EAAE,EAAE;MAC1CxB,IAAI,CAACiB,WAAW,CAACnK,UAAC,CAACoB,cAAc,CAAC,EAAE,CAAC,CAAC,CAAA;AACtCuB,MAAAA,KAAK,CAAC0B,IAAI,CAACrE,UAAC,CAAC2K,eAAe,CAAC3K,UAAC,CAAC6D,SAAS,CAACkE,GAAG,CAAC,CAAC,CAAC,CAAA;AACjD,KAAC,MAAM;AACLpF,MAAAA,KAAK,CAAC0B,IAAI,CAACrE,UAAC,CAAC2D,mBAAmB,CAAC3D,UAAC,CAAC6D,SAAS,CAACkE,GAAG,CAAC,CAAC,CAAC,CAAA;AACrD,KAAA;AACF,GAAA;AAEAmB,EAAAA,IAAI,CAACkB,mBAAmB,CAACzH,KAAK,CAAC,CAAA;EAC/BlC,KAAK,CAAC4J,KAAK,EAAE,CAAA;AACf;;ACruBA,SAASO,6BAA6BA,CAAC7K,IAA2B,EAAE;AAClE,EAAA,KAAK,MAAMqE,MAAM,IAAIrE,IAAI,CAACuJ,YAAY,EAAE;IACtC,IAAItJ,UAAC,CAACwF,SAAS,CAACpB,MAAM,CAACb,EAAE,CAAC,EAAE;AAC1B,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AACF,GAAA;AACA,EAAA,OAAO,KAAK,CAAA;AACd,CAAA;AAQA,YAAesH,yBAAO,CAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,IAAA,EAAAC,eAAA,EAAAC,KAAA,EAAAC,qBAAA,EAAAC,KAAA,EAAAC,gBAAA,CAAA;AAChDP,EAAAA,GAAG,CAACQ,aAAa,CAAC,CAAC,CAAC,CAAA;EAEpB,MAAM;AAAEtI,IAAAA,WAAW,GAAG,KAAA;AAAM,GAAC,GAAG+H,OAAO,CAAA;EAEvC,MAAMlI,eAAe,IAAAmI,IAAA,GAAA,CAAAC,eAAA,GACnBH,GAAG,CAACS,UAAU,CAAC,iBAAiB,CAAC,KAAA,IAAA,GAAAN,eAAA,GAAIF,OAAO,CAACS,KAAK,KAAA,IAAA,GAAAR,IAAA,GAAI,KAAK,CAAA;EAC7D,MAAMlI,mBAAmB,IAAAoI,KAAA,GAAA,CAAAC,qBAAA,GACvBJ,OAAO,CAACU,cAAc,KAAA,IAAA,GAAAN,qBAAA,GAAIL,GAAG,CAACS,UAAU,CAAC,qBAAqB,CAAC,KAAA,IAAA,GAAAL,KAAA,GAAI,KAAK,CAAA;EAC1E,MAAMnI,mBAAmB,IAAAqI,KAAA,GAAA,CAAAC,gBAAA,GACvBP,GAAG,CAACS,UAAU,CAAC,qBAAqB,CAAC,KAAA,IAAA,GAAAF,gBAAA,GAAIN,OAAO,CAACS,KAAK,KAAA,IAAA,GAAAJ,KAAA,GAAI,KAAK,CAAA;EAEjE,OAAO;AACLnK,IAAAA,IAAI,EAAE,yBAAyB;AAE/ByK,IAAAA,OAAO,EAAE;MACPC,sBAAsBA,CAACzC,IAAI,EAAE;AAC3B,QAAA,MAAM0C,WAAW,GAAG1C,IAAI,CAACvI,GAAG,CAAC,aAAa,CAAC,CAAA;AAC3C,QAAA,IAAI,CAACiL,WAAW,CAACjC,qBAAqB,EAAE,EAAE,OAAA;AAC1C,QAAA,IAAI,CAACiB,6BAA6B,CAACgB,WAAW,CAAC7L,IAAI,CAAC,EAAE,OAAA;QAEtD,MAAM8L,UAAU,GAAG,EAAE,CAAA;AAErB,QAAA,KAAK,MAAM5K,IAAI,IAAIH,MAAM,CAACC,IAAI,CAACmI,IAAI,CAAC4C,0BAA0B,EAAE,CAAC,EAAE;UACjED,UAAU,CAACxH,IAAI,CACbrE,UAAC,CAAC+L,eAAe,CAAC/L,UAAC,CAACqD,UAAU,CAACpC,IAAI,CAAC,EAAEjB,UAAC,CAACqD,UAAU,CAACpC,IAAI,CAAC,CAC1D,CAAC,CAAA;AACH,SAAA;AAKAiI,QAAAA,IAAI,CAACiB,WAAW,CAACyB,WAAW,CAAC7L,IAAI,CAAC,CAAA;QAClCmJ,IAAI,CAAC8C,WAAW,CAAChM,UAAC,CAACiM,sBAAsB,CAAC,IAAI,EAAEJ,UAAU,CAAC,CAAC,CAAA;AAC5D3C,QAAAA,IAAI,CAACzI,KAAK,CAAC4J,KAAK,EAAE,CAAA;OACnB;MAED6B,aAAaA,CAAChD,IAA+B,EAAE;QAC7C,MAAM;UAAEnJ,IAAI;AAAEU,UAAAA,KAAAA;AAAM,SAAC,GAAGyI,IAAI,CAAA;AAC5B,QAAA,MAAMlE,IAAI,GAAGjF,IAAI,CAACiF,IAAI,CAAA;AAEtB,QAAA,IAAIhF,UAAC,CAACwF,SAAS,CAACR,IAAI,CAAC,EAAE;AAGrB,UAAA,MAAMyB,IAAI,GAAGhG,KAAK,CAACiF,qBAAqB,CAAC,KAAK,CAAC,CAAA;AAE/C3F,UAAAA,IAAI,CAACiF,IAAI,GAAGhF,UAAC,CAACgE,mBAAmB,CAAC,KAAK,EAAE,CACvChE,UAAC,CAACiE,kBAAkB,CAACwC,IAAI,CAAC,CAC3B,CAAC,CAAA;UAEFyC,IAAI,CAAC1I,WAAW,EAAE,CAAA;UAClB,MAAM2L,aAAa,GAAGjD,IAAI,CAACnJ,IAAI,CAACoB,IAAI,CAACA,IAAI,CAAA;UACzC,MAAMwB,KAAK,GAAG,EAAE,CAAA;UAKhB,IAAIwJ,aAAa,CAACjK,MAAM,KAAK,CAAC,IAAIgH,IAAI,CAACuB,kBAAkB,EAAE,EAAE;AAC3D9H,YAAAA,KAAK,CAACtB,OAAO,CAACrB,UAAC,CAAC2D,mBAAmB,CAAClD,KAAK,CAACqD,kBAAkB,EAAE,CAAC,CAAC,CAAA;AAClE,WAAA;UAEAnB,KAAK,CAACtB,OAAO,CACXrB,UAAC,CAAC2D,mBAAmB,CACnB3D,UAAC,CAAC4D,oBAAoB,CAAC,GAAG,EAAEoB,IAAI,EAAEhF,UAAC,CAAC6D,SAAS,CAAC4C,IAAI,CAAC,CACrD,CACF,CAAC,CAAA;AAEDpG,UAAAA,wBAAwB,CAAC6I,IAAI,EAAEvG,KAAK,CAAC,CAAA;UACrClC,KAAK,CAAC4J,KAAK,EAAE,CAAA;AACb,UAAA,OAAA;AACF,SAAA;AAEA,QAAA,IAAI,CAACrK,UAAC,CAAC2J,qBAAqB,CAAC3E,IAAI,CAAC,EAAE,OAAA;QAEpC,MAAMzD,OAAO,GAAGyD,IAAI,CAACsE,YAAY,CAAC,CAAC,CAAC,CAAC/F,EAAE,CAAA;AACvC,QAAA,IAAI,CAACvD,UAAC,CAACwF,SAAS,CAACjE,OAAO,CAAC,EAAE,OAAA;AAE3B,QAAA,MAAM8E,GAAG,GAAG5F,KAAK,CAACiF,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAC9C3F,IAAI,CAACiF,IAAI,GAAGhF,UAAC,CAACgE,mBAAmB,CAACgB,IAAI,CAACpC,IAAI,EAAE,CAC3C5C,UAAC,CAACiE,kBAAkB,CAACoC,GAAG,EAAE,IAAI,CAAC,CAChC,CAAC,CAAA;QAEF,MAAM1D,KAAqC,GAAG,EAAE,CAAA;AAEhD,QAAA,MAAM4G,aAAa,GAAG,IAAIjH,wBAAwB,CAAC;UACjDM,IAAI,EAAEoC,IAAI,CAACpC,IAAI;AACfnC,UAAAA,KAAK,EAAEA,KAAK;AACZkC,UAAAA,KAAK,EAAEA,KAAK;UACZG,mBAAmB;UACnBD,eAAe;UACfE,mBAAmB;UACnBC,WAAW;AACXC,UAAAA,SAAS,EAAEhC,IAAI,IAAI,IAAI,CAACgC,SAAS,CAAChC,IAAI,CAAA;AACxC,SAAC,CAAC,CAAA;AAEFsI,QAAAA,aAAa,CAAC/F,IAAI,CAACjC,OAAO,EAAE8E,GAAG,CAAC,CAAA;AAEhChG,QAAAA,wBAAwB,CAAC6I,IAAI,EAAEvG,KAAK,CAAC,CAAA;QACrClC,KAAK,CAAC4J,KAAK,EAAE,CAAA;OACd;AAED+B,MAAAA,WAAWA,CAAC;QAAErM,IAAI;AAAEU,QAAAA,KAAAA;AAAM,OAAC,EAAE;AAC3B,QAAA,MAAMc,OAAO,GAAGxB,IAAI,CAACsM,KAAK,CAAA;AAC1B,QAAA,IAAI,CAACrM,UAAC,CAACwF,SAAS,CAACjE,OAAO,CAAC,EAAE,OAAA;AAE3B,QAAA,MAAMwG,GAAG,GAAGtH,KAAK,CAACiF,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAC9C3F,IAAI,CAACsM,KAAK,GAAGtE,GAAG,CAAA;QAEhB,MAAMpF,KAAqC,GAAG,EAAE,CAAA;AAEhD,QAAA,MAAM4G,aAAa,GAAG,IAAIjH,wBAAwB,CAAC;AACjDM,UAAAA,IAAI,EAAE,KAAK;AACXnC,UAAAA,KAAK,EAAEA,KAAK;AACZkC,UAAAA,KAAK,EAAEA,KAAK;UACZG,mBAAmB;UACnBD,eAAe;UACfE,mBAAmB;UACnBC,WAAW;AACXC,UAAAA,SAAS,EAAEhC,IAAI,IAAI,IAAI,CAACgC,SAAS,CAAChC,IAAI,CAAA;AACxC,SAAC,CAAC,CAAA;AACFsI,QAAAA,aAAa,CAAC/F,IAAI,CAACjC,OAAO,EAAEwG,GAAG,CAAC,CAAA;AAEhChI,QAAAA,IAAI,CAACoB,IAAI,CAACA,IAAI,GAAG,CAAC,GAAGwB,KAAK,EAAE,GAAG5C,IAAI,CAACoB,IAAI,CAACA,IAAI,CAAC,CAAA;QAC9CV,KAAK,CAAC4J,KAAK,EAAE,CAAA;OACd;AAEDiC,MAAAA,oBAAoBA,CAACpD,IAAI,EAAEjH,KAAK,EAAE;QAChC,IAAI,CAACjC,UAAC,CAACwF,SAAS,CAAC0D,IAAI,CAACnJ,IAAI,CAACiF,IAAI,CAAC,EAAE,OAAA;AAClCsF,QAAAA,2BAA2B,CACzBpB,IAAI,EACJjI,IAAI,IAAIgB,KAAK,CAACgB,SAAS,CAAChC,IAAI,CAAC,EAC7B6B,mBAAmB,EACnBD,eAAe,EACfE,mBAAmB,EACnBC,WACF,CAAC,CAAA;OACF;AAEDuJ,MAAAA,mBAAmBA,CAACrD,IAAI,EAAEjH,KAAK,EAAE;QAC/B,MAAM;UAAElC,IAAI;AAAEkK,UAAAA,MAAAA;AAAO,SAAC,GAAGf,IAAI,CAAA;AAC7B,QAAA,IAAIlJ,UAAC,CAACwM,eAAe,CAACvC,MAAM,CAAC,EAAE,OAAA;AAC/B,QAAA,IAAI,CAACA,MAAM,IAAI,CAACf,IAAI,CAACuD,SAAS,EAAE,OAAA;AAChC,QAAA,IAAI,CAAC7B,6BAA6B,CAAC7K,IAAI,CAAC,EAAE,OAAA;AAC1CkJ,QAAAA,0BAA0B,CACxBC,IAAI,EACJjI,IAAI,IAAIgB,KAAK,CAACgB,SAAS,CAAChC,IAAI,CAAC,EAC7B6B,mBAAmB,EACnBD,eAAe,EACfE,mBAAmB,EACnBC,WACF,CAAC,CAAA;AACH,OAAA;AACF,KAAA;GACD,CAAA;AACH,CAAC,CAAC;;;;;;"}