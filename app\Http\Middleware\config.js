module.exports = {
    port: process.env.APP_PORT || 8443,
    ssl: {
        enabled: process.env.APP_HTTPS === 'true',
        key: process.env.SSL_KEY_PATH || './ssl/privkey.pem',
        cert: process.env.SSL_CERT_PATH || './ssl/cert.pem'
    },
    database: {
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306,
        database: process.env.DB_DATABASE || 'casino',
        username: process.env.DB_USERNAME || 'casino',
        password: process.env.DB_PASSWORD || 'pinheiro123'
    },
    redis: {
        host: process.env.REDIS_HOST || '127.0.0.1',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD || null,
        // Use TCP connection instead of Unix socket
        path: null
    }
};
