"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.transform = transform;
var _core = require("@babel/core");
var _helperSkipTransparentExpressionWrappers = require("@babel/helper-skip-transparent-expression-wrappers");
var _util = require("./util");
const {
  ast
} = _core.template.expression;
function isSimpleMemberExpression(expression) {
  expression = (0, _helperSkipTransparentExpressionWrappers.skipTransparentExprWrapperNodes)(expression);
  return _core.types.isIdentifier(expression) || _core.types.isSuper(expression) || _core.types.isMemberExpression(expression) && !expression.computed && isSimpleMemberExpression(expression.object);
}
function needsMemoize(path) {
  let optionalPath = path;
  const {
    scope
  } = path;
  while (optionalPath.isOptionalMemberExpression() || optionalPath.isOptionalCallExpression()) {
    const {
      node
    } = optionalPath;
    const childPath = (0, _helperSkipTransparentExpressionWrappers.skipTransparentExprWrappers)(optionalPath.isOptionalMemberExpression() ? optionalPath.get("object") : optionalPath.get("callee"));
    if (node.optional) {
      return !scope.isStatic(childPath.node);
    }
    optionalPath = childPath;
  }
}
function transform(path, {
  pureGetters,
  noDocumentAll
}) {
  const {
    scope
  } = path;
  const maybeWrapped = (0, _util.findOutermostTransparentParent)(path);
  const {
    parentPath
  } = maybeWrapped;
  const willReplacementCastToBoolean = (0, _util.willPathCastToBoolean)(maybeWrapped);
  let isDeleteOperation = false;
  const parentIsCall = parentPath.isCallExpression({
    callee: maybeWrapped.node
  }) && path.isOptionalMemberExpression();
  const optionals = [];
  let optionalPath = path;
  if (scope.path.isPattern() && needsMemoize(optionalPath)) {
    path.replaceWith(_core.template.ast`(() => ${path.node})()`);
    return;
  }
  while (optionalPath.isOptionalMemberExpression() || optionalPath.isOptionalCallExpression()) {
    const {
      node
    } = optionalPath;
    if (node.optional) {
      optionals.push(node);
    }
    if (optionalPath.isOptionalMemberExpression()) {
      optionalPath.node.type = "MemberExpression";
      optionalPath = (0, _helperSkipTransparentExpressionWrappers.skipTransparentExprWrappers)(optionalPath.get("object"));
    } else if (optionalPath.isOptionalCallExpression()) {
      optionalPath.node.type = "CallExpression";
      optionalPath = (0, _helperSkipTransparentExpressionWrappers.skipTransparentExprWrappers)(optionalPath.get("callee"));
    }
  }
  let replacementPath = path;
  if (parentPath.isUnaryExpression({
    operator: "delete"
  })) {
    replacementPath = parentPath;
    isDeleteOperation = true;
  }
  for (let i = optionals.length - 1; i >= 0; i--) {
    const node = optionals[i];
    const isCall = _core.types.isCallExpression(node);
    const chainWithTypes = isCall ? node.callee : node.object;
    const chain = (0, _helperSkipTransparentExpressionWrappers.skipTransparentExprWrapperNodes)(chainWithTypes);
    let ref;
    let check;
    if (isCall && _core.types.isIdentifier(chain, {
      name: "eval"
    })) {
      check = ref = chain;
      node.callee = _core.types.sequenceExpression([_core.types.numericLiteral(0), ref]);
    } else if (pureGetters && isCall && isSimpleMemberExpression(chain)) {
      check = ref = node.callee;
    } else {
      ref = scope.maybeGenerateMemoised(chain);
      if (ref) {
        check = _core.types.assignmentExpression("=", _core.types.cloneNode(ref), chainWithTypes);
        isCall ? node.callee = ref : node.object = ref;
      } else {
        check = ref = chainWithTypes;
      }
    }
    if (isCall && _core.types.isMemberExpression(chain)) {
      if (pureGetters && isSimpleMemberExpression(chain)) {
        node.callee = chainWithTypes;
      } else {
        const {
          object
        } = chain;
        let context;
        if (_core.types.isSuper(object)) {
          context = _core.types.thisExpression();
        } else {
          const memoized = scope.maybeGenerateMemoised(object);
          if (memoized) {
            context = memoized;
            chain.object = _core.types.assignmentExpression("=", memoized, object);
          } else {
            context = object;
          }
        }
        node.arguments.unshift(_core.types.cloneNode(context));
        node.callee = _core.types.memberExpression(node.callee, _core.types.identifier("call"));
      }
    }
    let replacement = replacementPath.node;
    if (i === 0 && parentIsCall) {
      var _baseRef;
      const object = (0, _helperSkipTransparentExpressionWrappers.skipTransparentExprWrapperNodes)(replacement.object);
      let baseRef;
      if (!pureGetters || !isSimpleMemberExpression(object)) {
        baseRef = scope.maybeGenerateMemoised(object);
        if (baseRef) {
          replacement.object = _core.types.assignmentExpression("=", baseRef, object);
        }
      }
      replacement = _core.types.callExpression(_core.types.memberExpression(replacement, _core.types.identifier("bind")), [_core.types.cloneNode((_baseRef = baseRef) != null ? _baseRef : object)]);
    }
    if (willReplacementCastToBoolean) {
      const nonNullishCheck = noDocumentAll ? ast`${_core.types.cloneNode(check)} != null` : ast`
            ${_core.types.cloneNode(check)} !== null && ${_core.types.cloneNode(ref)} !== void 0`;
      replacementPath.replaceWith(_core.types.logicalExpression("&&", nonNullishCheck, replacement));
      replacementPath = (0, _helperSkipTransparentExpressionWrappers.skipTransparentExprWrappers)(replacementPath.get("right"));
    } else {
      const nullishCheck = noDocumentAll ? ast`${_core.types.cloneNode(check)} == null` : ast`
            ${_core.types.cloneNode(check)} === null || ${_core.types.cloneNode(ref)} === void 0`;
      const returnValue = isDeleteOperation ? ast`true` : ast`void 0`;
      replacementPath.replaceWith(_core.types.conditionalExpression(nullishCheck, returnValue, replacement));
      replacementPath = (0, _helperSkipTransparentExpressionWrappers.skipTransparentExprWrappers)(replacementPath.get("alternate"));
    }
  }
}

//# sourceMappingURL=transform.js.map
