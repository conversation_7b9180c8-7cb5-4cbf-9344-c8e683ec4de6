{"version": 3, "file": "index.node.mjs", "sources": ["../src/utils.ts", "../src/imports-cache.ts", "../src/debug-utils.ts", "../src/normalize-options.ts", "../src/visitors/usage.ts", "../src/visitors/entry.ts", "../src/node/dependencies.ts", "../src/meta-resolver.ts", "../src/index.ts"], "sourcesContent": ["import { types as t, template } from \"@babel/core\";\nimport type { NodePath } from \"@babel/traverse\";\nimport type { Utils } from \"./types\";\nimport type ImportsCache from \"./imports-cache\";\n\nexport function intersection<T>(a: Set<T>, b: Set<T>): Set<T> {\n  const result = new Set<T>();\n  a.forEach(v => b.has(v) && result.add(v));\n  return result;\n}\n\nexport function has(object: any, key: string) {\n  return Object.prototype.hasOwnProperty.call(object, key);\n}\n\nfunction getType(target: any): string {\n  return Object.prototype.toString.call(target).slice(8, -1);\n}\n\nfunction resolveId(path): string {\n  if (\n    path.isIdentifier() &&\n    !path.scope.hasBinding(path.node.name, /* noGlobals */ true)\n  ) {\n    return path.node.name;\n  }\n\n  const { deopt } = path.evaluate();\n  if (deopt && deopt.isIdentifier()) {\n    return deopt.node.name;\n  }\n}\n\nexport function resolveKey(\n  path: NodePath<t.Expression | t.PrivateName>,\n  computed: boolean = false,\n) {\n  const { scope } = path;\n  if (path.isStringLiteral()) return path.node.value;\n  const isIdentifier = path.isIdentifier();\n  if (\n    isIdentifier &&\n    !(computed || (path.parent as t.MemberExpression).computed)\n  ) {\n    return path.node.name;\n  }\n\n  if (\n    computed &&\n    path.isMemberExpression() &&\n    path.get(\"object\").isIdentifier({ name: \"Symbol\" }) &&\n    !scope.hasBinding(\"Symbol\", /* noGlobals */ true)\n  ) {\n    const sym = resolveKey(path.get(\"property\"), path.node.computed);\n    if (sym) return \"Symbol.\" + sym;\n  }\n\n  if (!isIdentifier || scope.hasBinding(path.node.name, /* noGlobals */ true)) {\n    const { value } = path.evaluate();\n    if (typeof value === \"string\") return value;\n  }\n}\n\nexport function resolveSource(obj: NodePath): {\n  id: string | null;\n  placement: \"prototype\" | \"static\" | null;\n} {\n  if (\n    obj.isMemberExpression() &&\n    obj.get(\"property\").isIdentifier({ name: \"prototype\" })\n  ) {\n    const id = resolveId(obj.get(\"object\"));\n\n    if (id) {\n      return { id, placement: \"prototype\" };\n    }\n    return { id: null, placement: null };\n  }\n\n  const id = resolveId(obj);\n  if (id) {\n    return { id, placement: \"static\" };\n  }\n\n  const { value } = obj.evaluate();\n  if (value !== undefined) {\n    return { id: getType(value), placement: \"prototype\" };\n  } else if (obj.isRegExpLiteral()) {\n    return { id: \"RegExp\", placement: \"prototype\" };\n  } else if (obj.isFunction()) {\n    return { id: \"Function\", placement: \"prototype\" };\n  }\n\n  return { id: null, placement: null };\n}\n\nexport function getImportSource({ node }: NodePath<t.ImportDeclaration>) {\n  if (node.specifiers.length === 0) return node.source.value;\n}\n\nexport function getRequireSource({ node }: NodePath<t.Statement>) {\n  if (!t.isExpressionStatement(node)) return;\n  const { expression } = node;\n  if (\n    t.isCallExpression(expression) &&\n    t.isIdentifier(expression.callee) &&\n    expression.callee.name === \"require\" &&\n    expression.arguments.length === 1 &&\n    t.isStringLiteral(expression.arguments[0])\n  ) {\n    return expression.arguments[0].value;\n  }\n}\n\nfunction hoist(node: t.Node) {\n  // @ts-expect-error\n  node._blockHoist = 3;\n  return node;\n}\n\nexport function createUtilsGetter(cache: ImportsCache) {\n  return (path: NodePath): Utils => {\n    const prog = path.findParent(p => p.isProgram()) as NodePath<t.Program>;\n\n    return {\n      injectGlobalImport(url) {\n        cache.storeAnonymous(prog, url, (isScript, source) => {\n          return isScript\n            ? template.statement.ast`require(${source})`\n            : t.importDeclaration([], source);\n        });\n      },\n      injectNamedImport(url, name, hint = name) {\n        return cache.storeNamed(prog, url, name, (isScript, source, name) => {\n          const id = prog.scope.generateUidIdentifier(hint);\n          return {\n            node: isScript\n              ? hoist(template.statement.ast`\n                  var ${id} = require(${source}).${name}\n                `)\n              : t.importDeclaration([t.importSpecifier(id, name)], source),\n            name: id.name,\n          };\n        });\n      },\n      injectDefaultImport(url, hint = url) {\n        return cache.storeNamed(prog, url, \"default\", (isScript, source) => {\n          const id = prog.scope.generateUidIdentifier(hint);\n          return {\n            node: isScript\n              ? hoist(template.statement.ast`var ${id} = require(${source})`)\n              : t.importDeclaration([t.importDefaultSpecifier(id)], source),\n            name: id.name,\n          };\n        });\n      },\n    };\n  };\n}\n", "import type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\n\ntype StrMap<K> = Map<string, K>;\n\nexport default class ImportsCache {\n  _imports: WeakMap<NodePath<t.Program>, StrMap<string>>;\n  _anonymousImports: WeakMap<NodePath<t.Program>, Set<string>>;\n  _lastImports: WeakMap<NodePath<t.Program>, NodePath<t.Node>>;\n  _resolver: (url: string) => string;\n\n  constructor(resolver: (url: string) => string) {\n    this._imports = new WeakMap();\n    this._anonymousImports = new WeakMap();\n    this._lastImports = new WeakMap();\n    this._resolver = resolver;\n  }\n\n  storeAnonymous(\n    programPath: NodePath<t.Program>,\n    url: string,\n    // eslint-disable-next-line no-undef\n    getVal: (isScript: boolean, source: t.StringLiteral) => t.Node,\n  ) {\n    const key = this._normalizeKey(programPath, url);\n    const imports = this._ensure<Set<string>>(\n      this._anonymousImports,\n      programPath,\n      Set,\n    );\n\n    if (imports.has(key)) return;\n\n    const node = getVal(\n      programPath.node.sourceType === \"script\",\n      t.stringLiteral(this._resolver(url)),\n    );\n    imports.add(key);\n    this._injectImport(programPath, node);\n  }\n\n  storeNamed(\n    programPath: NodePath<t.Program>,\n    url: string,\n    name: string,\n    getVal: (\n      isScript: boolean,\n      // eslint-disable-next-line no-undef\n      source: t.StringLiteral,\n      // eslint-disable-next-line no-undef\n      name: t.Identifier,\n    ) => { node: t.Node; name: string },\n  ) {\n    const key = this._normalizeKey(programPath, url, name);\n    const imports = this._ensure<Map<string, any>>(\n      this._imports,\n      programPath,\n      Map,\n    );\n\n    if (!imports.has(key)) {\n      const { node, name: id } = getVal(\n        programPath.node.sourceType === \"script\",\n        t.stringLiteral(this._resolver(url)),\n        t.identifier(name),\n      );\n      imports.set(key, id);\n      this._injectImport(programPath, node);\n    }\n\n    return t.identifier(imports.get(key));\n  }\n\n  _injectImport(programPath: NodePath<t.Program>, node: t.Node) {\n    const lastImport = this._lastImports.get(programPath);\n    let newNodes: [NodePath];\n    if (\n      lastImport &&\n      lastImport.node &&\n      // Sometimes the AST is modified and the \"last import\"\n      // we have has been replaced\n      lastImport.parent === programPath.node &&\n      lastImport.container === programPath.node.body\n    ) {\n      newNodes = lastImport.insertAfter(node);\n    } else {\n      newNodes = programPath.unshiftContainer(\"body\", node);\n    }\n    const newNode = newNodes[newNodes.length - 1];\n    this._lastImports.set(programPath, newNode);\n\n    /*\n    let lastImport;\n\n    programPath.get(\"body\").forEach(path => {\n      if (path.isImportDeclaration()) lastImport = path;\n      if (\n        path.isExpressionStatement() &&\n        isRequireCall(path.get(\"expression\"))\n      ) {\n        lastImport = path;\n      }\n      if (\n        path.isVariableDeclaration() &&\n        path.get(\"declarations\").length === 1 &&\n        (isRequireCall(path.get(\"declarations.0.init\")) ||\n          (path.get(\"declarations.0.init\").isMemberExpression() &&\n            isRequireCall(path.get(\"declarations.0.init.object\"))))\n      ) {\n        lastImport = path;\n      }\n    });*/\n  }\n\n  _ensure<C extends Map<string, any> | Set<string>>(\n    map: WeakMap<NodePath<t.Program>, C>,\n    programPath: NodePath<t.Program>,\n    Collection: { new (...args: any): C },\n  ): C {\n    let collection = map.get(programPath);\n    if (!collection) {\n      collection = new Collection();\n      map.set(programPath, collection);\n    }\n    return collection;\n  }\n\n  _normalizeKey(\n    programPath: NodePath<t.Program>,\n    url: string,\n    name: string = \"\",\n  ): string {\n    const { sourceType } = programPath.node;\n\n    // If we rely on the imported binding (the \"name\" parameter), we also need to cache\n    // based on the sourceType. This is because the module transforms change the names\n    // of the import variables.\n    return `${name && sourceType}::${url}::${name}`;\n  }\n}\n", "import { prettifyTargets } from \"@babel/helper-compilation-targets\";\n\nimport type { Targets } from \"./types\";\n\nexport const presetEnvSilentDebugHeader =\n  \"#__secret_key__@babel/preset-env__don't_log_debug_header_and_resolved_targets\";\n\nexport function stringifyTargetsMultiline(targets: Targets): string {\n  return JSON.stringify(prettifyTargets(targets), null, 2);\n}\n\nexport function stringifyTargets(targets: Targets): string {\n  return JSON.stringify(targets)\n    .replace(/,/g, \", \")\n    .replace(/^\\{\"/, '{ \"')\n    .replace(/\"\\}$/, '\" }');\n}\n", "import { intersection } from \"./utils\";\nimport type {\n  Pattern,\n  PluginOptions,\n  MissingDependenciesOption,\n} from \"./types\";\n\nfunction patternToRegExp(pattern: Pattern): RegExp | null {\n  if (pattern instanceof RegExp) return pattern;\n\n  try {\n    return new RegExp(`^${pattern}$`);\n  } catch {\n    return null;\n  }\n}\n\nfunction buildUnusedError(label, unused) {\n  if (!unused.length) return \"\";\n  return (\n    `  - The following \"${label}\" patterns didn't match any polyfill:\\n` +\n    unused.map(original => `    ${String(original)}\\n`).join(\"\")\n  );\n}\n\nfunction buldDuplicatesError(duplicates) {\n  if (!duplicates.size) return \"\";\n  return (\n    `  - The following polyfills were matched both by \"include\" and \"exclude\" patterns:\\n` +\n    Array.from(duplicates, name => `    ${name}\\n`).join(\"\")\n  );\n}\n\nexport function validateIncludeExclude(\n  provider: string,\n  polyfills: Set<string>,\n  includePatterns: Pattern[],\n  excludePatterns: Pattern[],\n) {\n  let current;\n  const filter = pattern => {\n    const regexp = patternToRegExp(pattern);\n    if (!regexp) return false;\n\n    let matched = false;\n    for (const polyfill of polyfills) {\n      if (regexp.test(polyfill)) {\n        matched = true;\n        current.add(polyfill);\n      }\n    }\n    return !matched;\n  };\n\n  // prettier-ignore\n  const include = current = new Set<string> ();\n  const unusedInclude = Array.from(includePatterns).filter(filter);\n\n  // prettier-ignore\n  const exclude = current = new Set<string> ();\n  const unusedExclude = Array.from(excludePatterns).filter(filter);\n\n  const duplicates = intersection(include, exclude);\n\n  if (\n    duplicates.size > 0 ||\n    unusedInclude.length > 0 ||\n    unusedExclude.length > 0\n  ) {\n    throw new Error(\n      `Error while validating the \"${provider}\" provider options:\\n` +\n        buildUnusedError(\"include\", unusedInclude) +\n        buildUnusedError(\"exclude\", unusedExclude) +\n        buldDuplicatesError(duplicates),\n    );\n  }\n\n  return { include, exclude };\n}\n\nexport function applyMissingDependenciesDefaults(\n  options: PluginOptions,\n  babelApi: any,\n): MissingDependenciesOption {\n  const { missingDependencies = {} } = options;\n  if (missingDependencies === false) return false;\n\n  const caller = babelApi.caller(caller => caller?.name);\n\n  const {\n    log = \"deferred\",\n    inject = caller === \"rollup-plugin-babel\" ? \"throw\" : \"import\",\n    all = false,\n  } = missingDependencies;\n\n  return { log, inject, all };\n}\n", "import type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\nimport type { MetaDescriptor } from \"../types\";\n\nimport { resolveKey, resolveSource } from \"../utils\";\n\nexport default (\n  callProvider: (payload: MetaDescriptor, path: NodePath) => void,\n) => {\n  function property(object, key, placement, path) {\n    return callProvider({ kind: \"property\", object, key, placement }, path);\n  }\n\n  return {\n    // Symbol(), new Promise\n    ReferencedIdentifier(path: NodePath<t.Identifier>) {\n      const {\n        node: { name },\n        scope,\n      } = path;\n      if (scope.getBindingIdentifier(name)) return;\n\n      callProvider({ kind: \"global\", name }, path);\n    },\n\n    MemberExpression(path: NodePath<t.MemberExpression>) {\n      const key = resolveKey(path.get(\"property\"), path.node.computed);\n      if (!key || key === \"prototype\") return;\n\n      const object = path.get(\"object\");\n      if (object.isIdentifier()) {\n        const binding = object.scope.getBinding(object.node.name);\n        if (binding && binding.path.isImportNamespaceSpecifier()) return;\n      }\n\n      const source = resolveSource(object);\n      return property(source.id, key, source.placement, path);\n    },\n\n    ObjectPattern(path: NodePath<t.ObjectPattern>) {\n      const { parentPath, parent } = path;\n      let obj;\n\n      // const { keys, values } = Object\n      if (parentPath.isVariableDeclarator()) {\n        obj = parentPath.get(\"init\");\n        // ({ keys, values } = Object)\n      } else if (parentPath.isAssignmentExpression()) {\n        obj = parentPath.get(\"right\");\n        // !function ({ keys, values }) {...} (Object)\n        // resolution does not work after properties transform :-(\n      } else if (parentPath.isFunction()) {\n        const grand = parentPath.parentPath;\n        if (grand.isCallExpression() || grand.isNewExpression()) {\n          if (grand.node.callee === parent) {\n            obj = grand.get(\"arguments\")[path.key];\n          }\n        }\n      }\n\n      let id = null;\n      let placement = null;\n      if (obj) ({ id, placement } = resolveSource(obj));\n\n      for (const prop of path.get(\"properties\")) {\n        if (prop.isObjectProperty()) {\n          const key = resolveKey(prop.get(\"key\"));\n          if (key) property(id, key, placement, prop);\n        }\n      }\n    },\n\n    BinaryExpression(path: NodePath<t.BinaryExpression>) {\n      if (path.node.operator !== \"in\") return;\n\n      const source = resolveSource(path.get(\"right\"));\n      const key = resolveKey(path.get(\"left\"), true);\n\n      if (!key) return;\n\n      callProvider(\n        {\n          kind: \"in\",\n          object: source.id,\n          key,\n          placement: source.placement,\n        },\n        path,\n      );\n    },\n  };\n};\n", "import type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\nimport type { MetaDescriptor } from \"../types\";\n\nimport { getImportSource, getRequireSource } from \"../utils\";\n\nexport default (\n  callProvider: (payload: MetaDescriptor, path: NodePath) => void,\n) => ({\n  ImportDeclaration(path: NodePath<t.ImportDeclaration>) {\n    const source = getImportSource(path);\n    if (!source) return;\n    callProvider({ kind: \"import\", source }, path);\n  },\n  Program(path: NodePath<t.Program>) {\n    path.get(\"body\").forEach(bodyPath => {\n      const source = getRequireSource(bodyPath);\n      if (!source) return;\n      callProvider({ kind: \"import\", source }, bodyPath);\n    });\n  },\n});\n", "import path from \"path\";\nimport debounce from \"lodash.debounce\";\nimport requireResolve from \"resolve\";\n\nconst nativeRequireResolve = parseFloat(process.versions.node) >= 8.9;\n\nimport { createRequire } from \"module\";\nconst require = createRequire(import /*::(_)*/.meta.url); // eslint-disable-line\n\nfunction myResolve(name: string, basedir: string) {\n  if (nativeRequireResolve) {\n    return require\n      .resolve(name, {\n        paths: [basedir],\n      })\n      .replace(/\\\\/g, \"/\");\n  } else {\n    return requireResolve.sync(name, { basedir }).replace(/\\\\/g, \"/\");\n  }\n}\n\nexport function resolve(\n  dirname: string,\n  moduleName: string,\n  absoluteImports: boolean | string,\n): string {\n  if (absoluteImports === false) return moduleName;\n\n  let basedir = dirname;\n  if (typeof absoluteImports === \"string\") {\n    basedir = path.resolve(basedir, absoluteImports);\n  }\n\n  try {\n    return myResolve(moduleName, basedir);\n  } catch (err) {\n    if (err.code !== \"MODULE_NOT_FOUND\") throw err;\n\n    throw Object.assign(\n      new Error(`Failed to resolve \"${moduleName}\" relative to \"${dirname}\"`),\n      {\n        code: \"BABEL_POLYFILL_NOT_FOUND\",\n        polyfill: moduleName,\n        dirname,\n      },\n    );\n  }\n}\n\nexport function has(basedir: string, name: string) {\n  try {\n    myResolve(name, basedir);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nexport function logMissing(missingDeps: Set<string>) {\n  if (missingDeps.size === 0) return;\n\n  const deps = Array.from(missingDeps).sort().join(\" \");\n\n  console.warn(\n    \"\\nSome polyfills have been added but are not present in your dependencies.\\n\" +\n      \"Please run one of the following commands:\\n\" +\n      `\\tnpm install --save ${deps}\\n` +\n      `\\tyarn add ${deps}\\n`,\n  );\n\n  process.exitCode = 1;\n}\n\nlet allMissingDeps = new Set<string>();\n\nconst laterLogMissingDependencies = debounce(() => {\n  logMissing(allMissingDeps);\n  allMissingDeps = new Set<string>();\n}, 100);\n\nexport function laterLogMissing(missingDeps: Set<string>) {\n  if (missingDeps.size === 0) return;\n\n  missingDeps.forEach(name => allMissingDeps.add(name));\n  laterLogMissingDependencies();\n}\n", "import type {\n  MetaDescriptor,\n  ResolverPolyfills,\n  ResolvedPolyfill,\n} from \"./types\";\n\nimport { has } from \"./utils\";\n\ntype ResolverFn<T> = (meta: MetaDescriptor) => void | ResolvedPolyfill<T>;\n\nconst PossibleGlobalObjects = new Set<string>([\n  \"global\",\n  \"globalThis\",\n  \"self\",\n  \"window\",\n]);\n\nexport default function createMetaResolver<T>(\n  polyfills: ResolverPolyfills<T>,\n): ResolverFn<T> {\n  const { static: staticP, instance: instanceP, global: globalP } = polyfills;\n\n  return meta => {\n    if (meta.kind === \"global\" && globalP && has(globalP, meta.name)) {\n      return { kind: \"global\", desc: globalP[meta.name], name: meta.name };\n    }\n\n    if (meta.kind === \"property\" || meta.kind === \"in\") {\n      const { placement, object, key } = meta;\n\n      if (object && placement === \"static\") {\n        if (globalP && PossibleGlobalObjects.has(object) && has(globalP, key)) {\n          return { kind: \"global\", desc: globalP[key], name: key };\n        }\n\n        if (staticP && has(staticP, object) && has(staticP[object], key)) {\n          return {\n            kind: \"static\",\n            desc: staticP[object][key],\n            name: `${object}$${key}`,\n          };\n        }\n      }\n\n      if (instanceP && has(instanceP, key)) {\n        return { kind: \"instance\", desc: instanceP[key], name: `${key}` };\n      }\n    }\n  };\n}\n", "import { declare } from \"@babel/helper-plugin-utils\";\nimport type { NodePath } from \"@babel/traverse\";\n\nimport _getTargets, {\n  isRequired,\n  getInclusionReasons,\n} from \"@babel/helper-compilation-targets\";\nconst getTargets = _getTargets.default || _getTargets;\n\nimport { createUtilsGetter } from \"./utils\";\nimport ImportsCache from \"./imports-cache\";\nimport {\n  stringifyTargetsMultiline,\n  presetEnvSilentDebugHeader,\n} from \"./debug-utils\";\nimport {\n  validateIncludeExclude,\n  applyMissingDependenciesDefaults,\n} from \"./normalize-options\";\n\nimport type {\n  ProviderApi,\n  MethodString,\n  Targets,\n  MetaDescriptor,\n  PolyfillProvider,\n  PluginOptions,\n  ProviderOptions,\n} from \"./types\";\n\nimport * as v from \"./visitors\";\nimport * as deps from \"./node/dependencies\";\n\nimport createMetaResolver from \"./meta-resolver\";\n\nexport type { PolyfillProvider, MetaDescriptor, Utils, Targets } from \"./types\";\n\nfunction resolveOptions<Options>(\n  options: PluginOptions,\n  babelApi,\n): {\n  method: MethodString;\n  methodName: \"usageGlobal\" | \"entryGlobal\" | \"usagePure\";\n  targets: Targets;\n  debug: boolean | typeof presetEnvSilentDebugHeader;\n  shouldInjectPolyfill:\n    | ((name: string, shouldInject: boolean) => boolean)\n    | undefined;\n  providerOptions: ProviderOptions<Options>;\n  absoluteImports: string | boolean;\n} {\n  const {\n    method,\n    targets: targetsOption,\n    ignoreBrowserslistConfig,\n    configPath,\n    debug,\n    shouldInjectPolyfill,\n    absoluteImports,\n    ...providerOptions\n  } = options;\n\n  if (isEmpty(options)) {\n    throw new Error(\n      `\\\nThis plugin requires options, for example:\n    {\n      \"plugins\": [\n        [\"<plugin name>\", { method: \"usage-pure\" }]\n      ]\n    }\n\nSee more options at https://github.com/babel/babel-polyfills/blob/main/docs/usage.md`,\n    );\n  }\n\n  let methodName;\n  if (method === \"usage-global\") methodName = \"usageGlobal\";\n  else if (method === \"entry-global\") methodName = \"entryGlobal\";\n  else if (method === \"usage-pure\") methodName = \"usagePure\";\n  else if (typeof method !== \"string\") {\n    throw new Error(\".method must be a string\");\n  } else {\n    throw new Error(\n      `.method must be one of \"entry-global\", \"usage-global\"` +\n        ` or \"usage-pure\" (received ${JSON.stringify(method)})`,\n    );\n  }\n\n  if (typeof shouldInjectPolyfill === \"function\") {\n    if (options.include || options.exclude) {\n      throw new Error(\n        `.include and .exclude are not supported when using the` +\n          ` .shouldInjectPolyfill function.`,\n      );\n    }\n  } else if (shouldInjectPolyfill != null) {\n    throw new Error(\n      `.shouldInjectPolyfill must be a function, or undefined` +\n        ` (received ${JSON.stringify(shouldInjectPolyfill)})`,\n    );\n  }\n\n  if (\n    absoluteImports != null &&\n    typeof absoluteImports !== \"boolean\" &&\n    typeof absoluteImports !== \"string\"\n  ) {\n    throw new Error(\n      `.absoluteImports must be a boolean, a string, or undefined` +\n        ` (received ${JSON.stringify(absoluteImports)})`,\n    );\n  }\n\n  let targets;\n\n  if (\n    // If any browserslist-related option is specified, fallback to the old\n    // behavior of not using the targets specified in the top-level options.\n    targetsOption ||\n    configPath ||\n    ignoreBrowserslistConfig\n  ) {\n    const targetsObj =\n      typeof targetsOption === \"string\" || Array.isArray(targetsOption)\n        ? { browsers: targetsOption }\n        : targetsOption;\n\n    targets = getTargets(targetsObj, {\n      ignoreBrowserslistConfig,\n      configPath,\n    });\n  } else {\n    targets = babelApi.targets();\n  }\n\n  return {\n    method,\n    methodName,\n    targets,\n    absoluteImports: absoluteImports ?? false,\n    shouldInjectPolyfill,\n    debug: !!debug,\n    providerOptions: providerOptions as any as ProviderOptions<Options>,\n  };\n}\n\nfunction instantiateProvider<Options>(\n  factory: PolyfillProvider<Options>,\n  options: PluginOptions,\n  missingDependencies,\n  dirname,\n  debugLog,\n  babelApi,\n) {\n  const {\n    method,\n    methodName,\n    targets,\n    debug,\n    shouldInjectPolyfill,\n    providerOptions,\n    absoluteImports,\n  } = resolveOptions<Options>(options, babelApi);\n\n  const getUtils = createUtilsGetter(\n    new ImportsCache(moduleName =>\n      deps.resolve(dirname, moduleName, absoluteImports),\n    ),\n  );\n\n  // eslint-disable-next-line prefer-const\n  let include, exclude;\n  let polyfillsSupport;\n  let polyfillsNames;\n  let filterPolyfills;\n\n  const depsCache = new Map();\n\n  const api: ProviderApi = {\n    babel: babelApi,\n    getUtils,\n    method: options.method,\n    targets,\n    createMetaResolver,\n    shouldInjectPolyfill(name) {\n      if (polyfillsNames === undefined) {\n        throw new Error(\n          `Internal error in the ${factory.name} provider: ` +\n            `shouldInjectPolyfill() can't be called during initialization.`,\n        );\n      }\n      if (!polyfillsNames.has(name)) {\n        console.warn(\n          `Internal error in the ${providerName} provider: ` +\n            `unknown polyfill \"${name}\".`,\n        );\n      }\n\n      if (filterPolyfills && !filterPolyfills(name)) return false;\n\n      let shouldInject = isRequired(name, targets, {\n        compatData: polyfillsSupport,\n        includes: include,\n        excludes: exclude,\n      });\n\n      if (shouldInjectPolyfill) {\n        shouldInject = shouldInjectPolyfill(name, shouldInject);\n        if (typeof shouldInject !== \"boolean\") {\n          throw new Error(`.shouldInjectPolyfill must return a boolean.`);\n        }\n      }\n\n      return shouldInject;\n    },\n    debug(name) {\n      debugLog().found = true;\n\n      if (!debug || !name) return;\n\n      if (debugLog().polyfills.has(providerName)) return;\n      debugLog().polyfills.add(name);\n      debugLog().polyfillsSupport ??= polyfillsSupport;\n    },\n    assertDependency(name, version = \"*\") {\n      if (missingDependencies === false) return;\n      if (absoluteImports) {\n        // If absoluteImports is not false, we will try resolving\n        // the dependency and throw if it's not possible. We can\n        // skip the check here.\n        return;\n      }\n\n      const dep = version === \"*\" ? name : `${name}@^${version}`;\n\n      const found = missingDependencies.all\n        ? false\n        : mapGetOr(depsCache, `${name} :: ${dirname}`, () =>\n            deps.has(dirname, name),\n          );\n\n      if (!found) {\n        debugLog().missingDeps.add(dep);\n      }\n    },\n  };\n\n  const provider = factory(api, providerOptions, dirname);\n  const providerName = provider.name || factory.name;\n\n  if (typeof provider[methodName] !== \"function\") {\n    throw new Error(\n      `The \"${providerName}\" provider doesn't support the \"${method}\" polyfilling method.`,\n    );\n  }\n\n  if (Array.isArray(provider.polyfills)) {\n    polyfillsNames = new Set(provider.polyfills);\n    filterPolyfills = provider.filterPolyfills;\n  } else if (provider.polyfills) {\n    polyfillsNames = new Set(Object.keys(provider.polyfills));\n    polyfillsSupport = provider.polyfills;\n    filterPolyfills = provider.filterPolyfills;\n  } else {\n    polyfillsNames = new Set();\n  }\n\n  ({ include, exclude } = validateIncludeExclude(\n    providerName,\n    polyfillsNames,\n    providerOptions.include || [],\n    providerOptions.exclude || [],\n  ));\n\n  return {\n    debug,\n    method,\n    targets,\n    provider,\n    providerName,\n    callProvider(payload: MetaDescriptor, path: NodePath) {\n      const utils = getUtils(path);\n      provider[methodName](payload, utils, path);\n    },\n  };\n}\n\nexport default function definePolyfillProvider<Options>(\n  factory: PolyfillProvider<Options>,\n) {\n  return declare((babelApi, options: PluginOptions, dirname: string) => {\n    babelApi.assertVersion(7);\n    const { traverse } = babelApi;\n\n    let debugLog;\n\n    const missingDependencies = applyMissingDependenciesDefaults(\n      options,\n      babelApi,\n    );\n\n    const { debug, method, targets, provider, providerName, callProvider } =\n      instantiateProvider<Options>(\n        factory,\n        options,\n        missingDependencies,\n        dirname,\n        () => debugLog,\n        babelApi,\n      );\n\n    const createVisitor = method === \"entry-global\" ? v.entry : v.usage;\n\n    const visitor = provider.visitor\n      ? traverse.visitors.merge([createVisitor(callProvider), provider.visitor])\n      : createVisitor(callProvider);\n\n    if (debug && debug !== presetEnvSilentDebugHeader) {\n      console.log(`${providerName}: \\`DEBUG\\` option`);\n      console.log(`\\nUsing targets: ${stringifyTargetsMultiline(targets)}`);\n      console.log(`\\nUsing polyfills with \\`${method}\\` method:`);\n    }\n\n    const { runtimeName } = provider;\n\n    return {\n      name: \"inject-polyfills\",\n      visitor,\n\n      pre(file) {\n        if (runtimeName) {\n          if (\n            file.get(\"runtimeHelpersModuleName\") &&\n            file.get(\"runtimeHelpersModuleName\") !== runtimeName\n          ) {\n            console.warn(\n              `Two different polyfill providers` +\n                ` (${file.get(\"runtimeHelpersModuleProvider\")}` +\n                ` and ${providerName}) are trying to define two` +\n                ` conflicting @babel/runtime alternatives:` +\n                ` ${file.get(\"runtimeHelpersModuleName\")} and ${runtimeName}.` +\n                ` The second one will be ignored.`,\n            );\n          } else {\n            file.set(\"runtimeHelpersModuleName\", runtimeName);\n            file.set(\"runtimeHelpersModuleProvider\", providerName);\n          }\n        }\n\n        debugLog = {\n          polyfills: new Set(),\n          polyfillsSupport: undefined,\n          found: false,\n          providers: new Set(),\n          missingDeps: new Set(),\n        };\n\n        provider.pre?.apply(this, arguments);\n      },\n      post() {\n        provider.post?.apply(this, arguments);\n\n        if (missingDependencies !== false) {\n          if (missingDependencies.log === \"per-file\") {\n            deps.logMissing(debugLog.missingDeps);\n          } else {\n            deps.laterLogMissing(debugLog.missingDeps);\n          }\n        }\n\n        if (!debug) return;\n\n        if (this.filename) console.log(`\\n[${this.filename}]`);\n\n        if (debugLog.polyfills.size === 0) {\n          console.log(\n            method === \"entry-global\"\n              ? debugLog.found\n                ? `Based on your targets, the ${providerName} polyfill did not add any polyfill.`\n                : `The entry point for the ${providerName} polyfill has not been found.`\n              : `Based on your code and targets, the ${providerName} polyfill did not add any polyfill.`,\n          );\n\n          return;\n        }\n\n        if (method === \"entry-global\") {\n          console.log(\n            `The ${providerName} polyfill entry has been replaced with ` +\n              `the following polyfills:`,\n          );\n        } else {\n          console.log(\n            `The ${providerName} polyfill added the following polyfills:`,\n          );\n        }\n\n        for (const name of debugLog.polyfills) {\n          if (debugLog.polyfillsSupport?.[name]) {\n            const filteredTargets = getInclusionReasons(\n              name,\n              targets,\n              debugLog.polyfillsSupport,\n            );\n\n            const formattedTargets = JSON.stringify(filteredTargets)\n              .replace(/,/g, \", \")\n              .replace(/^\\{\"/, '{ \"')\n              .replace(/\"\\}$/, '\" }');\n\n            console.log(`  ${name} ${formattedTargets}`);\n          } else {\n            console.log(`  ${name}`);\n          }\n        }\n      },\n    };\n  });\n}\n\nfunction mapGetOr(map, key, getDefault) {\n  let val = map.get(key);\n  if (val === undefined) {\n    val = getDefault();\n    map.set(key, val);\n  }\n  return val;\n}\n\nfunction isEmpty(obj) {\n  return Object.keys(obj).length === 0;\n}\n"], "names": ["types", "t", "template", "intersection", "a", "b", "result", "Set", "for<PERSON>ach", "v", "has", "add", "object", "key", "Object", "prototype", "hasOwnProperty", "call", "getType", "target", "toString", "slice", "resolveId", "path", "isIdentifier", "scope", "hasBinding", "node", "name", "de<PERSON>t", "evaluate", "<PERSON><PERSON><PERSON>", "computed", "isStringLiteral", "value", "parent", "isMemberExpression", "get", "sym", "resolveSource", "obj", "id", "placement", "undefined", "isRegExpLiteral", "isFunction", "getImportSource", "specifiers", "length", "source", "getRequireSource", "isExpressionStatement", "expression", "isCallExpression", "callee", "arguments", "hoist", "_blockHoist", "createUtilsGetter", "cache", "prog", "findParent", "p", "isProgram", "injectGlobalImport", "url", "storeAnonymous", "isScript", "statement", "ast", "importDeclaration", "injectNamedImport", "hint", "storeNamed", "generateUidIdentifier", "importSpecifier", "injectDefaultImport", "importDefaultSpecifier", "ImportsCache", "constructor", "resolver", "_imports", "WeakMap", "_anonymousImports", "_lastImports", "_resolver", "programPath", "getVal", "_normalizeKey", "imports", "_ensure", "sourceType", "stringLiteral", "_injectImport", "Map", "identifier", "set", "lastImport", "newNodes", "container", "body", "insertAfter", "unshiftContainer", "newNode", "map", "Collection", "collection", "presetEnvSilentDebugHeader", "stringifyTargetsMultiline", "targets", "JSON", "stringify", "prettifyTargets", "patternToRegExp", "pattern", "RegExp", "buildUnusedError", "label", "unused", "original", "String", "join", "buldDuplicatesError", "duplicates", "size", "Array", "from", "validateIncludeExclude", "provider", "polyfills", "includePatterns", "excludePatterns", "current", "filter", "regexp", "matched", "polyfill", "test", "include", "unusedInclude", "exclude", "unusedExclude", "Error", "applyMissingDependenciesDefaults", "options", "babelApi", "missingDependencies", "caller", "log", "inject", "all", "callProvider", "property", "kind", "ReferencedIdentifier", "getBindingIdentifier", "MemberExpression", "binding", "getBinding", "isImportNamespaceSpecifier", "ObjectPattern", "parentPath", "isVariableDeclarator", "isAssignmentExpression", "grand", "isNewExpression", "prop", "isObjectProperty", "BinaryExpression", "operator", "ImportDeclaration", "Program", "bodyPath", "nativeRequireResolve", "parseFloat", "process", "versions", "require", "createRequire", "import", "meta", "myResolve", "basedir", "resolve", "paths", "replace", "requireResolve", "sync", "dirname", "moduleName", "absoluteImports", "err", "code", "assign", "logMissing", "missingDeps", "deps", "sort", "console", "warn", "exitCode", "allMissingDeps", "laterLogMissingDependencies", "debounce", "laterLog<PERSON><PERSON>ing", "PossibleGlobalObjects", "createMetaResolver", "static", "staticP", "instance", "instanceP", "global", "globalP", "desc", "getTargets", "_getTargets", "default", "resolveOptions", "method", "targetsOption", "ignoreBrowserslistConfig", "config<PERSON><PERSON>", "debug", "shouldInjectPolyfill", "providerOptions", "isEmpty", "methodName", "targetsObj", "isArray", "browsers", "instantiateProvider", "factory", "debugLog", "getUtils", "polyfillsSupport", "polyfillsNames", "filterPolyfills", "deps<PERSON>ache", "api", "babel", "providerName", "shouldInject", "isRequired", "compatData", "includes", "excludes", "found", "assertDependency", "version", "dep", "mapGetOr", "keys", "payload", "utils", "definePolyfillProvider", "declare", "assertVersion", "traverse", "createVisitor", "visitor", "visitors", "merge", "runtimeName", "pre", "file", "providers", "apply", "post", "filename", "filteredTargets", "getInclusionReasons", "formattedTargets", "getDefault", "val"], "mappings": ";;;;;;;;;EAASA,OAASC;EAAGC,UAAAA;;AAKd,SAASC,YAAT,CAAyBC,CAAzB,EAAoCC,CAApC,EAAuD;EAC5D,MAAMC,MAAM,GAAG,IAAIC,GAAJ,EAAf;EACAH,CAAC,CAACI,OAAF,CAAUC,CAAC,IAAIJ,CAAC,CAACK,GAAF,CAAMD,CAAN,KAAYH,MAAM,CAACK,GAAP,CAAWF,CAAX,CAA3B;EACA,OAAOH,MAAP;AACD;AAEM,SAASI,KAAT,CAAaE,MAAb,EAA0BC,GAA1B,EAAuC;EAC5C,OAAOC,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCL,MAArC,EAA6CC,GAA7C,CAAP;AACD;;AAED,SAASK,OAAT,CAAiBC,MAAjB,EAAsC;EACpC,OAAOL,MAAM,CAACC,SAAP,CAAiBK,QAAjB,CAA0BH,IAA1B,CAA+BE,MAA/B,EAAuCE,KAAvC,CAA6C,CAA7C,EAAgD,CAAC,CAAjD,CAAP;AACD;;AAED,SAASC,SAAT,CAAmBC,IAAnB,EAAiC;EAC/B,IACEA,IAAI,CAACC,YAAL,MACA,CAACD,IAAI,CAACE,KAAL,CAAWC,UAAX,CAAsBH,IAAI,CAACI,IAAL,CAAUC,IAAhC;;EAAsD,IAAtD,CAFH,EAGE;IACA,OAAOL,IAAI,CAACI,IAAL,CAAUC,IAAjB;;;EAGF,MAAM;IAAEC;MAAUN,IAAI,CAACO,QAAL,EAAlB;;EACA,IAAID,KAAK,IAAIA,KAAK,CAACL,YAAN,EAAb,EAAmC;IACjC,OAAOK,KAAK,CAACF,IAAN,CAAWC,IAAlB;;AAEH;;AAEM,SAASG,UAAT,CACLR,IADK,EAELS,QAAiB,GAAG,KAFf,EAGL;EACA,MAAM;IAAEP;MAAUF,IAAlB;EACA,IAAIA,IAAI,CAACU,eAAL,EAAJ,EAA4B,OAAOV,IAAI,CAACI,IAAL,CAAUO,KAAjB;EAC5B,MAAMV,YAAY,GAAGD,IAAI,CAACC,YAAL,EAArB;;EACA,IACEA,YAAY,IACZ,EAAEQ,QAAQ,IAAKT,IAAI,CAACY,MAAN,CAAoCH,QAAlD,CAFF,EAGE;IACA,OAAOT,IAAI,CAACI,IAAL,CAAUC,IAAjB;;;EAGF,IACEI,QAAQ,IACRT,IAAI,CAACa,kBAAL,EADA,IAEAb,IAAI,CAACc,GAAL,CAAS,QAAT,EAAmBb,YAAnB,CAAgC;IAAEI,IAAI,EAAE;GAAxC,CAFA,IAGA,CAACH,KAAK,CAACC,UAAN,CAAiB,QAAjB;;EAA2C,IAA3C,CAJH,EAKE;IACA,MAAMY,GAAG,GAAGP,UAAU,CAACR,IAAI,CAACc,GAAL,CAAS,UAAT,CAAD,EAAuBd,IAAI,CAACI,IAAL,CAAUK,QAAjC,CAAtB;IACA,IAAIM,GAAJ,EAAS,OAAO,YAAYA,GAAnB;;;EAGX,IAAI,CAACd,YAAD,IAAiBC,KAAK,CAACC,UAAN,CAAiBH,IAAI,CAACI,IAAL,CAAUC,IAA3B;;EAAiD,IAAjD,CAArB,EAA6E;IAC3E,MAAM;MAAEM;QAAUX,IAAI,CAACO,QAAL,EAAlB;IACA,IAAI,OAAOI,KAAP,KAAiB,QAArB,EAA+B,OAAOA,KAAP;;AAElC;AAEM,SAASK,aAAT,CAAuBC,GAAvB,EAGL;EACA,IACEA,GAAG,CAACJ,kBAAJ,MACAI,GAAG,CAACH,GAAJ,CAAQ,UAAR,EAAoBb,YAApB,CAAiC;IAAEI,IAAI,EAAE;GAAzC,CAFF,EAGE;IACA,MAAMa,EAAE,GAAGnB,SAAS,CAACkB,GAAG,CAACH,GAAJ,CAAQ,QAAR,CAAD,CAApB;;IAEA,IAAII,EAAJ,EAAQ;MACN,OAAO;QAAEA,EAAF;QAAMC,SAAS,EAAE;OAAxB;;;IAEF,OAAO;MAAED,EAAE,EAAE,IAAN;MAAYC,SAAS,EAAE;KAA9B;;;EAGF,MAAMD,EAAE,GAAGnB,SAAS,CAACkB,GAAD,CAApB;;EACA,IAAIC,EAAJ,EAAQ;IACN,OAAO;MAAEA,EAAF;MAAMC,SAAS,EAAE;KAAxB;;;EAGF,MAAM;IAAER;MAAUM,GAAG,CAACV,QAAJ,EAAlB;;EACA,IAAII,KAAK,KAAKS,SAAd,EAAyB;IACvB,OAAO;MAAEF,EAAE,EAAEvB,OAAO,CAACgB,KAAD,CAAb;MAAsBQ,SAAS,EAAE;KAAxC;GADF,MAEO,IAAIF,GAAG,CAACI,eAAJ,EAAJ,EAA2B;IAChC,OAAO;MAAEH,EAAE,EAAE,QAAN;MAAgBC,SAAS,EAAE;KAAlC;GADK,MAEA,IAAIF,GAAG,CAACK,UAAJ,EAAJ,EAAsB;IAC3B,OAAO;MAAEJ,EAAE,EAAE,UAAN;MAAkBC,SAAS,EAAE;KAApC;;;EAGF,OAAO;IAAED,EAAE,EAAE,IAAN;IAAYC,SAAS,EAAE;GAA9B;AACD;AAEM,SAASI,eAAT,CAAyB;EAAEnB;AAAF,CAAzB,EAAkE;EACvE,IAAIA,IAAI,CAACoB,UAAL,CAAgBC,MAAhB,KAA2B,CAA/B,EAAkC,OAAOrB,IAAI,CAACsB,MAAL,CAAYf,KAAnB;AACnC;AAEM,SAASgB,gBAAT,CAA0B;EAAEvB;AAAF,CAA1B,EAA2D;EAChE,IAAI,CAAC1B,GAAC,CAACkD,qBAAF,CAAwBxB,IAAxB,CAAL,EAAoC;EACpC,MAAM;IAAEyB;MAAezB,IAAvB;;EACA,IACE1B,GAAC,CAACoD,gBAAF,CAAmBD,UAAnB,KACAnD,GAAC,CAACuB,YAAF,CAAe4B,UAAU,CAACE,MAA1B,CADA,IAEAF,UAAU,CAACE,MAAX,CAAkB1B,IAAlB,KAA2B,SAF3B,IAGAwB,UAAU,CAACG,SAAX,CAAqBP,MAArB,KAAgC,CAHhC,IAIA/C,GAAC,CAACgC,eAAF,CAAkBmB,UAAU,CAACG,SAAX,CAAqB,CAArB,CAAlB,CALF,EAME;IACA,OAAOH,UAAU,CAACG,SAAX,CAAqB,CAArB,EAAwBrB,KAA/B;;AAEH;;AAED,SAASsB,KAAT,CAAe7B,IAAf,EAA6B;;EAE3BA,IAAI,CAAC8B,WAAL,GAAmB,CAAnB;EACA,OAAO9B,IAAP;AACD;;AAEM,SAAS+B,iBAAT,CAA2BC,KAA3B,EAAgD;EACrD,OAAQpC,IAAD,IAA2B;IAChC,MAAMqC,IAAI,GAAGrC,IAAI,CAACsC,UAAL,CAAgBC,CAAC,IAAIA,CAAC,CAACC,SAAF,EAArB,CAAb;IAEA,OAAO;MACLC,kBAAkB,CAACC,GAAD,EAAM;QACtBN,KAAK,CAACO,cAAN,CAAqBN,IAArB,EAA2BK,GAA3B,EAAgC,CAACE,QAAD,EAAWlB,MAAX,KAAsB;UACpD,OAAOkB,QAAQ,GACXjE,QAAQ,CAACkE,SAAT,CAAmBC,GAAI,WAAUpB,MAAO,GAD7B,GAEXhD,GAAC,CAACqE,iBAAF,CAAoB,EAApB,EAAwBrB,MAAxB,CAFJ;SADF;OAFG;;MAQLsB,iBAAiB,CAACN,GAAD,EAAMrC,IAAN,EAAY4C,IAAI,GAAG5C,IAAnB,EAAyB;QACxC,OAAO+B,KAAK,CAACc,UAAN,CAAiBb,IAAjB,EAAuBK,GAAvB,EAA4BrC,IAA5B,EAAkC,CAACuC,QAAD,EAAWlB,MAAX,EAAmBrB,IAAnB,KAA4B;UACnE,MAAMa,EAAE,GAAGmB,IAAI,CAACnC,KAAL,CAAWiD,qBAAX,CAAiCF,IAAjC,CAAX;UACA,OAAO;YACL7C,IAAI,EAAEwC,QAAQ,GACVX,KAAK,CAACtD,QAAQ,CAACkE,SAAT,CAAmBC,GAAI;AAC7C,wBAAwB5B,EAAG,cAAaQ,MAAO,KAAIrB,IAAK;AACxD,iBAFqB,CADK,GAIV3B,GAAC,CAACqE,iBAAF,CAAoB,CAACrE,GAAC,CAAC0E,eAAF,CAAkBlC,EAAlB,EAAsBb,IAAtB,CAAD,CAApB,EAAmDqB,MAAnD,CALC;YAMLrB,IAAI,EAAEa,EAAE,CAACb;WANX;SAFK,CAAP;OATG;;MAqBLgD,mBAAmB,CAACX,GAAD,EAAMO,IAAI,GAAGP,GAAb,EAAkB;QACnC,OAAON,KAAK,CAACc,UAAN,CAAiBb,IAAjB,EAAuBK,GAAvB,EAA4B,SAA5B,EAAuC,CAACE,QAAD,EAAWlB,MAAX,KAAsB;UAClE,MAAMR,EAAE,GAAGmB,IAAI,CAACnC,KAAL,CAAWiD,qBAAX,CAAiCF,IAAjC,CAAX;UACA,OAAO;YACL7C,IAAI,EAAEwC,QAAQ,GACVX,KAAK,CAACtD,QAAQ,CAACkE,SAAT,CAAmBC,GAAI,OAAM5B,EAAG,cAAaQ,MAAO,GAArD,CADK,GAEVhD,GAAC,CAACqE,iBAAF,CAAoB,CAACrE,GAAC,CAAC4E,sBAAF,CAAyBpC,EAAzB,CAAD,CAApB,EAAoDQ,MAApD,CAHC;YAILrB,IAAI,EAAEa,EAAE,CAACb;WAJX;SAFK,CAAP;;;KAtBJ;GAHF;AAqCD;;;EC7JQ5B,OAASC;;AAIH,MAAM6E,YAAN,CAAmB;EAMhCC,WAAW,CAACC,QAAD,EAAoC;IAC7C,KAAKC,QAAL,GAAgB,IAAIC,OAAJ,EAAhB;IACA,KAAKC,iBAAL,GAAyB,IAAID,OAAJ,EAAzB;IACA,KAAKE,YAAL,GAAoB,IAAIF,OAAJ,EAApB;IACA,KAAKG,SAAL,GAAiBL,QAAjB;;;EAGFd,cAAc,CACZoB,WADY,EAEZrB,GAFY;EAIZsB,MAJY,EAKZ;IACA,MAAM1E,GAAG,GAAG,KAAK2E,aAAL,CAAmBF,WAAnB,EAAgCrB,GAAhC,CAAZ;;IACA,MAAMwB,OAAO,GAAG,KAAKC,OAAL,CACd,KAAKP,iBADS,EAEdG,WAFc,EAGd/E,GAHc,CAAhB;;IAMA,IAAIkF,OAAO,CAAC/E,GAAR,CAAYG,GAAZ,CAAJ,EAAsB;IAEtB,MAAMc,IAAI,GAAG4D,MAAM,CACjBD,WAAW,CAAC3D,IAAZ,CAAiBgE,UAAjB,KAAgC,QADf,EAEjB1F,CAAC,CAAC2F,aAAF,CAAgB,KAAKP,SAAL,CAAepB,GAAf,CAAhB,CAFiB,CAAnB;IAIAwB,OAAO,CAAC9E,GAAR,CAAYE,GAAZ;;IACA,KAAKgF,aAAL,CAAmBP,WAAnB,EAAgC3D,IAAhC;;;EAGF8C,UAAU,CACRa,WADQ,EAERrB,GAFQ,EAGRrC,IAHQ,EAIR2D,MAJQ,EAWR;IACA,MAAM1E,GAAG,GAAG,KAAK2E,aAAL,CAAmBF,WAAnB,EAAgCrB,GAAhC,EAAqCrC,IAArC,CAAZ;;IACA,MAAM6D,OAAO,GAAG,KAAKC,OAAL,CACd,KAAKT,QADS,EAEdK,WAFc,EAGdQ,GAHc,CAAhB;;IAMA,IAAI,CAACL,OAAO,CAAC/E,GAAR,CAAYG,GAAZ,CAAL,EAAuB;MACrB,MAAM;QAAEc,IAAF;QAAQC,IAAI,EAAEa;UAAO8C,MAAM,CAC/BD,WAAW,CAAC3D,IAAZ,CAAiBgE,UAAjB,KAAgC,QADD,EAE/B1F,CAAC,CAAC2F,aAAF,CAAgB,KAAKP,SAAL,CAAepB,GAAf,CAAhB,CAF+B,EAG/BhE,CAAC,CAAC8F,UAAF,CAAanE,IAAb,CAH+B,CAAjC;MAKA6D,OAAO,CAACO,GAAR,CAAYnF,GAAZ,EAAiB4B,EAAjB;;MACA,KAAKoD,aAAL,CAAmBP,WAAnB,EAAgC3D,IAAhC;;;IAGF,OAAO1B,CAAC,CAAC8F,UAAF,CAAaN,OAAO,CAACpD,GAAR,CAAYxB,GAAZ,CAAb,CAAP;;;EAGFgF,aAAa,CAACP,WAAD,EAAmC3D,IAAnC,EAAiD;IAC5D,MAAMsE,UAAU,GAAG,KAAKb,YAAL,CAAkB/C,GAAlB,CAAsBiD,WAAtB,CAAnB;;IACA,IAAIY,QAAJ;;IACA,IACED,UAAU,IACVA,UAAU,CAACtE,IADX;;IAIAsE,UAAU,CAAC9D,MAAX,KAAsBmD,WAAW,CAAC3D,IAJlC,IAKAsE,UAAU,CAACE,SAAX,KAAyBb,WAAW,CAAC3D,IAAZ,CAAiByE,IAN5C,EAOE;MACAF,QAAQ,GAAGD,UAAU,CAACI,WAAX,CAAuB1E,IAAvB,CAAX;KARF,MASO;MACLuE,QAAQ,GAAGZ,WAAW,CAACgB,gBAAZ,CAA6B,MAA7B,EAAqC3E,IAArC,CAAX;;;IAEF,MAAM4E,OAAO,GAAGL,QAAQ,CAACA,QAAQ,CAAClD,MAAT,GAAkB,CAAnB,CAAxB;;IACA,KAAKoC,YAAL,CAAkBY,GAAlB,CAAsBV,WAAtB,EAAmCiB,OAAnC;;AAGJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;EAIEb,OAAO,CACLc,GADK,EAELlB,WAFK,EAGLmB,UAHK,EAIF;IACH,IAAIC,UAAU,GAAGF,GAAG,CAACnE,GAAJ,CAAQiD,WAAR,CAAjB;;IACA,IAAI,CAACoB,UAAL,EAAiB;MACfA,UAAU,GAAG,IAAID,UAAJ,EAAb;MACAD,GAAG,CAACR,GAAJ,CAAQV,WAAR,EAAqBoB,UAArB;;;IAEF,OAAOA,UAAP;;;EAGFlB,aAAa,CACXF,WADW,EAEXrB,GAFW,EAGXrC,IAAY,GAAG,EAHJ,EAIH;IACR,MAAM;MAAE+D;QAAeL,WAAW,CAAC3D,IAAnC,CADQ;;;;IAMR,OAAQ,GAAEC,IAAI,IAAI+D,UAAW,KAAI1B,GAAI,KAAIrC,IAAK,EAA9C;;;AApI8B;;ACD3B,MAAM+E,0BAA0B,GACrC,+EADK;AAGA,SAASC,yBAAT,CAAmCC,OAAnC,EAA6D;EAClE,OAAOC,IAAI,CAACC,SAAL,CAAeC,eAAe,CAACH,OAAD,CAA9B,EAAyC,IAAzC,EAA+C,CAA/C,CAAP;AACD;;ACFD,SAASI,eAAT,CAAyBC,OAAzB,EAA0D;EACxD,IAAIA,OAAO,YAAYC,MAAvB,EAA+B,OAAOD,OAAP;;EAE/B,IAAI;IACF,OAAO,IAAIC,MAAJ,CAAY,IAAGD,OAAQ,GAAvB,CAAP;GADF,CAEE,MAAM;IACN,OAAO,IAAP;;AAEH;;AAED,SAASE,gBAAT,CAA0BC,KAA1B,EAAiCC,MAAjC,EAAyC;EACvC,IAAI,CAACA,MAAM,CAACtE,MAAZ,EAAoB,OAAO,EAAP;EACpB,OACG,sBAAqBqE,KAAM,yCAA5B,GACAC,MAAM,CAACd,GAAP,CAAWe,QAAQ,IAAK,OAAMC,MAAM,CAACD,QAAD,CAAW,IAA/C,EAAoDE,IAApD,CAAyD,EAAzD,CAFF;AAID;;AAED,SAASC,mBAAT,CAA6BC,UAA7B,EAAyC;EACvC,IAAI,CAACA,UAAU,CAACC,IAAhB,EAAsB,OAAO,EAAP;EACtB,OACG,sFAAD,GACAC,KAAK,CAACC,IAAN,CAAWH,UAAX,EAAuB/F,IAAI,IAAK,OAAMA,IAAK,IAA3C,EAAgD6F,IAAhD,CAAqD,EAArD,CAFF;AAID;;AAEM,SAASM,sBAAT,CACLC,QADK,EAELC,SAFK,EAGLC,eAHK,EAILC,eAJK,EAKL;EACA,IAAIC,OAAJ;;EACA,MAAMC,MAAM,GAAGnB,OAAO,IAAI;IACxB,MAAMoB,MAAM,GAAGrB,eAAe,CAACC,OAAD,CAA9B;IACA,IAAI,CAACoB,MAAL,EAAa,OAAO,KAAP;IAEb,IAAIC,OAAO,GAAG,KAAd;;IACA,KAAK,MAAMC,QAAX,IAAuBP,SAAvB,EAAkC;MAChC,IAAIK,MAAM,CAACG,IAAP,CAAYD,QAAZ,CAAJ,EAA2B;QACzBD,OAAO,GAAG,IAAV;QACAH,OAAO,CAACzH,GAAR,CAAY6H,QAAZ;;;;IAGJ,OAAO,CAACD,OAAR;GAXF,CAFA;;;EAiBA,MAAMG,OAAO,GAAGN,OAAO,GAAG,IAAI7H,GAAJ,EAA1B;EACA,MAAMoI,aAAa,GAAGd,KAAK,CAACC,IAAN,CAAWI,eAAX,EAA4BG,MAA5B,CAAmCA,MAAnC,CAAtB,CAlBA;;EAqBA,MAAMO,OAAO,GAAGR,OAAO,GAAG,IAAI7H,GAAJ,EAA1B;EACA,MAAMsI,aAAa,GAAGhB,KAAK,CAACC,IAAN,CAAWK,eAAX,EAA4BE,MAA5B,CAAmCA,MAAnC,CAAtB;EAEA,MAAMV,UAAU,GAAGxH,YAAY,CAACuI,OAAD,EAAUE,OAAV,CAA/B;;EAEA,IACEjB,UAAU,CAACC,IAAX,GAAkB,CAAlB,IACAe,aAAa,CAAC3F,MAAd,GAAuB,CADvB,IAEA6F,aAAa,CAAC7F,MAAd,GAAuB,CAHzB,EAIE;IACA,MAAM,IAAI8F,KAAJ,CACH,+BAA8Bd,QAAS,uBAAxC,GACEZ,gBAAgB,CAAC,SAAD,EAAYuB,aAAZ,CADlB,GAEEvB,gBAAgB,CAAC,SAAD,EAAYyB,aAAZ,CAFlB,GAGEnB,mBAAmB,CAACC,UAAD,CAJjB,CAAN;;;EAQF,OAAO;IAAEe,OAAF;IAAWE;GAAlB;AACD;AAEM,SAASG,gCAAT,CACLC,OADK,EAELC,QAFK,EAGsB;EAC3B,MAAM;IAAEC,mBAAmB,GAAG;MAAOF,OAArC;EACA,IAAIE,mBAAmB,KAAK,KAA5B,EAAmC,OAAO,KAAP;EAEnC,MAAMC,MAAM,GAAGF,QAAQ,CAACE,MAAT,CAAgBA,MAAM,IAAIA,MAAJ,oBAAIA,MAAM,CAAEvH,IAAlC,CAAf;EAEA,MAAM;IACJwH,GAAG,GAAG,UADF;IAEJC,MAAM,GAAGF,MAAM,KAAK,qBAAX,GAAmC,OAAnC,GAA6C,QAFlD;IAGJG,GAAG,GAAG;MACJJ,mBAJJ;EAMA,OAAO;IAAEE,GAAF;IAAOC,MAAP;IAAeC;GAAtB;AACD;;AC1FD,aACEC,YADa,IAEV;EACH,SAASC,QAAT,CAAkB5I,MAAlB,EAA0BC,GAA1B,EAA+B6B,SAA/B,EAA0CnB,IAA1C,EAAgD;IAC9C,OAAOgI,YAAY,CAAC;MAAEE,IAAI,EAAE,UAAR;MAAoB7I,MAApB;MAA4BC,GAA5B;MAAiC6B;KAAlC,EAA+CnB,IAA/C,CAAnB;;;EAGF,OAAO;;IAELmI,oBAAoB,CAACnI,IAAD,EAA+B;MACjD,MAAM;QACJI,IAAI,EAAE;UAAEC;SADJ;QAEJH;UACEF,IAHJ;MAIA,IAAIE,KAAK,CAACkI,oBAAN,CAA2B/H,IAA3B,CAAJ,EAAsC;MAEtC2H,YAAY,CAAC;QAAEE,IAAI,EAAE,QAAR;QAAkB7H;OAAnB,EAA2BL,IAA3B,CAAZ;KATG;;IAYLqI,gBAAgB,CAACrI,IAAD,EAAqC;MACnD,MAAMV,GAAG,GAAGkB,UAAU,CAACR,IAAI,CAACc,GAAL,CAAS,UAAT,CAAD,EAAuBd,IAAI,CAACI,IAAL,CAAUK,QAAjC,CAAtB;MACA,IAAI,CAACnB,GAAD,IAAQA,GAAG,KAAK,WAApB,EAAiC;MAEjC,MAAMD,MAAM,GAAGW,IAAI,CAACc,GAAL,CAAS,QAAT,CAAf;;MACA,IAAIzB,MAAM,CAACY,YAAP,EAAJ,EAA2B;QACzB,MAAMqI,OAAO,GAAGjJ,MAAM,CAACa,KAAP,CAAaqI,UAAb,CAAwBlJ,MAAM,CAACe,IAAP,CAAYC,IAApC,CAAhB;QACA,IAAIiI,OAAO,IAAIA,OAAO,CAACtI,IAAR,CAAawI,0BAAb,EAAf,EAA0D;;;MAG5D,MAAM9G,MAAM,GAAGV,aAAa,CAAC3B,MAAD,CAA5B;MACA,OAAO4I,QAAQ,CAACvG,MAAM,CAACR,EAAR,EAAY5B,GAAZ,EAAiBoC,MAAM,CAACP,SAAxB,EAAmCnB,IAAnC,CAAf;KAvBG;;IA0BLyI,aAAa,CAACzI,IAAD,EAAkC;MAC7C,MAAM;QAAE0I,UAAF;QAAc9H;UAAWZ,IAA/B;MACA,IAAIiB,GAAJ,CAF6C;;MAK7C,IAAIyH,UAAU,CAACC,oBAAX,EAAJ,EAAuC;QACrC1H,GAAG,GAAGyH,UAAU,CAAC5H,GAAX,CAAe,MAAf,CAAN,CADqC;OAAvC,MAGO,IAAI4H,UAAU,CAACE,sBAAX,EAAJ,EAAyC;QAC9C3H,GAAG,GAAGyH,UAAU,CAAC5H,GAAX,CAAe,OAAf,CAAN,CAD8C;;OAAzC,MAIA,IAAI4H,UAAU,CAACpH,UAAX,EAAJ,EAA6B;QAClC,MAAMuH,KAAK,GAAGH,UAAU,CAACA,UAAzB;;QACA,IAAIG,KAAK,CAAC/G,gBAAN,MAA4B+G,KAAK,CAACC,eAAN,EAAhC,EAAyD;UACvD,IAAID,KAAK,CAACzI,IAAN,CAAW2B,MAAX,KAAsBnB,MAA1B,EAAkC;YAChCK,GAAG,GAAG4H,KAAK,CAAC/H,GAAN,CAAU,WAAV,EAAuBd,IAAI,CAACV,GAA5B,CAAN;;;;;MAKN,IAAI4B,EAAE,GAAG,IAAT;MACA,IAAIC,SAAS,GAAG,IAAhB;MACA,IAAIF,GAAJ,EAAS,CAAC;QAAEC,EAAF;QAAMC;UAAcH,aAAa,CAACC,GAAD,CAAlC;;MAET,KAAK,MAAM8H,IAAX,IAAmB/I,IAAI,CAACc,GAAL,CAAS,YAAT,CAAnB,EAA2C;QACzC,IAAIiI,IAAI,CAACC,gBAAL,EAAJ,EAA6B;UAC3B,MAAM1J,GAAG,GAAGkB,UAAU,CAACuI,IAAI,CAACjI,GAAL,CAAS,KAAT,CAAD,CAAtB;UACA,IAAIxB,GAAJ,EAAS2I,QAAQ,CAAC/G,EAAD,EAAK5B,GAAL,EAAU6B,SAAV,EAAqB4H,IAArB,CAAR;;;KAtDV;;IA2DLE,gBAAgB,CAACjJ,IAAD,EAAqC;MACnD,IAAIA,IAAI,CAACI,IAAL,CAAU8I,QAAV,KAAuB,IAA3B,EAAiC;MAEjC,MAAMxH,MAAM,GAAGV,aAAa,CAAChB,IAAI,CAACc,GAAL,CAAS,OAAT,CAAD,CAA5B;MACA,MAAMxB,GAAG,GAAGkB,UAAU,CAACR,IAAI,CAACc,GAAL,CAAS,MAAT,CAAD,EAAmB,IAAnB,CAAtB;MAEA,IAAI,CAACxB,GAAL,EAAU;MAEV0I,YAAY,CACV;QACEE,IAAI,EAAE,IADR;QAEE7I,MAAM,EAAEqC,MAAM,CAACR,EAFjB;QAGE5B,GAHF;QAIE6B,SAAS,EAAEO,MAAM,CAACP;OALV,EAOVnB,IAPU,CAAZ;;;GAnEJ;AA8ED,CArFD;;ACAA,aACEgI,YADa,KAET;EACJmB,iBAAiB,CAACnJ,IAAD,EAAsC;IACrD,MAAM0B,MAAM,GAAGH,eAAe,CAACvB,IAAD,CAA9B;IACA,IAAI,CAAC0B,MAAL,EAAa;IACbsG,YAAY,CAAC;MAAEE,IAAI,EAAE,QAAR;MAAkBxG;KAAnB,EAA6B1B,IAA7B,CAAZ;GAJE;;EAMJoJ,OAAO,CAACpJ,IAAD,EAA4B;IACjCA,IAAI,CAACc,GAAL,CAAS,MAAT,EAAiB7B,OAAjB,CAAyBoK,QAAQ,IAAI;MACnC,MAAM3H,MAAM,GAAGC,gBAAgB,CAAC0H,QAAD,CAA/B;MACA,IAAI,CAAC3H,MAAL,EAAa;MACbsG,YAAY,CAAC;QAAEE,IAAI,EAAE,QAAR;QAAkBxG;OAAnB,EAA6B2H,QAA7B,CAAZ;KAHF;;;AAPE,CAFS,CAAf;;ACFA,MAAMC,oBAAoB,GAAGC,UAAU,CAACC,OAAO,CAACC,QAAR,CAAiBrJ,IAAlB,CAAV,IAAqC,GAAlE;;AAGA,MAAMsJ,OAAO,GAAGC,aAAa,CAACC;AAAO;AAAD,CAAWC,IAAjB,CAAsBnH,GAAvB,CAA7B;;;AAEA,SAASoH,SAAT,CAAmBzJ,IAAnB,EAAiC0J,OAAjC,EAAkD;EAChD,IAAIT,oBAAJ,EAA0B;IACxB,OAAOI,OAAO,CACXM,OADI,CACI3J,IADJ,EACU;MACb4J,KAAK,EAAE,CAACF,OAAD;KAFJ,EAIJG,OAJI,CAII,KAJJ,EAIW,GAJX,CAAP;GADF,MAMO;IACL,OAAOC,cAAc,CAACC,IAAf,CAAoB/J,IAApB,EAA0B;MAAE0J;KAA5B,EAAuCG,OAAvC,CAA+C,KAA/C,EAAsD,GAAtD,CAAP;;AAEH;;AAEM,SAASF,OAAT,CACLK,OADK,EAELC,UAFK,EAGLC,eAHK,EAIG;EACR,IAAIA,eAAe,KAAK,KAAxB,EAA+B,OAAOD,UAAP;EAE/B,IAAIP,OAAO,GAAGM,OAAd;;EACA,IAAI,OAAOE,eAAP,KAA2B,QAA/B,EAAyC;IACvCR,OAAO,GAAG/J,IAAI,CAACgK,OAAL,CAAaD,OAAb,EAAsBQ,eAAtB,CAAV;;;EAGF,IAAI;IACF,OAAOT,SAAS,CAACQ,UAAD,EAAaP,OAAb,CAAhB;GADF,CAEE,OAAOS,GAAP,EAAY;IACZ,IAAIA,GAAG,CAACC,IAAJ,KAAa,kBAAjB,EAAqC,MAAMD,GAAN;IAErC,MAAMjL,MAAM,CAACmL,MAAP,CACJ,IAAInD,KAAJ,CAAW,sBAAqB+C,UAAW,kBAAiBD,OAAQ,GAApE,CADI,EAEJ;MACEI,IAAI,EAAE,0BADR;MAEExD,QAAQ,EAAEqD,UAFZ;MAGED;KALE,CAAN;;AASH;AAEM,SAASlL,GAAT,CAAa4K,OAAb,EAA8B1J,IAA9B,EAA4C;EACjD,IAAI;IACFyJ,SAAS,CAACzJ,IAAD,EAAO0J,OAAP,CAAT;IACA,OAAO,IAAP;GAFF,CAGE,MAAM;IACN,OAAO,KAAP;;AAEH;AAEM,SAASY,UAAT,CAAoBC,WAApB,EAA8C;EACnD,IAAIA,WAAW,CAACvE,IAAZ,KAAqB,CAAzB,EAA4B;EAE5B,MAAMwE,IAAI,GAAGvE,KAAK,CAACC,IAAN,CAAWqE,WAAX,EAAwBE,IAAxB,GAA+B5E,IAA/B,CAAoC,GAApC,CAAb;EAEA6E,OAAO,CAACC,IAAR,CACE,iFACE,6CADF,GAEG,wBAAuBH,IAAK,IAF/B,GAGG,cAAaA,IAAK,IAJvB;EAOArB,OAAO,CAACyB,QAAR,GAAmB,CAAnB;AACD;AAED,IAAIC,cAAc,GAAG,IAAIlM,GAAJ,EAArB;AAEA,MAAMmM,2BAA2B,GAAGC,QAAQ,CAAC,MAAM;EACjDT,UAAU,CAACO,cAAD,CAAV;EACAA,cAAc,GAAG,IAAIlM,GAAJ,EAAjB;AACD,CAH2C,EAGzC,GAHyC,CAA5C;AAKO,SAASqM,eAAT,CAAyBT,WAAzB,EAAmD;EACxD,IAAIA,WAAW,CAACvE,IAAZ,KAAqB,CAAzB,EAA4B;EAE5BuE,WAAW,CAAC3L,OAAZ,CAAoBoB,IAAI,IAAI6K,cAAc,CAAC9L,GAAf,CAAmBiB,IAAnB,CAA5B;EACA8K,2BAA2B;AAC5B;;AC3ED,MAAMG,qBAAqB,GAAG,IAAItM,GAAJ,CAAgB,CAC5C,QAD4C,EAE5C,YAF4C,EAG5C,MAH4C,EAI5C,QAJ4C,CAAhB,CAA9B;AAOe,SAASuM,kBAAT,CACb7E,SADa,EAEE;EACf,MAAM;IAAE8E,MAAM,EAAEC,OAAV;IAAmBC,QAAQ,EAAEC,SAA7B;IAAwCC,MAAM,EAAEC;MAAYnF,SAAlE;EAEA,OAAOmD,IAAI,IAAI;IACb,IAAIA,IAAI,CAAC3B,IAAL,KAAc,QAAd,IAA0B2D,OAA1B,IAAqC1M,KAAG,CAAC0M,OAAD,EAAUhC,IAAI,CAACxJ,IAAf,CAA5C,EAAkE;MAChE,OAAO;QAAE6H,IAAI,EAAE,QAAR;QAAkB4D,IAAI,EAAED,OAAO,CAAChC,IAAI,CAACxJ,IAAN,CAA/B;QAA4CA,IAAI,EAAEwJ,IAAI,CAACxJ;OAA9D;;;IAGF,IAAIwJ,IAAI,CAAC3B,IAAL,KAAc,UAAd,IAA4B2B,IAAI,CAAC3B,IAAL,KAAc,IAA9C,EAAoD;MAClD,MAAM;QAAE/G,SAAF;QAAa9B,MAAb;QAAqBC;UAAQuK,IAAnC;;MAEA,IAAIxK,MAAM,IAAI8B,SAAS,KAAK,QAA5B,EAAsC;QACpC,IAAI0K,OAAO,IAAIP,qBAAqB,CAACnM,GAAtB,CAA0BE,MAA1B,CAAX,IAAgDF,KAAG,CAAC0M,OAAD,EAAUvM,GAAV,CAAvD,EAAuE;UACrE,OAAO;YAAE4I,IAAI,EAAE,QAAR;YAAkB4D,IAAI,EAAED,OAAO,CAACvM,GAAD,CAA/B;YAAsCe,IAAI,EAAEf;WAAnD;;;QAGF,IAAImM,OAAO,IAAItM,KAAG,CAACsM,OAAD,EAAUpM,MAAV,CAAd,IAAmCF,KAAG,CAACsM,OAAO,CAACpM,MAAD,CAAR,EAAkBC,GAAlB,CAA1C,EAAkE;UAChE,OAAO;YACL4I,IAAI,EAAE,QADD;YAEL4D,IAAI,EAAEL,OAAO,CAACpM,MAAD,CAAP,CAAgBC,GAAhB,CAFD;YAGLe,IAAI,EAAG,GAAEhB,MAAO,IAAGC,GAAI;WAHzB;;;;MAQJ,IAAIqM,SAAS,IAAIxM,KAAG,CAACwM,SAAD,EAAYrM,GAAZ,CAApB,EAAsC;QACpC,OAAO;UAAE4I,IAAI,EAAE,UAAR;UAAoB4D,IAAI,EAAEH,SAAS,CAACrM,GAAD,CAAnC;UAA0Ce,IAAI,EAAG,GAAEf,GAAI;SAA9D;;;GAvBN;AA2BD;;AC1CD,MAAMyM,UAAU,GAAGC,WAAW,CAACC,OAAZ,IAAuBD,WAA1C;;AA8BA,SAASE,cAAT,CACEzE,OADF,EAEEC,QAFF,EAaE;EACA,MAAM;IACJyE,MADI;IAEJ7G,OAAO,EAAE8G,aAFL;IAGJC,wBAHI;IAIJC,UAJI;IAKJC,KALI;IAMJC,oBANI;IAOJjC,eAPI;IAQJ,GAAGkC;MACDhF,OATJ;;EAWA,IAAIiF,OAAO,CAACjF,OAAD,CAAX,EAAsB;IACpB,MAAM,IAAIF,KAAJ,CACH;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qFATU,CAAN;;;EAaF,IAAIoF,UAAJ;EACA,IAAIR,MAAM,KAAK,cAAf,EAA+BQ,UAAU,GAAG,aAAb,CAA/B,KACK,IAAIR,MAAM,KAAK,cAAf,EAA+BQ,UAAU,GAAG,aAAb,CAA/B,KACA,IAAIR,MAAM,KAAK,YAAf,EAA6BQ,UAAU,GAAG,WAAb,CAA7B,KACA,IAAI,OAAOR,MAAP,KAAkB,QAAtB,EAAgC;IACnC,MAAM,IAAI5E,KAAJ,CAAU,0BAAV,CAAN;GADG,MAEE;IACL,MAAM,IAAIA,KAAJ,CACH,uDAAD,GACG,8BAA6BhC,IAAI,CAACC,SAAL,CAAe2G,MAAf,CAAuB,GAFnD,CAAN;;;EAMF,IAAI,OAAOK,oBAAP,KAAgC,UAApC,EAAgD;IAC9C,IAAI/E,OAAO,CAACN,OAAR,IAAmBM,OAAO,CAACJ,OAA/B,EAAwC;MACtC,MAAM,IAAIE,KAAJ,CACH,wDAAD,GACG,kCAFC,CAAN;;GAFJ,MAOO,IAAIiF,oBAAoB,IAAI,IAA5B,EAAkC;IACvC,MAAM,IAAIjF,KAAJ,CACH,wDAAD,GACG,cAAahC,IAAI,CAACC,SAAL,CAAegH,oBAAf,CAAqC,GAFjD,CAAN;;;EAMF,IACEjC,eAAe,IAAI,IAAnB,IACA,OAAOA,eAAP,KAA2B,SAD3B,IAEA,OAAOA,eAAP,KAA2B,QAH7B,EAIE;IACA,MAAM,IAAIhD,KAAJ,CACH,4DAAD,GACG,cAAahC,IAAI,CAACC,SAAL,CAAe+E,eAAf,CAAgC,GAF5C,CAAN;;;EAMF,IAAIjF,OAAJ;;EAEA;;EAGE8G,aAAa,IACbE,UADA,IAEAD,wBALF,EAME;IACA,MAAMO,UAAU,GACd,OAAOR,aAAP,KAAyB,QAAzB,IAAqC9F,KAAK,CAACuG,OAAN,CAAcT,aAAd,CAArC,GACI;MAAEU,QAAQ,EAAEV;KADhB,GAEIA,aAHN;IAKA9G,OAAO,GAAGyG,UAAU,CAACa,UAAD,EAAa;MAC/BP,wBAD+B;MAE/BC;KAFkB,CAApB;GAZF,MAgBO;IACLhH,OAAO,GAAGoC,QAAQ,CAACpC,OAAT,EAAV;;;EAGF,OAAO;IACL6G,MADK;IAELQ,UAFK;IAGLrH,OAHK;IAILiF,eAAe,EAAEA,eAAF,WAAEA,eAAF,GAAqB,KAJ/B;IAKLiC,oBALK;IAMLD,KAAK,EAAE,CAAC,CAACA,KANJ;IAOLE,eAAe,EAAEA;GAPnB;AASD;;AAED,SAASM,mBAAT,CACEC,OADF,EAEEvF,OAFF,EAGEE,mBAHF,EAIE0C,OAJF,EAKE4C,QALF,EAMEvF,QANF,EAOE;EACA,MAAM;IACJyE,MADI;IAEJQ,UAFI;IAGJrH,OAHI;IAIJiH,KAJI;IAKJC,oBALI;IAMJC,eANI;IAOJlC;MACE2B,cAAc,CAAUzE,OAAV,EAAmBC,QAAnB,CARlB;EAUA,MAAMwF,QAAQ,GAAG/K,iBAAiB,CAChC,IAAIoB,YAAJ,CAAiB+G,UAAU,IACzBO,OAAA,CAAaR,OAAb,EAAsBC,UAAtB,EAAkCC,eAAlC,CADF,CADgC,CAAlC,CAXA;;EAkBA,IAAIpD,OAAJ,EAAaE,OAAb;EACA,IAAI8F,gBAAJ;EACA,IAAIC,cAAJ;EACA,IAAIC,eAAJ;EAEA,MAAMC,SAAS,GAAG,IAAI/I,GAAJ,EAAlB;EAEA,MAAMgJ,GAAgB,GAAG;IACvBC,KAAK,EAAE9F,QADgB;IAEvBwF,QAFuB;IAGvBf,MAAM,EAAE1E,OAAO,CAAC0E,MAHO;IAIvB7G,OAJuB;IAKvBiG,kBALuB;;IAMvBiB,oBAAoB,CAACnM,IAAD,EAAO;MACzB,IAAI+M,cAAc,KAAKhM,SAAvB,EAAkC;QAChC,MAAM,IAAImG,KAAJ,CACH,yBAAwByF,OAAO,CAAC3M,IAAK,aAAtC,GACG,+DAFC,CAAN;;;MAKF,IAAI,CAAC+M,cAAc,CAACjO,GAAf,CAAmBkB,IAAnB,CAAL,EAA+B;QAC7B0K,OAAO,CAACC,IAAR,CACG,yBAAwByC,YAAa,aAAtC,GACG,qBAAoBpN,IAAK,IAF9B;;;MAMF,IAAIgN,eAAe,IAAI,CAACA,eAAe,CAAChN,IAAD,CAAvC,EAA+C,OAAO,KAAP;MAE/C,IAAIqN,YAAY,GAAGC,UAAU,CAACtN,IAAD,EAAOiF,OAAP,EAAgB;QAC3CsI,UAAU,EAAET,gBAD+B;QAE3CU,QAAQ,EAAE1G,OAFiC;QAG3C2G,QAAQ,EAAEzG;OAHiB,CAA7B;;MAMA,IAAImF,oBAAJ,EAA0B;QACxBkB,YAAY,GAAGlB,oBAAoB,CAACnM,IAAD,EAAOqN,YAAP,CAAnC;;QACA,IAAI,OAAOA,YAAP,KAAwB,SAA5B,EAAuC;UACrC,MAAM,IAAInG,KAAJ,CAAW,8CAAX,CAAN;;;;MAIJ,OAAOmG,YAAP;KAnCqB;;IAqCvBnB,KAAK,CAAClM,IAAD,EAAO;MAAA;;MACV4M,QAAQ,GAAGc,KAAX,GAAmB,IAAnB;MAEA,IAAI,CAACxB,KAAD,IAAU,CAAClM,IAAf,EAAqB;MAErB,IAAI4M,QAAQ,GAAGvG,SAAX,CAAqBvH,GAArB,CAAyBsO,YAAzB,CAAJ,EAA4C;MAC5CR,QAAQ,GAAGvG,SAAX,CAAqBtH,GAArB,CAAyBiB,IAAzB;MACA,sCAAA4M,QAAQ,IAAGE,gBAAX,8CAAWA,gBAAX,GAAgCA,gBAAhC;KA5CqB;;IA8CvBa,gBAAgB,CAAC3N,IAAD,EAAO4N,OAAO,GAAG,GAAjB,EAAsB;MACpC,IAAItG,mBAAmB,KAAK,KAA5B,EAAmC;;MACnC,IAAI4C,eAAJ,EAAqB;;;;QAInB;;;MAGF,MAAM2D,GAAG,GAAGD,OAAO,KAAK,GAAZ,GAAkB5N,IAAlB,GAA0B,GAAEA,IAAK,KAAI4N,OAAQ,EAAzD;MAEA,MAAMF,KAAK,GAAGpG,mBAAmB,CAACI,GAApB,GACV,KADU,GAEVoG,QAAQ,CAACb,SAAD,EAAa,GAAEjN,IAAK,OAAMgK,OAAQ,EAAlC,EAAqC,MAC3CQ,GAAA,CAASR,OAAT,EAAkBhK,IAAlB,CADM,CAFZ;;MAMA,IAAI,CAAC0N,KAAL,EAAY;QACVd,QAAQ,GAAGrC,WAAX,CAAuBxL,GAAvB,CAA2B8O,GAA3B;;;;GAhEN;EAqEA,MAAMzH,QAAQ,GAAGuG,OAAO,CAACO,GAAD,EAAMd,eAAN,EAAuBpC,OAAvB,CAAxB;EACA,MAAMoD,YAAY,GAAGhH,QAAQ,CAACpG,IAAT,IAAiB2M,OAAO,CAAC3M,IAA9C;;EAEA,IAAI,OAAOoG,QAAQ,CAACkG,UAAD,CAAf,KAAgC,UAApC,EAAgD;IAC9C,MAAM,IAAIpF,KAAJ,CACH,QAAOkG,YAAa,mCAAkCtB,MAAO,uBAD1D,CAAN;;;EAKF,IAAI7F,KAAK,CAACuG,OAAN,CAAcpG,QAAQ,CAACC,SAAvB,CAAJ,EAAuC;IACrC0G,cAAc,GAAG,IAAIpO,GAAJ,CAAQyH,QAAQ,CAACC,SAAjB,CAAjB;IACA2G,eAAe,GAAG5G,QAAQ,CAAC4G,eAA3B;GAFF,MAGO,IAAI5G,QAAQ,CAACC,SAAb,EAAwB;IAC7B0G,cAAc,GAAG,IAAIpO,GAAJ,CAAQO,MAAM,CAAC6O,IAAP,CAAY3H,QAAQ,CAACC,SAArB,CAAR,CAAjB;IACAyG,gBAAgB,GAAG1G,QAAQ,CAACC,SAA5B;IACA2G,eAAe,GAAG5G,QAAQ,CAAC4G,eAA3B;GAHK,MAIA;IACLD,cAAc,GAAG,IAAIpO,GAAJ,EAAjB;;;EAGF,CAAC;IAAEmI,OAAF;IAAWE;MAAYb,sBAAsB,CAC5CiH,YAD4C,EAE5CL,cAF4C,EAG5CX,eAAe,CAACtF,OAAhB,IAA2B,EAHiB,EAI5CsF,eAAe,CAACpF,OAAhB,IAA2B,EAJiB,CAA9C;EAOA,OAAO;IACLkF,KADK;IAELJ,MAFK;IAGL7G,OAHK;IAILmB,QAJK;IAKLgH,YALK;;IAMLzF,YAAY,CAACqG,OAAD,EAA0BrO,IAA1B,EAA0C;MACpD,MAAMsO,KAAK,GAAGpB,QAAQ,CAAClN,IAAD,CAAtB;MACAyG,QAAQ,CAACkG,UAAD,CAAR,CAAqB0B,OAArB,EAA8BC,KAA9B,EAAqCtO,IAArC;;;GARJ;AAWD;;AAEc,SAASuO,sBAAT,CACbvB,OADa,EAEb;EACA,OAAOwB,OAAO,CAAC,CAAC9G,QAAD,EAAWD,OAAX,EAAmC4C,OAAnC,KAAuD;IACpE3C,QAAQ,CAAC+G,aAAT,CAAuB,CAAvB;IACA,MAAM;MAAEC;QAAahH,QAArB;IAEA,IAAIuF,QAAJ;IAEA,MAAMtF,mBAAmB,GAAGH,gCAAgC,CAC1DC,OAD0D,EAE1DC,QAF0D,CAA5D;IAKA,MAAM;MAAE6E,KAAF;MAASJ,MAAT;MAAiB7G,OAAjB;MAA0BmB,QAA1B;MAAoCgH,YAApC;MAAkDzF;QACtD+E,mBAAmB,CACjBC,OADiB,EAEjBvF,OAFiB,EAGjBE,mBAHiB,EAIjB0C,OAJiB,EAKjB,MAAM4C,QALW,EAMjBvF,QANiB,CADrB;IAUA,MAAMiH,aAAa,GAAGxC,MAAM,KAAK,cAAX,GAA4BjN,KAA5B,GAAsCA,KAA5D;IAEA,MAAM0P,OAAO,GAAGnI,QAAQ,CAACmI,OAAT,GACZF,QAAQ,CAACG,QAAT,CAAkBC,KAAlB,CAAwB,CAACH,aAAa,CAAC3G,YAAD,CAAd,EAA8BvB,QAAQ,CAACmI,OAAvC,CAAxB,CADY,GAEZD,aAAa,CAAC3G,YAAD,CAFjB;;IAIA,IAAIuE,KAAK,IAAIA,KAAK,KAAKnH,0BAAvB,EAAmD;MACjD2F,OAAO,CAAClD,GAAR,CAAa,GAAE4F,YAAa,oBAA5B;MACA1C,OAAO,CAAClD,GAAR,CAAa,oBAAmBxC,yBAAyB,CAACC,OAAD,CAAU,EAAnE;MACAyF,OAAO,CAAClD,GAAR,CAAa,4BAA2BsE,MAAO,YAA/C;;;IAGF,MAAM;MAAE4C;QAAgBtI,QAAxB;IAEA,OAAO;MACLpG,IAAI,EAAE,kBADD;MAELuO,OAFK;;MAILI,GAAG,CAACC,IAAD,EAAO;QAAA;;QACR,IAAIF,WAAJ,EAAiB;UACf,IACEE,IAAI,CAACnO,GAAL,CAAS,0BAAT,KACAmO,IAAI,CAACnO,GAAL,CAAS,0BAAT,MAAyCiO,WAF3C,EAGE;YACAhE,OAAO,CAACC,IAAR,CACG,kCAAD,GACG,KAAIiE,IAAI,CAACnO,GAAL,CAAS,8BAAT,CAAyC,EADhD,GAEG,QAAO2M,YAAa,4BAFvB,GAGG,2CAHH,GAIG,IAAGwB,IAAI,CAACnO,GAAL,CAAS,0BAAT,CAAqC,QAAOiO,WAAY,GAJ9D,GAKG,kCANL;WAJF,MAYO;YACLE,IAAI,CAACxK,GAAL,CAAS,0BAAT,EAAqCsK,WAArC;YACAE,IAAI,CAACxK,GAAL,CAAS,8BAAT,EAAyCgJ,YAAzC;;;;QAIJR,QAAQ,GAAG;UACTvG,SAAS,EAAE,IAAI1H,GAAJ,EADF;UAETmO,gBAAgB,EAAE/L,SAFT;UAGT2M,KAAK,EAAE,KAHE;UAITmB,SAAS,EAAE,IAAIlQ,GAAJ,EAJF;UAKT4L,WAAW,EAAE,IAAI5L,GAAJ;SALf;QAQA,iBAAAyH,QAAQ,CAACuI,GAAT,mCAAcG,KAAd,CAAoB,IAApB,EAA0BnN,SAA1B;OAhCG;;MAkCLoN,IAAI,GAAG;QAAA;;QACL,kBAAA3I,QAAQ,CAAC2I,IAAT,oCAAeD,KAAf,CAAqB,IAArB,EAA2BnN,SAA3B;;QAEA,IAAI2F,mBAAmB,KAAK,KAA5B,EAAmC;UACjC,IAAIA,mBAAmB,CAACE,GAApB,KAA4B,UAAhC,EAA4C;YAC1CgD,UAAA,CAAgBoC,QAAQ,CAACrC,WAAzB;WADF,MAEO;YACLC,eAAA,CAAqBoC,QAAQ,CAACrC,WAA9B;;;;QAIJ,IAAI,CAAC2B,KAAL,EAAY;QAEZ,IAAI,KAAK8C,QAAT,EAAmBtE,OAAO,CAAClD,GAAR,CAAa,MAAK,KAAKwH,QAAS,GAAhC;;QAEnB,IAAIpC,QAAQ,CAACvG,SAAT,CAAmBL,IAAnB,KAA4B,CAAhC,EAAmC;UACjC0E,OAAO,CAAClD,GAAR,CACEsE,MAAM,KAAK,cAAX,GACIc,QAAQ,CAACc,KAAT,GACG,8BAA6BN,YAAa,qCAD7C,GAEG,2BAA0BA,YAAa,+BAH9C,GAIK,uCAAsCA,YAAa,qCAL1D;UAQA;;;QAGF,IAAItB,MAAM,KAAK,cAAf,EAA+B;UAC7BpB,OAAO,CAAClD,GAAR,CACG,OAAM4F,YAAa,yCAApB,GACG,0BAFL;SADF,MAKO;UACL1C,OAAO,CAAClD,GAAR,CACG,OAAM4F,YAAa,0CADtB;;;QAKF,KAAK,MAAMpN,IAAX,IAAmB4M,QAAQ,CAACvG,SAA5B,EAAuC;UAAA;;UACrC,8BAAIuG,QAAQ,CAACE,gBAAb,aAAI,uBAA4B9M,IAA5B,CAAJ,EAAuC;YACrC,MAAMiP,eAAe,GAAGC,mBAAmB,CACzClP,IADyC,EAEzCiF,OAFyC,EAGzC2H,QAAQ,CAACE,gBAHgC,CAA3C;YAMA,MAAMqC,gBAAgB,GAAGjK,IAAI,CAACC,SAAL,CAAe8J,eAAf,EACtBpF,OADsB,CACd,IADc,EACR,IADQ,EAEtBA,OAFsB,CAEd,MAFc,EAEN,KAFM,EAGtBA,OAHsB,CAGd,MAHc,EAGN,KAHM,CAAzB;YAKAa,OAAO,CAAClD,GAAR,CAAa,KAAIxH,IAAK,IAAGmP,gBAAiB,EAA1C;WAZF,MAaO;YACLzE,OAAO,CAAClD,GAAR,CAAa,KAAIxH,IAAK,EAAtB;;;;;KAvFR;GAnCY,CAAd;AAgID;;AAED,SAAS8N,QAAT,CAAkBlJ,GAAlB,EAAuB3F,GAAvB,EAA4BmQ,UAA5B,EAAwC;EACtC,IAAIC,GAAG,GAAGzK,GAAG,CAACnE,GAAJ,CAAQxB,GAAR,CAAV;;EACA,IAAIoQ,GAAG,KAAKtO,SAAZ,EAAuB;IACrBsO,GAAG,GAAGD,UAAU,EAAhB;IACAxK,GAAG,CAACR,GAAJ,CAAQnF,GAAR,EAAaoQ,GAAb;;;EAEF,OAAOA,GAAP;AACD;;AAED,SAAShD,OAAT,CAAiBzL,GAAjB,EAAsB;EACpB,OAAO1B,MAAM,CAAC6O,IAAP,CAAYnN,GAAZ,EAAiBQ,MAAjB,KAA4B,CAAnC;AACD;;;;"}