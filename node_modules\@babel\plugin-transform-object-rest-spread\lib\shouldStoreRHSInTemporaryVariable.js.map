{"version": 3, "names": ["_core", "require", "isObjectProperty", "isArrayPattern", "isObjectPattern", "isAssignmentPattern", "isRestElement", "isIdentifier", "t", "shouldStoreRHSInTemporaryVariable", "node", "nonNullElements", "elements", "filter", "element", "length", "properties", "firstProperty", "value", "left", "argument"], "sources": ["../src/shouldStoreRHSInTemporaryVariable.ts"], "sourcesContent": ["import { types as t } from \"@babel/core\";\n\nconst {\n  isObjectProperty,\n  isArrayPattern,\n  isObjectPattern,\n  isAssignmentPattern,\n  isRestElement,\n  isIdentifier,\n} = t;\n/**\n * This is a helper function to determine if we should create an intermediate variable\n * such that the RHS of an assignment is not duplicated.\n *\n * See https://github.com/babel/babel/pull/13711#issuecomment-914388382 for discussion\n * on further optimizations.\n */\nexport default function shouldStoreRHSInTemporaryVariable(\n  node: t.LVal,\n): boolean {\n  if (isArrayPattern(node)) {\n    const nonNullElements = node.elements.filter(element => element !== null);\n    if (nonNullElements.length > 1) return true;\n    else return shouldStoreRHSInTemporaryVariable(nonNullElements[0]);\n  } else if (isObjectPattern(node)) {\n    const { properties } = node;\n    if (properties.length > 1) return true;\n    else if (properties.length === 0) return false;\n    else {\n      const firstProperty = properties[0];\n      if (isObjectProperty(firstProperty)) {\n        // the value of the property must be an LVal\n        return shouldStoreRHSInTemporaryVariable(firstProperty.value as t.LVal);\n      } else {\n        return shouldStoreRHSInTemporaryVariable(firstProperty);\n      }\n    }\n  } else if (isAssignmentPattern(node)) {\n    return shouldStoreRHSInTemporaryVariable(node.left);\n  } else if (isRestElement(node)) {\n    if (isIdentifier(node.argument)) return true;\n    return shouldStoreRHSInTemporaryVariable(node.argument);\n  } else {\n    // node is Identifier or MemberExpression\n    return false;\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAEA,MAAM;EACJC,gBAAgB;EAChBC,cAAc;EACdC,eAAe;EACfC,mBAAmB;EACnBC,aAAa;EACbC;AACF,CAAC,GAAGC,WAAC;AAQU,SAASC,iCAAiCA,CACvDC,IAAY,EACH;EACT,IAAIP,cAAc,CAACO,IAAI,CAAC,EAAE;IACxB,MAAMC,eAAe,GAAGD,IAAI,CAACE,QAAQ,CAACC,MAAM,CAACC,OAAO,IAAIA,OAAO,KAAK,IAAI,CAAC;IACzE,IAAIH,eAAe,CAACI,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,KACvC,OAAON,iCAAiC,CAACE,eAAe,CAAC,CAAC,CAAC,CAAC;EACnE,CAAC,MAAM,IAAIP,eAAe,CAACM,IAAI,CAAC,EAAE;IAChC,MAAM;MAAEM;IAAW,CAAC,GAAGN,IAAI;IAC3B,IAAIM,UAAU,CAACD,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,KAClC,IAAIC,UAAU,CAACD,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC,KAC1C;MACH,MAAME,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC;MACnC,IAAId,gBAAgB,CAACe,aAAa,CAAC,EAAE;QAEnC,OAAOR,iCAAiC,CAACQ,aAAa,CAACC,KAAe,CAAC;MACzE,CAAC,MAAM;QACL,OAAOT,iCAAiC,CAACQ,aAAa,CAAC;MACzD;IACF;EACF,CAAC,MAAM,IAAIZ,mBAAmB,CAACK,IAAI,CAAC,EAAE;IACpC,OAAOD,iCAAiC,CAACC,IAAI,CAACS,IAAI,CAAC;EACrD,CAAC,MAAM,IAAIb,aAAa,CAACI,IAAI,CAAC,EAAE;IAC9B,IAAIH,YAAY,CAACG,IAAI,CAACU,QAAQ,CAAC,EAAE,OAAO,IAAI;IAC5C,OAAOX,iCAAiC,CAACC,IAAI,CAACU,QAAQ,CAAC;EACzD,CAAC,MAAM;IAEL,OAAO,KAAK;EACd;AACF"}