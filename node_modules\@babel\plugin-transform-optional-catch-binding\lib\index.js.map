{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_pluginSyntaxOptionalCatchBinding", "_default", "declare", "api", "assertVersion", "name", "inherits", "syntaxOptionalCatchBinding", "default", "visitor", "CatchClause", "path", "node", "param", "uid", "scope", "generateUidIdentifier", "<PERSON><PERSON><PERSON><PERSON>", "get", "replaceWith", "exports"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport syntaxOptionalCatchBinding from \"@babel/plugin-syntax-optional-catch-binding\";\n\nexport default declare(api => {\n  api.assertVersion(7);\n\n  return {\n    name: \"transform-optional-catch-binding\",\n    inherits: syntaxOptionalCatchBinding.default,\n\n    visitor: {\n      CatchClause(path) {\n        if (!path.node.param) {\n          const uid = path.scope.generateUidIdentifier(\"unused\");\n          const paramPath = path.get(\"param\");\n          paramPath.replaceWith(uid);\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,iCAAA,GAAAD,OAAA;AAAqF,IAAAE,QAAA,GAEtE,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAC,CAAC,CAAC;EAEpB,OAAO;IACLC,IAAI,EAAE,kCAAkC;IACxCC,QAAQ,EAAEC,iCAA0B,CAACC,OAAO;IAE5CC,OAAO,EAAE;MACPC,WAAWA,CAACC,IAAI,EAAE;QAChB,IAAI,CAACA,IAAI,CAACC,IAAI,CAACC,KAAK,EAAE;UACpB,MAAMC,GAAG,GAAGH,IAAI,CAACI,KAAK,CAACC,qBAAqB,CAAC,QAAQ,CAAC;UACtD,MAAMC,SAAS,GAAGN,IAAI,CAACO,GAAG,CAAC,OAAO,CAAC;UACnCD,SAAS,CAACE,WAAW,CAACL,GAAG,CAAC;QAC5B;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC;AAAAM,OAAA,CAAAZ,OAAA,GAAAP,QAAA"}